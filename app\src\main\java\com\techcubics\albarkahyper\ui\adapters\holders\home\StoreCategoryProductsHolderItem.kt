package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreDetailsMenuData
import com.techcubics.data.model.pojo.StoreDetailsMenuProductData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.databinding.ItemStoreCategoryProductsBinding
import com.techcubics.albarkahyper.ui.adapters.store.ProductsAdapter
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler


class StoreCategoryProductsHolderItem(val binding: ItemStoreCategoryProductsBinding, val context: Context,val margin:Int=0,val onFavClickListener: IFavClickListener,val onClickHandler: IOnAdapterItemClickHandler): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "StoreCategoryProductsHolderItem"
    private val minOrder = 0f
    fun bind(data:StoreDetailsMenuData){

        Helper.loadImage(context,data.image,binding.icon)
        binding.tvTitle.text=data.name

        data.products.forEach {

            it.categoryID=data.id
        }
        val productsAdapter= ProductsAdapter<StoreDetailsMenuProductData>(
            margin = margin,
            onFavClickListener=onFavClickListener,
            onClickHandler,
            false,
            context
        )
        productsAdapter.setItemsList(data.products, minOrder)
        binding.rvProducts.adapter = productsAdapter
        binding.rvProducts.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)


    }



}