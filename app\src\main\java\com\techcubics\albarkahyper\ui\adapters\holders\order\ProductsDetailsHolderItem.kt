package com.techcubics.albarkahyper.ui.adapters.holders.order

import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemOrderBinding
import com.techcubics.albarkahyper.databinding.ItemProductDetailsBinding

class ProductsDetailsHolderItem(itemView: ItemProductDetailsBinding) : RecyclerView.ViewHolder(itemView.root) {
        val productName = itemView.productName
        val productPrice = itemView.productPrice
        val productQty = itemView.productQty
        val productImg = itemView.productImage
    }