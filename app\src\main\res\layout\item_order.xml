<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="12dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="10dp"
            android:layout_margin="5dp"
            android:elevation="10dp">
            <ImageView
                android:id="@+id/image_view"
                android:visibility="gone"
                android:layout_width="@dimen/icons_size_height_weight_12"
                android:layout_height="@dimen/icons_size_height_weight_12"
                android:scaleType="fitXY"
                tools:srcCompat="@tools:sample/avatars"
                />
            <com.denzcoskun.imageslider.ImageSlider
                android:id="@+id/image_slider"
                android:layout_width="@dimen/icons_size_height_weight_12"
                android:layout_height="@dimen/icons_size_height_weight_12"
                app:iss_auto_cycle="true"
                android:visibility="visible"
                android:gravity="center_vertical"
                app:iss_delay="0"
                app:iss_error_image="@drawable/portrait_placeholder"
                app:iss_period="1000"
                app:iss_placeholder="@drawable/portrait_placeholder"
                app:iss_selected_dot="@drawable/default_selected_dot"
                app:iss_unselected_dot="@drawable/default_unselected_dot"
                tools:srcCompat="@tools:sample/avatars"
                />
        </androidx.cardview.widget.CardView>



        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical">
            <TextView
                android:id="@+id/order_no"
                style="@style/label_item_order_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:gravity="start"
                tools:text="1111111111" />
            <com.techcubics.albarkahyper.common.DrawableTopLeftTextView
                android:id="@+id/product_name"
                style="@style/label_item_order_produtname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:textAlignment="viewStart"
                android:maxLines="2"
                tools:text="testtestetst" />
            <com.techcubics.albarkahyper.common.DrawableTopLeftTextView
                android:id="@+id/order_status"
                style="@style/label_item_order_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:maxLines="2"
                android:textAlignment="viewStart"
                android:gravity="start"
                tools:text="testtestetst" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp">
                <com.techcubics.albarkahyper.common.DrawableTopLeftTextView
                    android:id="@+id/amount"
                    style="@style/label_item_order_total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="1200" />
                <com.techcubics.albarkahyper.common.DrawableTopLeftTextView
                    android:id="@+id/currency_name"
                    style="@style/label_item_order_currency"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="5dp"
                    android:textAlignment="viewStart"
                    android:gravity="start"
                    tools:text="@string/currency_name" />
            </LinearLayout>

        </LinearLayout>


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>