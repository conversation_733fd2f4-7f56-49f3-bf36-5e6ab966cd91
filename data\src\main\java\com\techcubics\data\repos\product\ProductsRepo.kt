package com.techcubics.data.repos.product

import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.ItemRemovingRequest
import com.techcubics.data.model.requests.SelectLocationRequest
import com.techcubics.data.model.requests.SubmitCouponRequest
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.remote.BaseResponse

interface ProductsRepo {
    suspend fun removeAllCart(): BaseResponse<CartData>?
    suspend fun getProductsFilter(request: ProductSearchRequest): BaseResponse<List<ProductSearchData>>?
    suspend fun getProductsByCategory(request: ProductByCategoryRequest): BaseResponse<MutableList<ProductDetailsDto>>?
    suspend fun getProductsBySubCategory(request: ProductByCategoryRequest): BaseResponse<MutableList<ProductDetailsDto>>?

    suspend fun getColors(): BaseResponse<MutableList<ColorObject>>?
    suspend fun getColorsSizes(request: ProductColorsSizesRequest): BaseResponse<ProductColorsSizesData>?
    suspend fun addRate(request: AddRateRequest2): BaseResponse<Any>?
    suspend fun getProductDetails(request: ProductDetailsRequest): BaseResponse<ProductData>?
    suspend fun addToCart(
        pathEndPoint: String,
        request: AddCartRequest
    ): BaseResponse<CartData>?
    suspend fun getCart(): BaseResponse<ArrayList<CartData>>?
    suspend fun removeCartItem(

        request: ItemRemovingRequest
    ): BaseResponse<CartData>?
    suspend fun submitCoupon(request: SubmitCouponRequest): BaseResponse<Coupon>?
    suspend fun selectLocation(
       request: SelectLocationRequest
    ): BaseResponse<SelectedLocationData>?

    suspend fun getDiscountDetails(discountId: Int): BaseResponse<Discount>?
    suspend fun getOfferDetails(offerId: String): BaseResponse<Offer>?
    suspend fun getSaveDetails(saveId: String): BaseResponse<Save>?


    suspend fun getBranchTypesCategories(branchTypeID:Int): BaseResponse<MutableList<Category>>?
    suspend fun getCategoriesByShopId(shopId:Int): BaseResponse<MutableList<Category>>?
    suspend fun getSubCategoriesByCatId(catId:Int): BaseResponse<MutableList<SubCategory>>?
    suspend fun getBanners(): BaseResponse<MutableList<BannerData>>?

    suspend fun getMostWantedProducts(store: Int, category_id: Int, page: Int): BaseResponse<MutableList<ProductDetailsDto>>?

    suspend fun getHomeBanner() : BaseResponse<MutableList<BannerData>>?
    suspend fun getStoreDiscounts(store: Int, page: Int): BaseResponse<MutableList<ProductDetailsDto>>?
}