package com.techcubics.albarkahyper.di

import android.content.Context
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.chat.viewmodels.ChatViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.viewmodels.FavoritesViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.home.viewmodels.HomeFragmentViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.orders.viewmodels.OrdersViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.ProductDetailsViewModel
import com.techcubics.albarkahyper.ui.views.stores.StoresViewModel
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.auth.AuthRepo
import com.techcubics.data.repos.auth.AuthRepoImpl
import com.techcubics.data.repos.chat.*
import com.techcubics.data.repos.home.HomeRepo
import com.techcubics.data.repos.home.HomeRepoImpl
import com.techcubics.data.repos.home.favourite.FavoritesRepo
import com.techcubics.data.repos.home.favourite.FavoritesRepoImpl
import com.techcubics.data.repos.home.order.OrderRepo
import com.techcubics.data.repos.home.order.OrderRepoImpl
import com.techcubics.data.repos.home.profile.ProfileRepo
import com.techcubics.data.repos.home.profile.ProfileRepoImpl
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.data.repos.product.ProductsRepoImpl
import com.techcubics.data.repos.store.StoresRepo
import com.techcubics.data.repos.store.StoresRepoImpl
import io.reactivex.rxjava3.schedulers.Schedulers
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module


val viewModelModule = module {
    viewModel{ AuthViewModel(get())}
    viewModel { HomeFragmentViewModel(get(),get(),get(),get()) }
    viewModel { OrdersViewModel(get()) }
    viewModel { ProfileViewModel(get()) }
    viewModel{ProductDetailsViewModel(get(),get(),get())}
    viewModel{CartFragmentViewModel(get(),get())}
    viewModel{MainViewModel()}
    viewModel{ ChatViewModel(get(),get(),get(),get()) }
    viewModel{ FavoritesViewModel(get(),get(),get()) }
    viewModel{ ProductsViewModel(get(),get(),get(),get()) }
    viewModel{ StoresViewModel(get(),get(),get(),get()) }
    viewModel{ SectionsViewModel(get(),get(),get()) }
}

val repositoryModule = module {
    single<AuthRepo> { AuthRepoImpl(get(),get()) }
    single<HomeRepo> { HomeRepoImpl(get()) }
    single<StoresRepo> { StoresRepoImpl(get()) }
    single<ProductsRepo> { ProductsRepoImpl(get()) }
    single<OrderRepo> { OrderRepoImpl(get()) }
    single<ProfileRepo> { ProfileRepoImpl(get())  }
    single<OrderChatRepo> { OrderChatRepoImpl(get()) }
    single<LiveChatRepo> { LiveChatRepoImpl(get()) }
    single<SupportChatRepo> { SupportChatRepoImpl(get()) }
    single<FavoritesRepo> { FavoritesRepoImpl(get()) }


}
val networkModule = module {
    single { RetrofitBuilder(get()) }
}

val sharedPreferenceModule = module {
    single { androidContext().getSharedPreferences("markiato", Context.MODE_PRIVATE) }
    single {
        SharedPreferencesManager(get())
    }
}