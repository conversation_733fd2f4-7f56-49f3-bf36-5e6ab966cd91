# AlBarkaHyper APK Builder - PowerShell Version
Write-Host "==========================================" -ForegroundColor Green
Write-Host "    AlBarkaHyper APK Builder" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

# Set Java path (try common locations)
$javaPaths = @(
    "C:\Program Files\Java\jdk-17",
    "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
    "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
    "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
    "C:\Program Files\OpenJDK\jdk-17"
)

$javaHome = $null
foreach ($path in $javaPaths) {
    if (Test-Path $path) {
        $javaHome = $path
        break
    }
}

# Set Android SDK path (try common locations)
$androidPaths = @(
    "$env:LOCALAPPDATA\Android\Sdk",
    "C:\Android\Sdk",
    "C:\Users\<USER>\Android\Sdk"
)

$androidHome = $null
foreach ($path in $androidPaths) {
    if (Test-Path $path) {
        $androidHome = $path
        break
    }
}

# Set environment variables
if ($javaHome) {
    $env:JAVA_HOME = $javaHome
    $env:PATH = "$javaHome\bin;$env:PATH"
}

if ($androidHome) {
    $env:ANDROID_HOME = $androidHome
    $env:PATH = "$androidHome\tools;$androidHome\platform-tools;$env:PATH"
}

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host "Java Home: $javaHome" -ForegroundColor Yellow
Write-Host "Android Home: $androidHome" -ForegroundColor Yellow
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "gradlew.bat")) {
    Write-Host "ERROR: gradlew.bat not found!" -ForegroundColor Red
    Write-Host "Please make sure you're running this from the project root directory." -ForegroundColor Red
    Write-Host "Expected location: AlbarkaHyper-android-development folder" -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "app\build.gradle")) {
    Write-Host "ERROR: app\build.gradle not found!" -ForegroundColor Red
    Write-Host "This doesn't appear to be an Android project directory." -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Project files found" -ForegroundColor Green
Write-Host ""

# Test Java
Write-Host "Testing Java installation..." -ForegroundColor Cyan
try {
    $javaVersion = & java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Java is working" -ForegroundColor Green
    } else {
        throw "Java command failed"
    }
} catch {
    Write-Host "ERROR: Java not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Java JDK 17 from: https://adoptium.net/" -ForegroundColor Yellow
    Write-Host "After installation, restart this script." -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Test Gradle wrapper
Write-Host "Testing Gradle wrapper..." -ForegroundColor Cyan
try {
    & .\gradlew.bat --version | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Gradle wrapper is working" -ForegroundColor Green
    } else {
        throw "Gradle wrapper failed"
    }
} catch {
    Write-Host "ERROR: Gradle wrapper failed!" -ForegroundColor Red
    Write-Host "This might be due to missing Android SDK or other dependencies." -ForegroundColor Red
    Write-Host ""
    Write-Host "Trying to download Gradle wrapper..." -ForegroundColor Yellow
    & .\gradlew.bat wrapper --gradle-version=8.10.2
}
Write-Host ""

Write-Host "==========================================" -ForegroundColor Green
Write-Host "    Starting Build Process" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

# Step 1: Clean
Write-Host "Step 1/3: Cleaning project..." -ForegroundColor Cyan
try {
    & .\gradlew.bat clean --no-daemon --stacktrace
    if ($LASTEXITCODE -ne 0) {
        throw "Clean failed"
    }
    Write-Host "✓ Clean completed" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "ERROR: Clean failed!" -ForegroundColor Red
    Write-Host "Check the error messages above." -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Step 2: Build APK
Write-Host "Step 2/3: Building APK..." -ForegroundColor Cyan
try {
    & .\gradlew.bat assembleRelease --no-daemon --stacktrace
    if ($LASTEXITCODE -ne 0) {
        throw "APK build failed"
    }
    Write-Host "✓ APK build completed" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "ERROR: APK build failed!" -ForegroundColor Red
    Write-Host "Check the error messages above." -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Step 3: Build AAB
Write-Host "Step 3/3: Building AAB (Android App Bundle)..." -ForegroundColor Cyan
try {
    & .\gradlew.bat bundleRelease --no-daemon --stacktrace
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "WARNING: AAB build failed!" -ForegroundColor Yellow
        Write-Host "But APK was built successfully." -ForegroundColor Yellow
        Write-Host "You can use the APK file for now." -ForegroundColor Yellow
        Write-Host ""
    } else {
        Write-Host "✓ AAB build completed" -ForegroundColor Green
        Write-Host ""
    }
} catch {
    Write-Host ""
    Write-Host "WARNING: AAB build failed!" -ForegroundColor Yellow
    Write-Host "But APK was built successfully." -ForegroundColor Yellow
    Write-Host "You can use the APK file for now." -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "==========================================" -ForegroundColor Green
Write-Host "    Build Completed!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

# Check and display results
Write-Host "Checking output files..." -ForegroundColor Cyan
Write-Host ""

$apkPath = "app\build\outputs\apk\release\app-release.apk"
$aabPath = "app\build\outputs\bundle\release\app-release.aab"

if (Test-Path $apkPath) {
    $apkSize = (Get-Item $apkPath).Length
    Write-Host "✓ SUCCESS: APK file created!" -ForegroundColor Green
    Write-Host "  Location: $(Get-Location)\$apkPath" -ForegroundColor White
    Write-Host "  Size: $apkSize bytes" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "✗ ERROR: APK file was not created!" -ForegroundColor Red
    Write-Host ""
}

if (Test-Path $aabPath) {
    $aabSize = (Get-Item $aabPath).Length
    Write-Host "✓ SUCCESS: AAB file created!" -ForegroundColor Green
    Write-Host "  Location: $(Get-Location)\$aabPath" -ForegroundColor White
    Write-Host "  Size: $aabSize bytes" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "✗ WARNING: AAB file was not created (but APK should work)" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "==========================================" -ForegroundColor Green
Write-Host "    Next Steps" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""
Write-Host "1. Test the APK on an Android device" -ForegroundColor White
Write-Host "2. Check the google-play-assets folder for store materials" -ForegroundColor White
Write-Host "3. Upload to Google Play Store" -ForegroundColor White
Write-Host ""
Write-Host "Full paths:" -ForegroundColor Yellow
Write-Host "APK: $(Get-Location)\$apkPath" -ForegroundColor White
Write-Host "AAB: $(Get-Location)\$aabPath" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
