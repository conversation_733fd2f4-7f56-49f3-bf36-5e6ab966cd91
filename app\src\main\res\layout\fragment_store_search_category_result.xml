<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.views.stores.searchResult.StoreSearchCategoryResultFragment">

    <include

        android:id="@+id/toolbarSearchByCategory"
        layout="@layout/toolbar_fragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/linearLayoutCompat2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <include
                android:id="@+id/searchToolbar"
                layout="@layout/toolbar_product_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/rvBanners"
                tools:itemCount="1"
                android:layout_marginTop="16dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                tools:listitem="@layout/item_slider"/>
            <ru.tinkoff.scrollingpagerindicator.ScrollingPagerIndicator
                android:id="@+id/indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:spi_dotColor="@color/color_gray_1"
                app:spi_dotSelectedColor="@color/app_color"
                app:spi_dotSelectedSize="12dp"
                app:spi_dotSize="12dp"
                app:spi_visibleDotCount="3"
                app:spi_visibleDotThreshold="0"
                android:layout_gravity="center"
                android:layout_marginTop="8dp"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvStores"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    tools:listitem="@layout/item_store"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/pagingLoadingImg"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_gravity="bottom"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/lottie_pager_loading"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/rvStores"/>

                <include
                    android:id="@+id/placeholder"
                    layout="@layout/include_placeholder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginTop="50dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />
                <include
                    android:id="@+id/page_loading_animation"
                    layout="@layout/include_page_loading_animation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginTop="50dp"
                    android:visibility="gone"
                    android:layout_centerInParent="true" />
            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.appcompat.widget.LinearLayoutCompat>



    </androidx.core.widget.NestedScrollView>






</androidx.appcompat.widget.LinearLayoutCompat>