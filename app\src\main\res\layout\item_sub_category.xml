<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/icons_size_height_weight_3"
    android:layout_height="@dimen/icons_size_height_weight_11"
    android:layout_margin="4dp"
    android:clickable="true"
    android:background="?attr/selectableItemBackground">



    <androidx.cardview.widget.CardView
        android:id="@+id/imgThumbContainer"
        android:layout_width="@dimen/icons_size_height_weight_5"
        android:layout_height="@dimen/icons_size_height_weight_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:cardCornerRadius="100dp"
        app:cardElevation="0.5dp"
        tools:srcCompat="@tools:sample/avatars">


        <ImageView
            android:id="@+id/icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:scaleType="centerCrop"
            tools:src="@drawable/portrait_placeholder" />


    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/sub_category_title"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imgThumbContainer"
        android:gravity="center"
        tools:text="TestTestTestTest" />
</androidx.constraintlayout.widget.ConstraintLayout>