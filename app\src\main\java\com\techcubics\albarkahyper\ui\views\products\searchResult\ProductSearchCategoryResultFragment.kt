package com.techcubics.albarkahyper.ui.views.products.searchResult


import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Html
import android.text.Spanned
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.MutableLiveData
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.*
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentProductSearchCategoryResultBinding
import com.techcubics.albarkahyper.ui.adapters.product.SubCategoryAdatper
import com.techcubics.albarkahyper.ui.adapters.store.ProductsAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.CartData
import com.techcubics.data.model.pojo.ProductDetailsDto
import com.techcubics.data.model.pojo.SubCategory
import com.techcubics.data.remote.BaseResponse
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.style.R.string
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class ProductSearchCategoryResultFragment : Fragment(),
    IPageRowset<ProductDetailsDto>, IFavClickListener, IOnAdapterItemClickHandler  , ViewTreeObserver.OnScrollChangedListener {


    private var isMainCategory: Boolean =true
    private var _binding: FragmentProductSearchCategoryResultBinding? = null
    private val binding get() = _binding!!
    private lateinit var subCategoryAdapter: SubCategoryAdatper
    private lateinit var productsdapter: ProductsAdapter<ProductDetailsDto>
    private val productsFragmentViewModel: ProductsViewModel by viewModel()
    private val mainViewModel by activityViewModels<MainViewModel>()
    private lateinit var resultList: ArrayList<ProductDetailsDto>
    private lateinit var mostWantedResultList: ArrayList<ProductDetailsDto>
    lateinit var mostWantedAdapter: ProductsAdapter<ProductDetailsDto>
//    private lateinit var bannersAdapter: BannersAdaper

    private lateinit var productOnClickHandler: IOnAdapterItemClickHandler
    private lateinit var mostWantedOnClickHandler: IOnAdapterItemClickHandler

    private lateinit var productFavHandler: IFavClickListener
    private lateinit var mostWantedFavHandler: IFavClickListener
    private var adapterFavType = 1

    //    private var adapterCartType = 1
    private var catId: Int? = null
    private var subCatId: Int? = null
    private var branch_type_id: Int? = null
    private var shop_id: Int? = null
    private lateinit var name: String
    private var _operation: Int = -1
    private var _position: Int? = null

    //    private lateinit var productSearchProgressButton: CircleProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    private val TAG = "ProductSearchCategoryResultFragment"
    private var bannersSlider: Slider? = null
    private var totalPrice = 0f
    private lateinit var rvMostWantedScrollListener:RecyclerView.OnScrollListener


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Log.i("conflictContext", "FragmentProductSearchCategoryResult")
        if (_binding == null) {
            _binding =
                FragmentProductSearchCategoryResultBinding.inflate(inflater, container, false)
            bottomSheetAlertDialog = BottomSheetAlertDialog()
            bottomSheetAlertDialog.init(requireContext())

            init()
        }
        observers()

        events()

        return binding.root
    }
    override fun onScrollChanged() {
        val view = binding.nestedScrollView.getChildAt(binding.nestedScrollView.childCount - 1)
        val bottomDetector: Int = view.bottom - (binding.nestedScrollView.height + binding.nestedScrollView.scrollY)
        if (bottomDetector == 0) {
            if (productsFragmentViewModel.productsCategoriesResponse.value?.pagingator?.hasMorePages==true){
                binding.pagingLoadingImg.visibility = View.VISIBLE
                val page = (productsFragmentViewModel.productsCategoriesResponse.value?.pagingator?.currentPage?:0 )+ 1
                Log.d(TAG, "getProductsByCategory scroll")
                if (isMainCategory) {
                    productsFragmentViewModel.getProductsByCategory(
                        catId!!,
                        branch_type_id!!,
                        shop_id!!,
                        page
                    )
                }else {
                    productsFragmentViewModel.getProductsBySubCategory(
                        subCatId!!,
                        branch_type_id!!,
                        shop_id!!,
                        page
                    )
                }
            }
        }
    }
    override fun init() {
//        productsFragmentViewModel.getCart()
        binding.nestedScrollView.viewTreeObserver.addOnScrollChangedListener(this)
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        resultList = ArrayList<ProductDetailsDto>()
        mostWantedResultList = ArrayList<ProductDetailsDto>()
        startLoading()
        ////////////////////////////////////

        arguments?.let {
            catId = it.getInt(Constants.INTENT_ID, -1)
            name = it.getString(Constants.INTENT_NAME, "")
            shop_id = it.getInt(Constants.INTENT_PLACE_ID, -1)
            branch_type_id = it.getInt(Constants.INTENT_TYPE_ID, -1)
            minOrder = it.getFloat(Constants.MIN_ORDER, 0f)
            binding.toolbarSearchByCategory.tvTitle.text = name
            //
            binding.tvCurrentSection.text = getString(string.all_products)
            calcPrice(0f, 0f)

        }
        setSubCategoriesAdapter()

        catId?.let { productsFragmentViewModel.getSubCategoriesByCatId(it) }

        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        if (isMainCategory) {
            productsFragmentViewModel.getProductsByCategory(catId!!, branch_type_id!!, shop_id!!, 1)
        } else {
            productsFragmentViewModel.getProductsBySubCategory(
                subCatId!!, branch_type_id!!, shop_id!!, 1
            )
        }
        productsFragmentViewModel.getMostWantedProducts(shop_id!!, this.catId!!, 1)


    }

    override fun onResume() {
        super.onResume()
        productsFragmentViewModel.getCart()
        Log.d(TAG, "getProductsByCategory start")

    }
    fun startLoading(){
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)

        binding.toolbarSearchByCategory.btnSearch.visibility = View.VISIBLE
        binding.toolbarSearchByCategory.btnCart.visibility = View.VISIBLE

        showHidePlaceHolder(show = false, type = null, message = null)
//        binding.rvProducts.visibility = View.GONE
//        binding.tvCurrentSection.visibility = View.GONE
//        binding.rvSubCategories.visibility = View.GONE
        binding.nestedScrollView.visibility = View.GONE
//        binding.includeMostSales.apply {
//            rvMostWantedProducts.visibility = View.GONE
//            imageView3.visibility = View.GONE
//            textView.visibility = View.GONE
//        }

//        binding.includeQtyCartInfo.root.visibility = View.GONE



    }
    fun finishLoading() {

//
//        productsFragmentViewModel.productsCategoriesResponse.let {
//            if(it.value!=null){
//                if(it.value?.data!=null){
//                    if(it.value?.data?.size!!>0) {
//                        binding.rvProducts.visibility = View.VISIBLE
//                        binding.tvCurrentSection.visibility = View.VISIBLE
//                        binding.rvSubCategories.visibility = View.VISIBLE
//                    }
//                }
//
//            }
//        }
//
//
//        productsFragmentViewModel.mostWantedProductsResponse.let {
//            if(it.value!=null){
//                if(it.value?.data!=null){
//                    if (it.value?.data?.size!! > 0) {
//                        binding.includeMostSales.apply {
//                            rvMostWantedProducts.visibility = View.VISIBLE
//                            imageView3.visibility = View.VISIBLE
//                            textView.visibility = View.VISIBLE
//                        }
//                    }
//                }
//
//            }
//        }
//
//        productsFragmentViewModel.getCartResponse.let {
//            if(it.value!=null){
//                binding.includeQtyCartInfo.root.visibility = View.VISIBLE
//            }
//        }


    }
    override fun observers() {
        mainViewModel.updateProductLiveData.observe(viewLifecycleOwner){
            if (it!=null){
                productsFragmentViewModel.updateProduct(it)
                mainViewModel.removeUpdateProductObserver()
            }
        }
        productsFragmentViewModel.subCategoriesResponse.observe(viewLifecycleOwner) {
            if (it.status==true){
                it.data?.let { subs -> showSubCategories(subs) }
            }else{
                it?.message?.let { msg -> bottomSheetAlertDialog.showDialog(msg) }
            }
        }
        productsFragmentViewModel.productsCategoriesResponse.observe(viewLifecycleOwner) {
            binding.nestedScrollView.visibility = View.VISIBLE
            binding.pagingLoadingImg.visibility = View.GONE
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            Log.d(TAG, "observers: productsCategoriesResponse")
            try {
                if (it?.status!!) {

                    if (it.data != null) {

//                        if (it.data?.category?.subcategories!!.size > 0) {
//                            showSubCategories(it.data?.category?.subcategories!!)
//                        }
                        if (it.data?.size!! > 0) {
                            binding.rvProducts.visibility = View.VISIBLE
                            binding.tvCurrentSection.visibility = View.VISIBLE
                            showData(it.data!!)
                            showHidePlaceHolder(show = false, type = null, message = null)

                        } else {
                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(string.message_empty_list_general)
                            )
                            binding.rvProducts.visibility = View.GONE
                            binding.tvCurrentSection.visibility = View.GONE
                        }


                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true, type = LottieIconEnum.Empty, message = it.message
                        )
                        binding.rvProducts.visibility = View.GONE
                        binding.tvCurrentSection.visibility = View.GONE
                    }

                } else {
                    //error
                    showHidePlaceHolder(
                        show = true, type = LottieIconEnum.Error, message = it?.message
                    )
                    binding.rvProducts.visibility = View.GONE
                    binding.tvCurrentSection.visibility = View.GONE
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
                binding.rvProducts.visibility = View.GONE
                binding.tvCurrentSection.visibility = View.GONE
            }
        }
        productsFragmentViewModel.mostWantedProductsResponse.observe(viewLifecycleOwner) {
            binding.includeMostSales.horizontalLoading.visibility = View.GONE
//            finishLoading()
            Log.d(TAG, "observers: 1")
            try {
                if (it?.status!!) {

                    if (!it.data.isNullOrEmpty()) {

                            binding.includeMostSales.root.visibility = View.VISIBLE
                            showMostWantedProducts(it.data!!)

                    } else {
                        //empty
//                        showHidePlaceHolder(
//                            show = true, type = LottieIconEnum.Empty, message = it.message
//                        )

                        binding.includeMostSales.root.visibility = View.GONE

                    }

                } else {
                    Log.d(TAG, "observers: 6")
                    //error
//                    showHidePlaceHolder(
//                        show = true, type = LottieIconEnum.Error, message = it.message
//                    )
                    binding.includeMostSales.root.visibility = View.GONE

                }

            } catch (ex: Exception) {
                Log.d(TAG, "observers: 7")
//error
//                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
                binding.includeMostSales.root.visibility = View.GONE

            }


        }
        productsFragmentViewModel.getCartResponse.observe(viewLifecycleOwner) { cartRes ->
            finishLoading()

            if (cartRes != null) {
                if (cartRes.status == true && cartRes.data != null && cartRes.data?.size!! > 0) {
                    totalPrice = cartRes.data!![0].totalPrice!!
                    calcPrice(minOrder, totalPrice)
                }
                productsFragmentViewModel.getCartResponse.value = null
            }

        }


        productsFragmentViewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner) {


            try {

                if (it.status!!) {

                    if (adapterFavType == 1) {
                        when (_operation) {
                            1 -> mostWantedResultList[_position!!].isFav = false
                            2 -> mostWantedResultList[_position!!].isFav = true
                        }
                    } else {
                        when (_operation) {
                            1 -> resultList[_position!!].isFav = false
                            2 -> resultList[_position!!].isFav = true
                        }
                    }
                    // productsAdapter.notifyDataSetChanged()

                } else {
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    binding.tvCurrentSection.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                binding.tvCurrentSection.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }


        }

        /* productsFragmentViewModel.addCartResponse.observe(viewLifecycleOwner) {
             if (it != null) {
                 if (it.message.toString() != Constants.SERVER_ERROR) {

                     if (it.status == true) {
                         updateProductItem(it.data)
                         it.data?.totalPrice?.let { p ->
                             totalPrice = p
                             calcPrice(minOrder, totalPrice)
                         }
                     } else {
                         calcPrice(0f, 0f)
                         if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                             CoroutineScope(Dispatchers.Main).launch {
                                 popupDialog.showSessionExpiredDialog(requireContext())
                                 delay(1200)
                                 popupDialog.onDismiss()
                                 findNavController().navigate(R.id.go_to_login)
                             }
                         } else {
                             bottomSheetAlertDialog.showDialog(it.message.toString())
                         }
                     }
                 } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                     calcPrice(0f, 0f)
                     Helper.ShowErrorDialog(
                         requireContext(),
                         getString(com.techcubics.style.R.string.server_error)
                     )
                 }
                 //refresh(resultList, itemPosition)
                 productsFragmentViewModel.addCartResponse.value = null
             }
         }*/

        /*productsFragmentViewModel.cartItemRemovedResponse.observe(context as LifecycleOwner) { cart ->
            if (cart != null) {
                if (!cart.status!!) {
                    bottomSheetAlertDialog.showDialog(cart.message.toString())
                } else {
                    totalPrice = cart.data?.totalPrice ?: 0f
                    resultList[itemPosition].qtyCart = "0"
                }
                //  refresh(resultList, itemPosition)
                productsFragmentViewModel.cartItemRemovedResponse.value = null
            }
        }*/



    }



    override fun events() {
        productOnClickHandler = object : IOnAdapterItemClickHandler {
            private var qty = 0
            private var maxQty = 0
            private var itemPrice = 0f
            private var itemPosition = -1

            override fun onItemClicked(itemId: Int?, type: String) {
                <EMAIL> =
                    resultList.indexOf(resultList.find { p -> p.id == itemId })
                <EMAIL>(itemId, type)
            }

            override fun removeItemFromCart(
                modelType: String?,
                modelId: Int?,
                productPrice: Float,
                itemPosition: Int
            ) {
                this.itemPosition = itemPosition
                var p = ((totalPrice * 100) - (qty * (productPrice * 100))) / 100
                totalPrice = p
                if (SharedPreferencesManager.isLoggedIn() == "true") {
                    calcPrice(minOrder, totalPrice)
                    val removeItem = productsFragmentViewModel.removeCartItemForSectionProducts(
                        modelType,
                        modelId
                    )
                    removeItem.observe(viewLifecycleOwner) { cart ->
                        if (cart != null) {
                            if (!cart.status!!) {
                                p = ((totalPrice * 100) + (qty * (productPrice * 100))) / 100
                                totalPrice = p
                                bottomSheetAlertDialog.showDialog(cart.message.toString())
                            } else {
                                totalPrice = cart.data?.totalPrice ?: 0f
                                if (resultList[itemPosition].isDiscount==true){
                                    resultList[itemPosition].discount?.get(0)?.quantityCart = 0
                                }else{
                                    resultList[itemPosition].qtyCart = 0
                                }
                            }
                            productsdapter.items = resultList
                            productsdapter.notifyItemChanged(itemPosition)
                            removeItem.value = null
                        }
                        calcPrice(minOrder, totalPrice)
                    }

                } else {
                    findNavController().navigate(R.id.go_to_login)
                }

            }

            fun addToCartResponseHandle(
                cartLiveData: MutableLiveData<BaseResponse<CartData>>,
                offset: Int
            ) {
                cartLiveData.observe(viewLifecycleOwner) {
                    if (it != null) {
                        val p = ((totalPrice * 100) - (offset * (itemPrice * 100))) / 100
                        totalPrice = p
                        if (it.message.toString() != Constants.SERVER_ERROR) {
                            if (it.status == true) {
                                totalPrice = it.data?.totalPrice ?: 0f
                            } else {
                                if (it.message!!.contains(getString(string.unauthenticated))) {
                                    CoroutineScope(Dispatchers.Main).launch {
                                        popupDialog.showSessionExpiredDialog(requireContext())
                                        delay(1200)
                                        popupDialog.onDismiss()
                                        findNavController().navigate(R.id.go_to_login)
                                    }
                                } else {
                                    productsdapter.notifyItemChanged(itemPosition)
                                    bottomSheetAlertDialog.showDialog(it.message.toString())
                                }
                            }
                        } else if (it.message.toString().contains(Constants.SERVER_ERROR)) {
                            Helper.ShowErrorDialog(
                                requireContext(),
                                getString(string.server_error)
                            )
                        }
                        calcPrice(minOrder, totalPrice)
                        cartLiveData.value = null
                    }
                }
            }

            override fun addToCart(
                pathEndPoint: String, shopId: Int?, modelType: String?,
                modelId: Int?,
                qty: Int?,
                itemPrice: Float,
                maxQty: Int,
                minQty: Int,
                minOrdern: Float,
                itemPosition: Int
            ) {
                this.qty = qty ?: 0
                this.itemPrice = itemPrice
                this.maxQty = maxQty
                this.itemPosition = itemPosition
                Log.d(TAG, "addToCart: ${itemPosition}")
                if (SharedPreferencesManager.isLoggedIn() == "true") {
                    val offset = if (qty == minQty) qty else 1
                    val p = ((totalPrice * 100) + (offset * (itemPrice * 100))) / 100
                    totalPrice = p
                    calcPrice(minOrder, totalPrice)
                    val cartLiveData = productsFragmentViewModel
                        .addToCartForSectionProducts(
                            pathEndPoint,
                            shop_id,
                            modelType,
                            modelId,
                            qty
                        )
                    addToCartResponseHandle(cartLiveData, offset)
                } else {
                    findNavController().navigate(R.id.go_to_login)
                }
            }
        }
        mostWantedOnClickHandler = object : IOnAdapterItemClickHandler {
            private var qty = 0
            private var maxQty = 0
            private var itemPrice = 0f
            private var itemPosition = -1

            override fun onItemClicked(itemId: Int?, type: String) {
                <EMAIL> =
                    mostWantedResultList.indexOf(mostWantedResultList.find { p -> p.id == itemId })
                <EMAIL>(itemId, type)
            }

            override fun removeItemFromCart(
                modelType: String?,
                modelId: Int?,
                productPrice: Float,
                itemPosition: Int
            ) {
                this.itemPosition = itemPosition
                var p = ((totalPrice * 100) - (qty * (productPrice * 100))) / 100
                totalPrice = p
                if (SharedPreferencesManager.isLoggedIn() == "true") {
                    calcPrice(minOrder, totalPrice)
                    val removeItem = productsFragmentViewModel.removeCartItemForSectionProducts(
                        modelType,
                        modelId
                    )
                    removeItem.observe(viewLifecycleOwner) { cart ->
                        if (cart != null) {
                            if (!cart.status!!) {
                                p = ((totalPrice * 100) + (qty * (productPrice * 100))) / 100
                                totalPrice = p
                                bottomSheetAlertDialog.showDialog(cart.message.toString())
                            } else {
                                totalPrice = cart.data?.totalPrice ?: 0f
                                if (mostWantedResultList[itemPosition].isDiscount==true){
                                    mostWantedResultList[itemPosition].discount?.get(0)?.quantityCart = 0
                                }else{
                                    mostWantedResultList[itemPosition].qtyCart = 0
                                }
                            }
                            mostWantedAdapter.items = mostWantedResultList
                            mostWantedAdapter.notifyItemChanged(itemPosition)
                            removeItem.value = null
                        }
                        calcPrice(minOrder, totalPrice)
                    }

                } else {
                    findNavController().navigate(R.id.go_to_login)
                }

            }

            fun addToCartResponseHandle(
                cartLiveData: MutableLiveData<BaseResponse<CartData>>,
                offset: Int
            ) {
                cartLiveData.observe(viewLifecycleOwner) {
                    if (it != null) {
                        val p = ((totalPrice * 100) - (offset * (itemPrice * 100))) / 100
                        totalPrice = p
                        if (it.message.toString() != Constants.SERVER_ERROR) {
                            if (it.status == true) {
                                totalPrice = it.data?.totalPrice ?: 0f
                            } else {
                                if (it.message!!.contains(getString(string.unauthenticated))) {
                                    CoroutineScope(Dispatchers.Main).launch {
                                        popupDialog.showSessionExpiredDialog(requireContext())
                                        delay(1200)
                                        popupDialog.onDismiss()
                                        findNavController().navigate(R.id.go_to_login)
                                    }
                                } else {
                                    mostWantedAdapter.notifyItemChanged(itemPosition)
                                    bottomSheetAlertDialog.showDialog(it.message.toString())
                                }
                            }
                        } else if (it.message.toString().contains(Constants.SERVER_ERROR)) {
                            Helper.ShowErrorDialog(
                                requireContext(),
                                getString(string.server_error)
                            )
                        }
                        calcPrice(minOrder, totalPrice)
                        cartLiveData.value = null
                    }
                }
            }

            override fun addToCart(
                pathEndPoint: String, shopId: Int?, modelType: String?,
                modelId: Int?,
                qty: Int?,
                itemPrice: Float,
                maxQty: Int,
                minQty: Int,
                minOrdern: Float,
                itemPosition: Int
            ) {
                this.qty = qty ?: 0
                this.itemPrice = itemPrice
                this.maxQty = maxQty
                this.itemPosition = itemPosition
                Log.d(TAG, "addToCart: ${itemPosition}")
                if (SharedPreferencesManager.isLoggedIn() == "true") {
                    val offset = if (qty == minQty) qty else 1
                    val p = ((totalPrice * 100) + (offset * (itemPrice * 100))) / 100
                    totalPrice = p
                    calcPrice(minOrder, totalPrice)
                    val cartLiveData = productsFragmentViewModel
                        .addToCartForSectionProducts(
                            pathEndPoint,
                            shop_id,
                            modelType,
                            modelId,
                            qty
                        )
                    addToCartResponseHandle(cartLiveData, offset)
                } else {
                    findNavController().navigate(R.id.go_to_login)
                }
            }
        }

        productFavHandler = object : IFavClickListener {
            override fun onFavClick(parent: Int, position: Int, operation: Int) {
                _operation = operation
                _position = position
                adapterFavType = 2
                resultList[position].id?.let {
                    productsFragmentViewModel.addRemoveFav(
                        it
                    )
                }
            }

        }
        mostWantedFavHandler = object : IFavClickListener {
            override fun onFavClick(parent: Int, position: Int, operation: Int) {
                _operation = operation
                _position = position
                adapterFavType = 1
                mostWantedResultList[position].id?.let {
                    productsFragmentViewModel.addRemoveFav(
                        it
                    )
                }
            }

        }
        binding.includeQtyCartInfo.openCartBtn.setOnClickListener {
            val b = Bundle()
            b.putString(Constants.INTENT_PAGE_TYPE, "store_details")
            findNavController().navigate(R.id.view_cart, b)
        }
        binding.toolbarSearchByCategory.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }

        binding.toolbarSearchByCategory.btnSearch.setOnClickListener {

            val bundle = Bundle()
            bundle.putString(
                Constants.INTENT_NAME,
                getString(string.section_search_hint)
            )
            bundle.putInt(Constants.INTENT_ID, shop_id!!)
            bundle.putFloat(Constants.MIN_ORDER, minOrder)
            bundle.putInt(Constants.INTENT_TYPE_ID, catId!!)

            findNavController().navigate(
                R.id.action_productSearchCategoryResultFragment_to_findInStoreFragment,
                bundle
            )
        }
        binding.toolbarSearchByCategory.btnCart.setOnClickListener {
            val b = Bundle()
            b.putString(Constants.INTENT_PAGE_TYPE, "store_details")
            findNavController().navigate(R.id.view_cart, b)
        }
        setProductAdapter()
        setMostWantedProductsAdapter()
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    override fun onDestroy() {
        super.onDestroy()
        bannersSlider?.stop()
        binding.includeMostSales.rvMostWantedProducts.removeOnScrollListener(rvMostWantedScrollListener)
    }

    private fun setMostWantedProductsAdapter() {

        mostWantedResultList = arrayListOf()
        mostWantedAdapter =
            ProductsAdapter(
                onFavClickListener = mostWantedFavHandler,
                onClickHandler = mostWantedOnClickHandler,
                isHorizontal = true,
                context = requireContext()
            )
        mostWantedAdapter.setItemsList(mostWantedResultList, minOrder)
        binding.includeMostSales.rvMostWantedProducts.adapter = mostWantedAdapter
        binding.includeMostSales.rvMostWantedProducts.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

        rvMostWantedScrollListener = object :RecyclerView.OnScrollListener(){
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
                val totalItemCount = layoutManager.itemCount
                if (lastVisibleItemPosition == totalItemCount - 1) {
                    val hasMorePage = productsFragmentViewModel.mostWantedProductsResponse.value?.pagingator?.hasMorePages
                    if (hasMorePage==true) {
                        binding.includeMostSales.horizontalLoading.visibility = View.GONE
                        val page = (productsFragmentViewModel.mostWantedProductsResponse.value?.pagingator?.currentPage?:0)+1
                        productsFragmentViewModel.getMostWantedProducts(shop_id!!,
                            <EMAIL>!!,page)
                    }
                }
            }
        }
        binding.includeMostSales.rvMostWantedProducts.addOnScrollListener(rvMostWantedScrollListener)

    }
    @SuppressLint("NotifyDataSetChanged")
    private fun showMostWantedProducts(data: MutableList<ProductDetailsDto>) {
        mostWantedResultList = data as ArrayList<ProductDetailsDto>
        mostWantedAdapter.setItemsList(mostWantedResultList, minOrder)
        mostWantedAdapter.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun showData(items: List<ProductDetailsDto>) {
//        var pos: Int = -1
//        items.forEach {
//            it.position = ++pos
//        }
        resultList = items as ArrayList<ProductDetailsDto>
        productsdapter.setItemsList(resultList, minOrder)
        productsdapter.notifyDataSetChanged()
    }

    override fun onFavClick(parent: Int, position: Int, operation: Int) {

        _operation = operation
        _position = position
        resultList[position].id?.let {
            productsFragmentViewModel.addRemoveFav(
                it
            )
        }


    }

    override fun showHidePlaceHolder(
        show: Boolean,
        type: LottieIconEnum?,
        message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    if (message == Constants.SERVER_ERROR) {
                        binding.placeholder.tvMessage.text =
                            getString(string.server_error)

                    } else {
                        binding.placeholder.tvMessage.text = message

                    }
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }


    @SuppressLint("ResourceType")
    private fun setSubCategoriesAdapter() {
        subCategoryAdapter = SubCategoryAdatper(this)
        subCategoryAdapter.items = arrayListOf()
        binding.rvSubCategories.adapter = subCategoryAdapter
        binding.rvSubCategories.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
    }
    @SuppressLint("ResourceType")
    private fun showSubCategories(data: MutableList<SubCategory>) {
        data.add(
            0,
            SubCategory(id = -1, name = getString(string.all))
        )
        subCategoryAdapter.items = data
    }

    private fun setProductAdapter() {

        productsdapter =
            ProductsAdapter(
                onFavClickListener = productFavHandler,
                onClickHandler = productOnClickHandler,
                isHorizontal = false,
                context = requireContext()
            )
        productsdapter.setItemsList(resultList, minOrder)
        binding.rvProducts.adapter = productsdapter
        val manager = GridLayoutManager(context, 2)
        binding.rvProducts.layoutManager = manager
        binding.rvProducts.isNestedScrollingEnabled = false
        binding.rvProducts.adapter = productsdapter


    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refresh(result: ArrayList<ProductDetailsDto>? = null, position: Int? = null) {
        if (result == null) {
            productsdapter.items = resultList
            productsdapter.notifyDataSetChanged()
        } else {

            if (position != null) {
                productsdapter.notifyItemChanged(itemPosition)
            } else {

                productsdapter.items = result
                productsdapter.notifyDataSetChanged()
            }
        }


    }

    override fun onItemClicked(itemId: Int?, type: String, name: String) {

        scroll(itemId, name)
    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle = Bundle()
        bundle.putInt(Constants.INTENT_ID, itemId ?: -1)
        findNavController().navigate(R.id.view_productDetails, bundle)
    }


    private fun scroll(itemId: Int?, itemName: String) {


//        productsFragmentViewModel.productsCategoriesResponse.value!!.data!!.products.let {

            when (itemId) {
                -1 -> {
                    isMainCategory = true
                    binding.tvCurrentSection.text =
                        getString(string.all_products)
//                    refresh()
                    Log.d(TAG, "getProductsByCategory branches")
                    productsFragmentViewModel.getProductsByCategory(catId!!, branch_type_id!!, shop_id!!, 1)

                }
                else -> {
                    subCatId = itemId
                    isMainCategory = false
                    binding.tvCurrentSection.text = itemName
//                    val result = it.filter { it.subCategory?.id == itemId }
//
//                    refresh(result as ArrayList<ProductDetailsDto>)
                    productsFragmentViewModel.getProductsBySubCategory(subCatId!!, branch_type_id!!, shop_id!!, 1)

                }
//            }


        }

    }

    fun RecyclerView.smoothSnapToPosition(
        position: Int,
        snapMode: Int = LinearSmoothScroller.SNAP_TO_START
    ) {

        Log.d(TAG, "scroll: ")

        val smoothScroller = object : LinearSmoothScroller(this.context) {
            override fun getVerticalSnapPreference(): Int = snapMode
            override fun getHorizontalSnapPreference(): Int = snapMode
        }
        smoothScroller.targetPosition = position
        layoutManager?.startSmoothScroll(smoothScroller)


    }

    /////////////////////////////////////// cart
    var start: Boolean = true

//    private var qty = 0
//    private var maxQty = 0
    private var minOrder = 0f
//    private var itemPrice = 0f
    private var itemPosition = -1

    private fun calcPrice(minOrder: Float, totalPrice: Float) {
        val progressBarMax = 100
        binding.includeQtyCartInfo.progressBar.max = progressBarMax
        binding.includeQtyCartInfo.progressBar.progress = if (minOrder > 0) {
            (((totalPrice * progressBarMax) / (minOrder * progressBarMax)) * progressBarMax).toInt()
        } else {
            0
        }
        //min order
        binding.includeQtyCartInfo.minOrder.text = formatPrices(minOrder, string.min_order)
        //total amount
        binding.includeQtyCartInfo.totalAmount.text =
            formatPrices(totalPrice, string.product_order_total_amount)

    }

    private fun formatPrices(price: Float, strRes: Int): Spanned {
        val formatPriceStr = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH)
            .format(price) + "&#8200;" +
                getString(string.currency_name)
        val strResWithPrice = getString(strRes, formatPriceStr)
        return Html.fromHtml(strResWithPrice, Html.FROM_HTML_MODE_LEGACY)
    }
}
