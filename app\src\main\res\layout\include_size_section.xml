<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.FurnitureApp.MaterialComponent">

    <TextView
        android:id="@+id/size_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:text="@string/available_sizes"
        android:theme="@style/product_details_colors_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/package_list_container"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hintEnabled="false"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/size_title">

        <AutoCompleteTextView
            android:id="@+id/sizes_dropdown_list"
            android:textAppearance="@style/product_details_size_value"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:hint="@string/available_sizes" />

    </com.google.android.material.textfield.TextInputLayout>

</androidx.constraintlayout.widget.ConstraintLayout>