package com.techcubics.albarkahyper.ui.views.auth.fragments.register

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.location.Geocoder
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.maps.*
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.widget.Autocomplete
import com.google.android.libraries.places.widget.AutocompleteActivity
import com.google.android.libraries.places.widget.model.AutocompleteActivityMode
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentFacilityLocationBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.java.KoinJavaComponent.inject
import java.text.DecimalFormat
import java.util.*


class FacilityLocationFragment : Fragment() {

    private var governorateList: MutableLiveData<ArrayList<Governerate>> =
        MutableLiveData<ArrayList<Governerate>>()
    private var regionList: MutableLiveData<ArrayList<Regoin>> =
        MutableLiveData<ArrayList<Regoin>>()
    private var mapFragment: SupportMapFragment? = null
    private var googleMap: GoogleMap? = null
    private var selectedCountryId = 0
    private var selectedGovernorateId = 0
    private var selectedRegionId = 0
    private lateinit var chosenLocation: Latlng
    private lateinit var currentLocation: Latlng
    private var location: Latlng? = null
    private lateinit var popupDialog: PopupDialog
    private var checkGovCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkRegionCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkCountryCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()
    private val signUpViewModel by viewModel<AuthViewModel>()
    private lateinit var facilityLocationBinding: FragmentFacilityLocationBinding
    private lateinit var progressBtn: ProgressButton
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val facilityLocationInfo = FacilityLocationInfo()
    private val mainViewModel: MainViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        facilityLocationBinding = FragmentFacilityLocationBinding.inflate(inflater, container, false)
        initViews()
        observeViews()

        return facilityLocationBinding.root
    }

    private fun initViews(){

        initMap()
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        Helper.loadingAnimationVisibility(View.VISIBLE,facilityLocationBinding.actionLoadingAnimation.root)
//        profileViewModel.getCountries()

        facilityLocationBinding.region.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)
        facilityLocationBinding.governerate.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)
        facilityLocationBinding.country.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)


        //address clicked to show search
        facilityLocationBinding.address.keyListener = null

        facilityLocationBinding.address.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                facilityLocationBinding.address.callOnClick()
            }
        }

        facilityLocationBinding.address.setOnClickListener {
            val fieldList =
                listOf(Place.Field.ADDRESS, Place.Field.LAT_LNG, Place.Field.NAME)
            val intent = Autocomplete.IntentBuilder(AutocompleteActivityMode.OVERLAY, fieldList)
                .build(requireActivity())
            startActivityForResult(intent, 100)
        }

        facilityLocationBinding.country.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, facilityLocationBinding.actionLoadingAnimation.root)
            checkCountryCLicked.postValue(true)
            profileViewModel.getCountries()
            facilityLocationBinding.governerate.isEnabled = true
            facilityLocationBinding.governerate.isClickable = true
            facilityLocationBinding.governerate.isLongClickable = true
        }
        facilityLocationBinding.governerate.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, facilityLocationBinding.actionLoadingAnimation.root)
            checkGovCLicked.postValue(true)
            profileViewModel.getGovernerateByCountryId(selectedCountryId)
            facilityLocationBinding.region.isEnabled = true
            facilityLocationBinding.region.isClickable = true
            facilityLocationBinding.region.isLongClickable = true
        }
        facilityLocationBinding.region.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, facilityLocationBinding.actionLoadingAnimation.root)
            checkRegionCLicked.postValue(true)
            profileViewModel.getRegionBygovernorateId(
                selectedGovernorateId,
                selectedCountryId
            )

        }

    }

    private fun initMap(){

        with(facilityLocationBinding.map) {
            // Initialise the MapView
            onCreate(null)
            // Set the map ready callback to receive the GoogleMap object
            try{
                getMapAsync{
                    MapsInitializer.initialize(requireContext())
                    setMapLocation(it)
                }
            }catch (e : Exception){
                bottomSheetAlertDialog.showDialog(e.message.toString())
            }

        }
        val ai: ApplicationInfo = requireContext().packageManager
            .getApplicationInfo(requireContext().packageName, PackageManager.GET_META_DATA)
        val value = ai.metaData["com.google.android.geo.API_KEY"]
        val placekey = value.toString()
        Log.i("here",placekey)
        Places.initialize(requireContext(), placekey)
    }
    private fun observeViews(){
        profileViewModel.countriesPopupMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                facilityLocationBinding.country.setText(it.name)
                profileViewModel.getGovernerateByCountryId(it.country_id)
                selectedCountryId = it.country_id
                facilityLocationInfo.country = it
                mainViewModel.setFacilityLocationInfo(facilityLocationInfo)
                profileViewModel.countriesPopupMenuItemLiveData.value = null

            }
        }
        profileViewModel.listOfgovernerateMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    governorateList.postValue(it.data!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
                profileViewModel.listOfgovernerateMutableLiveData.value = null
            }
        }
        profileViewModel.governeratesMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                facilityLocationBinding.governerate.setText(it.name)
                profileViewModel.getRegionBygovernorateId(
                    it.governorate_id,
                    sharedPreferencesManager.getCountryID().toInt()
                )
                facilityLocationInfo.governorate = it
                mainViewModel.setFacilityLocationInfo(facilityLocationInfo)
                selectedGovernorateId = it.governorate_id
                profileViewModel.governeratesMutableLiveData.value = null
            }
        }
        profileViewModel.listOfregionsMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    regionList.postValue(it.data!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
            }

        }
        profileViewModel.regionPopupMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                facilityLocationBinding.region.setText(it.name)
                selectedRegionId = it.regionID
                facilityLocationInfo.region = it
                mainViewModel.setFacilityLocationInfo(facilityLocationInfo)
                profileViewModel.regionPopupMenuItemLiveData.value = null
            }
        }

        governorateList.observe(viewLifecycleOwner) {
            if (it != null) {
                checkGovCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Log.i("clothes","clickedgov"+it)
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                facilityLocationBinding.actionLoadingAnimation.root
                            )
                            popupDialog.showGovernerateDialog(
                                requireContext(),
                                R.layout.dialog_language,
                                governorateList.value!!,
                                profileViewModel.governeratesMutableLiveData
                            )
                        }
                        checkGovCLicked.value = null
                    }
                }

            }
        }
        regionList.observe(viewLifecycleOwner) {
            if (it != null) {
                checkRegionCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                facilityLocationBinding.actionLoadingAnimation.root
                            )
                            popupDialog.showRegionDialog(
                                requireContext(), R.layout.dialog_language,
                                regionList.value!!, profileViewModel.regionPopupMenuItemLiveData
                            )
                        }
                        checkRegionCLicked.value = null
                    }
                }

            }
        }
        profileViewModel.countriesMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                val countryList = it.data
                checkCountryCLicked.observe(viewLifecycleOwner){
                    if(it != null){
                        if(it){
                            Helper.loadingAnimationVisibility(View.GONE, facilityLocationBinding.actionLoadingAnimation.root)
                            popupDialog.showCounrtyDialog(
                                requireContext(),
                                R.layout.dialog_country,
                                countryList,
                                profileViewModel.countriesPopupMenuItemLiveData as MutableLiveData<Any?>,
                                Constants.JOIN
                            )
                        }
                        checkCountryCLicked.value = null
                    }
                }
            }
        }
    }


    private fun getAddress(lat: Double, lng: Double): String {
        var returnAddress = ""
        runBlocking {
            if (<EMAIL> != null) {
                val geocoder =
                    Geocoder(requireContext(), Locale.getDefault())
                val address = geocoder.getFromLocation(lat, lng, 1)
                val addressJob = async { address?.get(0)?.getAddressLine(0).toString() }
                returnAddress = addressJob.await()

            }

        }
        return returnAddress
    }
    private fun roundToSevenDigits(value: Double): String {
        val df = DecimalFormat("##.#######").format(value)
        return convertArabic(df)
    }

    private fun convertArabic(arabicStr: String): String {
        val chArr = arabicStr.toCharArray()
        val sb = StringBuilder()
        for (ch in chArr) {
            if (Character.isDigit(ch)) {
                sb.append(Character.getNumericValue(ch))
            }else if (ch == '٫'){
                sb.append(".")
            }

            else {
                sb.append(ch)
            }
        }
        return sb.toString()
    }


    fun setMapLocation(mapView: GoogleMap) {
        Log.i("here", "onMapReady")
        googleMap = mapView
        googleMap?.uiSettings?.isZoomControlsEnabled = true

        currentLocation = sharedPreferencesManager.getCurrentLatlng()
        val currentLatLng =
            LatLng(roundToSevenDigits(currentLocation.lat.toDouble()).toDouble(),roundToSevenDigits(currentLocation.lng.toDouble()).toDouble())
        chosenLocation = currentLocation
        facilityLocationInfo.lat = currentLatLng.latitude
        facilityLocationInfo.lng = currentLatLng.longitude
        facilityLocationInfo.address = getAddress(currentLatLng.latitude, currentLatLng.longitude)
        mainViewModel.setFacilityLocationInfo(facilityLocationInfo)
        facilityLocationBinding.address.setText(getAddress(currentLatLng.latitude, currentLatLng.longitude))
        placeMarker(currentLatLng)
    }

    private fun addLocationMarker(currentLatLng: LatLng) {
        if (googleMap != null) {
            val markerOption = MarkerOptions().position(currentLatLng)
                .title(getString(com.techcubics.style.R.string.choosen_location))
                .snippet(getAddress(currentLatLng.latitude, currentLatLng.longitude))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLng(currentLatLng))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0f))
            val currentMarker = googleMap?.addMarker(markerOption)
            currentMarker?.showInfoWindow()
        }
    }

    private fun placeMarker(currentLatLng: LatLng) {
        Helper.loadingAnimationVisibility(View.GONE,facilityLocationBinding.actionLoadingAnimation.root)
        if (googleMap != null) {
            if (ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            googleMap?.isMyLocationEnabled = true
            googleMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0F))

        }

    }


    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == Activity.RESULT_OK) {

            //init place
            val place = data?.let { Autocomplete.getPlaceFromIntent(it) }

            //set Address on edittext
            place?.let {
                facilityLocationBinding.address.setText(place.name)
                place.latLng?.let { latlng ->
                    addLocationMarker(latlng)
                    facilityLocationInfo.lat = latlng.latitude
                    facilityLocationInfo.lng = latlng.longitude
                    facilityLocationInfo.address = place.address
                    mainViewModel.setFacilityLocationInfo(facilityLocationInfo)
                    chosenLocation = Latlng(roundToSevenDigits(latlng.latitude),roundToSevenDigits(latlng.longitude))
                    Log.i("here",latlng.toString()+chosenLocation)
                }
            }


        } else if (resultCode == AutocompleteActivity.RESULT_ERROR) {

            //init status
            val status = data?.let { Autocomplete.getStatusFromIntent(it) }
            Log.i("here", "status" + status?.statusMessage)
        }
    }

    override fun onPause() {
        super.onPause()
        facilityLocationBinding.map.onPause()
    }


    override fun onResume() {
        super.onResume()
        facilityLocationBinding.map.onResume()
    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}
data class FacilityLocationInfo(
    var country: Countries?=null,
    var governorate: Governerate?=null,
    var region: Regoin?=null,
    var address: String?=null,
    var lat: Double?=null,
    var lng: Double?=null
)