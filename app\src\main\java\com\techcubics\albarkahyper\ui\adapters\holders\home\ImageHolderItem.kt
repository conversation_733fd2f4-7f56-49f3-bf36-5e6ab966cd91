package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Image
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemImageViewBinding


class ImageHolderItem (val binding: ItemImageViewBinding, val context: Context): RecyclerView.ViewHolder(binding.root) {

    fun bind(image: Image){
        Helper.loadImage(context,image.path,binding.photo)
    }

}