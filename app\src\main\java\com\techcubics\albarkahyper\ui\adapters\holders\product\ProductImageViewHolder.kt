package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.YouTubeHelper
import com.techcubics.albarkahyper.databinding.ItemProductImageBinding
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.data.model.pojo.Image

class ProductImageViewHolder(
    private val context: Context,
    private val binding: ItemProductImageBinding,
    private val youtubeSizeChangeListener: OnItemsChangedListener?,
    private val viewModel: MainViewModel?
) :
    RecyclerView.ViewHolder(binding.root) {

    fun setData(image: Image) {
        with(binding) {
            youtubePlayerView.visibility = View.GONE
            furnitureImg.visibility = View.VISIBLE

            Helper.loadImage(context, image.path, furnitureImg)
        }
    }

    fun setVideo(videoId: String) {
        with(binding) {
            furnitureImg.visibility = View.GONE
            youtubePlayerView.visibility = View.VISIBLE
            val youTubeHelper =
                YouTubeHelper(context as FragmentActivity, videoId, youtubePlayerView, 0f)
            youTubeHelper.initialize(false)
            setCurrentSeekTimeObserver(youTubeHelper)
            youTubeHelper.setFullScreenListener(fullScreen = { seconds ->
                youtubeSizeChangeListener?.onYoutubeSizeChanged(
                    seconds,
                    videoId,
                    bindingAdapterPosition
                )
            })
        }
    }

    private fun setCurrentSeekTimeObserver(youTubeHelper: YouTubeHelper) {
        viewModel?.seconds?.observe(youtubeSizeChangeListener as LifecycleOwner) { seconds ->
            binding.youtubePlayerView.exitFullScreen()
            youTubeHelper.setCurrentSeconds(seconds)
            youTubeHelper.setCurrentSeekTime()
        }
    }

}