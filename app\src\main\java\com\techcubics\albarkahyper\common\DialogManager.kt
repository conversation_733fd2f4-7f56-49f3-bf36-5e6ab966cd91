package com.techcubics.albarkahyper.common

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager

object DialogManager {
    fun showMaterialFeedback(context: Context, rating: Float, email: String?) {
        val fragmentManager = getFragManager(context)
        val materialFeedback = email?.let { DialogFeedback(it) }
        materialFeedback?.setRating(rating)
        materialFeedback?.show(fragmentManager, DialogRateApp.KEY)
    }

    private fun getFragManager(context: Context): FragmentManager {
        val activity = context as AppCompatActivity
        return activity.supportFragmentManager
    }
}