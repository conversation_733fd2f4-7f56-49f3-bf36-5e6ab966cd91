<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:android="http://schemas.android.com/apk/res/android"
        tools:context=".ui.views.home.navFragments.profile.fragments.addresses.AddAddressFragment"
        android:id="@+id/addaddress_layout">


        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_fragment"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/sc_view"

            />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/sc_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/country_"
                        android:textAppearance="@style/label_addaddress" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="25dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/governorate"
                        android:textAppearance="@style/label_addaddress" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <EditText
                        android:id="@+id/country"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:cursorVisible="false"
                        android:focusableInTouchMode="false"
                        android:background="@drawable/et_style_with_borders"
                        android:drawableEnd="@drawable/ic_arrow_down"
                        android:drawablePadding="12dp"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_addaddress"
                        android:inputType="textNoSuggestions"
                        />

                    <EditText
                        android:id="@+id/governerate"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:cursorVisible="false"
                        android:focusableInTouchMode="false"
                        android:drawableEnd="@drawable/ic_arrow_down"
                        android:drawablePadding="12dp"
                        android:padding="3dp"
                        android:inputType="textNoSuggestions"
                        android:textAppearance="@style/label_edittext_addaddress" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                  >

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/region"
                        android:textAppearance="@style/label_addaddress" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    >

                    <EditText
                        android:id="@+id/region"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:cursorVisible="false"
                        android:focusableInTouchMode="false"
                        android:drawableEnd="@drawable/ic_arrow_down"
                        android:drawablePadding="12dp"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_addaddress" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/address"
                        android:textAppearance="@style/label_addaddress" />

                    <EditText
                        android:id="@+id/address"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:padding="3dp"
                        android:inputType="textNoSuggestions"
                        android:textAppearance="@style/label_edittext_addaddress" />
                </LinearLayout>
                <androidx.fragment.app.FragmentContainerView
                    android:id="@+id/map"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:name="com.google.android.gms.maps.SupportMapFragment"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:background="@drawable/et_style_with_borders" />

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="23dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="70dp" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/action_loading_animation"
            layout="@layout/include_action_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>