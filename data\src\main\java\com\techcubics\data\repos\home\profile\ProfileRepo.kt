package com.techcubics.data.repos.home.profile
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.pojo.Locations
import com.techcubics.data.model.requests.profile.UpdateProfileRequest
import com.techcubics.data.model.requests.profile.location.AddLoactionRequest
import com.techcubics.data.model.requests.profile.location.UpdateLocationRequest
import com.techcubics.data.model.requests.profile.ContactusRequest
import com.techcubics.data.model.requests.profile.JoinusRequest
import com.techcubics.data.model.requests.profile.MyPurchaseRequset
import com.techcubics.data.model.requests.profile.location.UpdatePurchaseRequest
import com.techcubics.data.remote.BaseResponse
import retrofit2.Response
import retrofit2.http.Query

interface ProfileRepo {

    suspend fun getCountries(): BaseResponse<List<Countries>>?
    suspend fun getLanguages() : BaseResponse<List<Language>>?
    suspend fun profileCall(): BaseResponse<User>?
    suspend fun updateProfileCall(updateProfileRequest: UpdateProfileRequest):BaseResponse<User>?
    suspend fun getGovernerateByCountryId(country_id : Int ):BaseResponse<ArrayList<Governerate>>?
    suspend fun getRegionBygovernorateId(governorate_id : Int,country_id : Int ): BaseResponse<ArrayList<Regoin>>?
    suspend fun storeLocation(addLoactionRequest: AddLoactionRequest):BaseResponse<String>?
    suspend fun updateLocation(updateLocationRequest: UpdateLocationRequest) : BaseResponse<String>?
    suspend fun deleteLocation(location_id : Int,country_id : Int,country_code: String ): BaseResponse<String>?
    suspend fun listLocation(country_id : Int,country_code: String ): BaseResponse<ArrayList<Locations>>?
    suspend fun showLocation(location_id : Int,country_id : Int ,country_code: String ): BaseResponse<Locations>?
    suspend fun storeTypesCall(): BaseResponse<NearStoreTypes>?
    suspend fun joinus(joinusRequest: JoinusRequest) : BaseResponse<String>?
    suspend fun contactUsCall(contactusRequest: ContactusRequest) : BaseResponse<Nothing>?
    suspend fun pagesCall(page_name:String): BaseResponse<PageData>?
    suspend fun getCountriesAndDeliveryAreasByFurnitureId(furnitureId: Int ,countryId: Int):BaseResponse<ArrayList<DeliveryAreaId>>?
    suspend fun getGovernorateAreasByDeliveryId(deliveryAreaId: Int, countryId: Int): BaseResponse<ArrayList<Governerate>>?
    suspend fun getRegionByGovernorateAndDeliveryAreaId(governorateId: Int, deliveryAreaId: Int, countryId: Int): BaseResponse<ArrayList<Regoin>>?
    suspend fun storePurchase(purchaseRequset: MyPurchaseRequset):BaseResponse<String>?
    suspend fun updatePurchase(updatePurchaseRequest: UpdatePurchaseRequest) : BaseResponse<String>?
    suspend fun deletePurchase(listing_id : Int): BaseResponse<String>?
    suspend fun listPurchase(date: String): BaseResponse<ArrayList<Mypurchase>>?
    suspend fun showPurchase(listing_id : Int): BaseResponse<Mypurchase>?
    suspend fun markPurchaseAsBuying(listing_id: Int): BaseResponse<String>?

}