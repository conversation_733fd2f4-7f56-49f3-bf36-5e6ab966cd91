<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:theme="@style/TransparentBackground"
    android:layout_height="match_parent">

    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/furniture_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:contentDescription="product images"
        android:src="@drawable/portrait_splash_img" />


    <com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
        android:id="@+id/youtube_player_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:autoPlay="false"
        android:layoutDirection="ltr"
        android:visibility="gone"
        app:enableAutomaticInitialization="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--    <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--        android:id="@+id/youtube_thumbnail_container"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:visibility="">-->

    <!--        <com.google.android.youtube.player.YouTubeThumbnailView-->
    <!--            android:id="@+id/youtube_thumbnail"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="250dp"-->
    <!--            android:scaleType="centerCrop"-->
    <!--            app:layout_constraintBottom_toBottomOf="parent"-->
    <!--            app:layout_constraintTop_toTopOf="parent" />-->

    <!--        <ImageView-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:scaleX="4"-->
    <!--            android:scaleY="3.5"-->
    <!--            android:src="@drawable/ic_youtube_play"-->
    <!--            app:layout_constraintBottom_toBottomOf="parent"-->
    <!--            app:layout_constraintEnd_toEndOf="parent"-->
    <!--            app:layout_constraintStart_toStartOf="parent"-->
    <!--            app:layout_constraintTop_toTopOf="parent" />-->
    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->


</androidx.constraintlayout.widget.ConstraintLayout>