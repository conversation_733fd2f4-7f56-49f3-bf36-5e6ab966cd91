<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.views.stores.searchResult.StoreSearchNearByResultFragment">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/linearLayoutCompat2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include

            android:id="@+id/toolbarSearchByNearBy"
            layout="@layout/toolbar_fragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <include

            android:id="@+id/searchToolbar"
            layout="@layout/toolbar_product_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="16dp"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvStores"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            tools:listitem="@layout/item_store" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/pagingLoadingImg"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="bottom"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_pager_loading" />


    </androidx.appcompat.widget.LinearLayoutCompat>


    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />

    <include
        android:id="@+id/page_loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</RelativeLayout>