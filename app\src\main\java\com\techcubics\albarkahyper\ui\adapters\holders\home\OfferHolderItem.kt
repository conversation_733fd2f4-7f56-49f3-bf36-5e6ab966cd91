package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.CircleProgressButton
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Offer
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.databinding.ItemOffersBinding
import com.techcubics.albarkahyper.ui.adapters.product.OfferProductsAdapter
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import org.koin.android.ext.android.get

class OfferHolderItem<T>(
    val binding: ItemOffersBinding,
    val context: Context,
    val margin: Int = 0,
    val clickHandler: IOnAdapterItemClickHandler
) : RecyclerView.ViewHolder(binding.root) {


    private  val TAG = "OfferHolderItem"
    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    fun bind(data: T) {


        when (SharedPreferencesManager.getLanguage()) {
            "ar" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "eg" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "sa" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            else -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
        }
        when (data) {
            is Offer -> {
                fillByGeneral(data)
            }

        }

    }

    private fun fillByGeneral(offer: Offer) {

        val progressButton: CircleProgressButton
        progressButton = CircleProgressButton(context)
        progressButton.initRoundButton(binding.btnOrder)

        binding.tvTitle.text =offer.name
       Helper.loadImage(context, offer.shop.logo, binding.logo)
        binding.tvBranchName.text = offer.shop.name
       // binding.tvDescription.text = offer.shop.description
        binding.tvCurrentPrice.text =
            "${offer.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"

        offer.Images[0].path.let {
            Helper.loadImage(context, offer.Images[0].path, binding.imgThumb)
        }


        //

       /* offer.products.clear()
        offer.products.add(Product(1,1,"Product 1","https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Circle-icons-computer.svg/2048px-Circle-icons-computer.svg.png"))
        offer.products.add(Product(1,1,"Product 2","https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Circle-icons-computer.svg/2048px-Circle-icons-computer.svg.png"))
        offer.products.add(Product(1,1,"Product 3","https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Circle-icons-computer.svg/2048px-Circle-icons-computer.svg.png"))
        offer.products.add(Product(1,1,"Product 4","https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Circle-icons-computer.svg/2048px-Circle-icons-computer.svg.png"))*/
        val productsAdapter: OfferProductsAdapter = OfferProductsAdapter()
        productsAdapter.items = offer.products
        binding.rvOfferProducts.adapter = productsAdapter

        binding.rvOfferProducts.layoutManager =GridLayoutManager(context,2)
          //  LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        itemView.setOnClickListener {
            clickHandler.onItemClicked(offer.id, RateTypesEnum.Offer.value)
        }
        binding.branchContainer.setOnClickListener {view->
            clickHandler.onItemClicked(offer.shop.id, RateTypesEnum.Store.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
            if(SharedPreferencesManager.isLoggedIn() == "true"){
                progressButton.btnRoundActivated()
            }
            clickHandler.addToCart(
                EndPointConstants.add_offer,
                offer.shop.id,
                offer.modelType,
                offer.offerID,
                1,
                maxQty = 1,
                minQty = 1,
                minOrder = 0f,
                itemPosition = bindingAdapterPosition
            )
        }

    }



}