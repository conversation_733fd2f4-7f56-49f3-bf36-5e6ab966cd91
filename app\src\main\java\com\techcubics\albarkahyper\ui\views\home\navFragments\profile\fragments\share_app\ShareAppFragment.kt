package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.share_app

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentShareAppBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants.furniture
import com.techcubics.shared.enums.PagesEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class ShareAppFragment : Fragment() {

    private lateinit var binding: FragmentShareAppBinding
    private val authViewModel by viewModel<AuthViewModel>()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentShareAppBinding.inflate(layoutInflater, container, false)
        initViews()
        observeViews()
        return binding.root
    }

    private fun observeViews() {
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {

            if (it != null) {
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.shareappLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    binding.webview.visibility = View.GONE
                    binding.serverErrorLayout.layout.visibility = View.VISIBLE
                    binding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }

        profileViewModel.pageCallMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE, binding.pageLoadingAnimation.root)
            if (it != null) {
                if (it.status!!) {
                    binding.coolWebviewId.loadDataWithBaseURL(
                        null,
                        it.data!!.description,
                        "text/html",
                        "utf-8",
                        null
                    )
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    binding.webview.visibility = View.GONE
                    binding.serverErrorLayout.layout.visibility = View.VISIBLE
                    binding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                } else {
                    binding.webview.visibility = View.GONE
                    binding.serverErrorLayout.layout.visibility = View.VISIBLE
                    binding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.serverErrorLayout.tvMessage.text = it.message
                }
                profileViewModel.pageCallMutableLiveData.value = null
            }
        }
    }

    private fun initViews() {
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        checkAutherization()
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.pageLoadingAnimation.root)
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.share_app)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }
        profileViewModel.pagecall(PagesEnum.SharingPoint.value)
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}