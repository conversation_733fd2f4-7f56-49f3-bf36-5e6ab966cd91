package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class Offer(

    @SerializedName("id")
    val id: Int,
    @SerializedName("offer_id")
    val offerID: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("slug")
    var slug: String? = null,
    @SerializedName("price")
    val price: Float,
    @SerializedName("video")
    val video: String,
    @SerializedName("images")
    val Images: MutableList<Image>,

    @SerializedName("shop")
    val shop:Store,

    val furniture_logo: String,
    @SerializedName("products")
    val products: MutableList<Product>,
    @SerializedName("rate")
    val rate: Int,

    @SerializedName("rate_count")
    val rate_count: Int,

    @SerializedName("model_type")
    val modelType: String,

    @SerializedName("qty_cart")
    val quantityCart: Int
)
