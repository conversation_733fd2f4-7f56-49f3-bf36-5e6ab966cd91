package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class StoreDetailsDiscountsData(
    @SerializedName("id")
    val id:Int,
    @SerializedName("discount_id")
    val discountID:Int,
    @SerializedName("price_before")
    val priceBefore:Float,
    @SerializedName("price_after")
    val priceAfter:Float,
    @SerializedName("percent")
    val percent:Float,
    @SerializedName("product_name")
    val productName:String,
    @SerializedName("product_description")
    val productDescription:String,
    @SerializedName("images")
    val Images:MutableList<Image>,
    @SerializedName("furniture_id")
    val furnitureID:Int,
    @SerializedName("furniture_name")
    val furnitureName:String,
    @SerializedName("furniture_logo")
    val furnitureLogo:String,
    @SerializedName("rate")
    val rate:Float,
    @SerializedName("rate_count")
    val rateCount:Int,
    @SerializedName("model_type")
    val modelType:String,
    @SerializedName("qty_cart")
    val quantityCart:String,
    @SerializedName("minimum_order_number") var minQty: Int = 1,
    @SerializedName("maximum_order_number") var maxQty: Int = 1
)
