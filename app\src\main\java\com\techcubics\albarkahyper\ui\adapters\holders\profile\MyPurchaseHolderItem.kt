package com.techcubics.albarkahyper.ui.adapters.holders.profile

import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemLanguageBinding
import com.techcubics.albarkahyper.databinding.ItemMypurchasesBinding


class MyPurchaseHolderItem(itemView: ItemMypurchasesBinding) : RecyclerView.ViewHolder(itemView.root){
        val itemCheckbox = itemView.purchaseCheckBox
        val itemName = itemView.item
        val itemDate = itemView.date
    }