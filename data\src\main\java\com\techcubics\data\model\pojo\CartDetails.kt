package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName


data class CartDetails (

  @SerializedName("cart_detail_id" ) var cartDetailId  : Int?                     = null,
  @SerializedName("model_id", alternate = ["modelable_id"])
  var modelId: Int? = null,
  @SerializedName("model_type"     ) var modelType     : String?                  = null,
  @SerializedName("qty"            ) var qty           : Int?                     = null,
  @SerializedName("price"          ) var price         : Float?                     = null,
  @SerializedName("size_id"        ) var sizeId        : Int?                     = null,
  @SerializedName("size_name"      ) var sizeName      : String?                  = null,
  @SerializedName("size_price"     ) var sizePrice     : Float?                     = null,
  @SerializedName("color_id"       ) var colorId       : Int?                     = null,
  @SerializedName("color_name"     ) var colorName     : String?                  = null,
  @SerializedName("color_code"     ) var colorCode     : String?                  = null,
  @SerializedName("product_id"     ) var productId     : Int?                     = null,
  @SerializedName("product_name"   ) var productName   : String?                  = null,
  @SerializedName("product_slug"   ) var productSlug   : String?                  = null,
  @SerializedName("product_icon"   ) var productIcon   : String?                  = null,
  @SerializedName("product_images" ) var productImages : ArrayList<Image> = arrayListOf(),
  @SerializedName("products"       ) var products      : ArrayList<Products>      = arrayListOf(),
  @SerializedName("minimum_order_number"   ) var minOrderNum      : Int     = 1,
  @SerializedName("maximum_order_number"   ) var maxOrderNum      : Int     = 1,
  @SerializedName("is_discount"          ) var isDiscount         : Boolean?          = null,
  @SerializedName("discount_id"          ) var discountId         : Int?              = null,
  @SerializedName("price_before"         ) var priceBefore        : Float?              = null,
  @SerializedName("product_status" ) var status        : Boolean                  = true,
  @SerializedName("price_after"          ) var priceAfter         : Float?           = null,
  @SerializedName("percent"              ) var percent            : Double?           = null
)