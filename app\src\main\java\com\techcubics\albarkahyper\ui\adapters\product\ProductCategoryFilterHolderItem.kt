package com.techcubics.albarkahyper.ui.adapters.product


import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Category
import com.techcubics.albarkahyper.common.ICategoryClickListener
import com.techcubics.albarkahyper.databinding.ItemSizeFilterBinding


class ProductCategoryFilterHolderItem(
    val binding: ItemSizeFilterBinding,
    val context: Context,
    val handler: ICategoryClickListener? = null
) : RecyclerView.ViewHolder(binding.root) {

    private val TAG = "ProductSizeFilterHolderItem"
    private var isSelected: Boolean = false
    fun bind(category: Category) {

        binding.size.setText(category.name)

        binding.root.setOnClickListener {

            if (isSelected == false) {

                isSelected = true
                binding.size.isChecked = true
                handler?.onAddCategoryClick(category)
            } else {

                isSelected = false
                binding.size.isChecked = false
                handler?.onRemoveCategory(category)
            }

        }
        binding.size.setOnClickListener {

            if (binding.size.isChecked == true) {

                isSelected = true
                handler?.onAddCategoryClick(category)
            } else {

                isSelected = false
                handler?.onRemoveCategory(category)
            }

        }


    }


}