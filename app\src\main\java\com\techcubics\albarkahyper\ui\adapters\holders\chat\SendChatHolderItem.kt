package com.techcubics.albarkahyper.ui.adapters.holders.chat

import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.LiveMessageData
import com.techcubics.data.model.pojo.OrderMessageHistoryData
import com.techcubics.data.model.pojo.SupportTicketData
import com.techcubics.albarkahyper.databinding.ItemChatSendBinding
import org.koin.android.ext.android.get
import org.koin.java.KoinJavaComponent
import java.text.SimpleDateFormat
import java.util.*


class SendChatHolderItem<T>(val binding: ItemChatSendBinding, val context: Context): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "ItemChatSendBinding"
    private lateinit var intent:Intent

    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }

    fun bind(data:T){

       when(data){
           is SupportTicketData-> {
               fillForSupport(data)
           }
           is OrderMessageHistoryData -> {
               fillForOrder(data)
           }
           is LiveMessageData -> {
               fillForLive(data)
           }
       }
    }

  private  fun fillForSupport(message: SupportTicketData){

     if(message.user_id==SharedPreferencesManager.getUserID()){

         binding.sendLayout.visibility=View.VISIBLE
         binding.receiveLayout.visibility=View.GONE

         binding.tvSMessage.text=message.message
         binding.tvSDateTime.text=message.time

     }else{

         binding.receiveLayout.visibility=View.VISIBLE
         binding.sendLayout.visibility=View.GONE

         binding.tvRMessage.text=message.message
         binding.tvRDateTime.text=message.time

     }




    }

    private  fun fillForOrder(message:OrderMessageHistoryData){

        if(message.user_id==SharedPreferencesManager.getUserID()){

            binding.sendLayout.visibility=View.VISIBLE
            binding.receiveLayout.visibility=View.GONE

            binding.tvSMessage.text=message.message
            binding.tvSDateTime.text=message.time

        }else{

            binding.receiveLayout.visibility=View.VISIBLE
            binding.sendLayout.visibility=View.GONE

            binding.tvRMessage.text=message.message
            binding.tvRDateTime.text=message.time

        }




    }
    private  fun fillForLive(message: LiveMessageData){

        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        val l: Date = format.parse(message.createdAt)
        var formatter = SimpleDateFormat("dd/MM/yyyy").format(l)
        var timeFormat = SimpleDateFormat("hh:mm aa").format(l)



        if(message.senderId==SharedPreferencesManager.getID()){

            binding.sendLayout.visibility=View.VISIBLE
            binding.receiveLayout.visibility=View.GONE

            binding.tvSMessage.text=message.message
            binding.tvSDateTime.text="${timeFormat} ${formatter}"

        }else{

            binding.receiveLayout.visibility=View.VISIBLE
            binding.sendLayout.visibility=View.GONE

            binding.tvRMessage.text=message.message
            binding.tvRDateTime.text="${timeFormat} ${formatter}"



        }




        


    }

}