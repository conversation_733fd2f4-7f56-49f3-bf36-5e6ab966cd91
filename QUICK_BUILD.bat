@echo off
echo Building AlBarkaHyper APK - Quick Version
echo.

echo Step 1: Building Debug APK (easier to build)...
call gradlew.bat assembleDebug
if %ERRORLEVEL% equ 0 (
    echo ✓ Debug APK build completed!
    goto :check_debug
)

echo.
echo Step 2: Trying Release APK...
call gradlew.bat assembleRelease
if %ERRORLEVEL% equ 0 (
    echo ✓ Release APK build completed!
    goto :check_release
)

echo.
echo Both builds failed. Please check the errors above.
pause
exit /b 1

:check_debug
echo.
echo Checking Debug APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✓ SUCCESS: Debug APK created!
    echo   Location: %CD%\app\build\outputs\apk\debug\app-debug.apk
    for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do echo   Size: %%~zI bytes
    echo.
    echo ⚠️  WARNING: This is a DEBUG version!
    echo    - Use for testing only
    echo    - NOT suitable for Google Play Store
    echo    - You need Release version for store submission
    echo.
    echo Full path:
    echo %CD%\app\build\outputs\apk\debug\app-debug.apk
) else (
    echo ✗ Debug APK not found!
)
goto :try_release

:check_release
echo.
echo Checking Release APK...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ SUCCESS: Release APK created!
    echo   Location: %CD%\app\build\outputs\apk\release\app-release.apk
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo   Size: %%~zI bytes
    echo.
    echo ✅ PERFECT: This is a RELEASE version!
    echo    - Ready for Google Play Store
    echo    - Signed and optimized
    echo.
    echo Full path:
    echo %CD%\app\build\outputs\apk\release\app-release.apk
    
    echo.
    echo Trying to build AAB as well...
    call gradlew.bat bundleRelease
    if %ERRORLEVEL% equ 0 (
        if exist "app\build\outputs\bundle\release\app-release.aab" (
            echo ✓ BONUS: AAB file also created!
            echo   Location: %CD%\app\build\outputs\bundle\release\app-release.aab
            for %%I in ("app\build\outputs\bundle\release\app-release.aab") do echo   Size: %%~zI bytes
        )
    )
) else (
    echo ✗ Release APK not found!
)
goto :end

:try_release
echo.
echo Debug APK was created. Now trying Release APK...
call gradlew.bat assembleRelease
if %ERRORLEVEL% equ 0 (
    goto :check_release
) else (
    echo Release build failed, but you have Debug APK for testing.
)

:end
echo.
echo ==========================================
echo Build process completed!
echo ==========================================
pause
