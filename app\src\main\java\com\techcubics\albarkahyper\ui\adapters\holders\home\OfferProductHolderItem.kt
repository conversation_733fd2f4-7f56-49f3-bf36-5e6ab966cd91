package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import android.text.TextUtils
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Product
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemOfferProductBinding


class OfferProductHolderItem(
    val binding: ItemOfferProductBinding,
    val context: Context,
    var isDetails: Boolean
) : RecyclerView.ViewHolder(binding.root) {

    fun bind(product: Product){

        Helper.loadImage(context,product.icon,binding.imgProduct)
        binding.tvTitle.text="${product.quantity.toString()} ${product.product}"
        if (isDetails){
            binding.tvTitle.maxLines = Int.MAX_VALUE
            binding.tvTitle.ellipsize = null
        }else{
            binding.tvTitle.maxLines = 1
            binding.tvTitle.ellipsize = TextUtils.TruncateAt.END
        }
    }

}