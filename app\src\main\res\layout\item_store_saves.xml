<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="20dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:background="?attr/selectableItemBackground"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imgThumb"
            android:layout_width="135dp"
            android:layout_height="0dp"
            android:background="@null"
            android:scaleType="centerInside"
            android:layout_marginVertical="18dp"
            android:layout_marginHorizontal="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/portrait_placeholder" />

        <include
            android:id="@+id/includeTimerSection"
            layout="@layout/include_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/rvOfferProducts"
            app:layout_constraintVertical_bias="0.86" />

<!--        <androidx.constraintlayout.widget.ConstraintLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:paddingStart="8dp"-->
<!--            android:paddingTop="8dp"-->
<!--            android:paddingEnd="8dp"-->
<!--            android:paddingBottom="16dp"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@id/imgThumb">-->

        <TextView
            android:id="@+id/tvTitle"
            style="@style/label_item_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="10dp"
            android:textAlignment="viewStart"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="TitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitle"
            android:layout_marginHorizontal="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imgThumb" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOfferProducts"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_marginTop="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvTitle"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle"
                tools:itemCount="3"
                app:spanCount="3"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/item_offer_product" />

        <TextView
            android:id="@+id/tvCurrentPrice"

            style="@style/label_item_price_amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_price"
            android:padding="8dp"
            android:textStyle="bold"
            android:textColor="@color/_light_green"
            app:layout_constraintBottom_toBottomOf="@+id/imgThumb"
            app:layout_constraintEnd_toEndOf="@+id/imgThumb"
            app:layout_constraintStart_toStartOf="@+id/imgThumb"
            app:layout_constraintTop_toTopOf="@+id/imgThumb"
            app:layout_constraintVertical_bias="1.0"
            android:textAlignment="center"
            tools:text="41.99 جنيه" />


<!--        <androidx.appcompat.widget.LinearLayoutCompat-->
<!--                android:id="@+id/linearLayoutCompat"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="8dp"-->
<!--                android:orientation="horizontal"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/rvOfferProducts">-->

<!--                <TextView-->
<!--                    style="@style/label_item_price_title"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="@string/offer_price" />-->

<!--                <TextView-->
<!--                    android:id="@+id/tvCurrentPrice"-->

<!--                    style="@style/label_item_price_amount"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="8dp"-->
<!--                    android:textStyle="bold"-->
<!--                    android:textColor="@color/color_59"-->
<!--                    tools:text="41.99 جنيه" />-->


<!--            </androidx.appcompat.widget.LinearLayoutCompat>-->


        <include
            android:id="@+id/btnOrder"
            layout="@layout/btn_progress_circle"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="10dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@id/includeTimerSection" />

        <LinearLayout
            android:id="@+id/quantity_cont"
            android:layout_width="wrap_content"
            android:background="@drawable/bg_cart_quantity"
            android:padding="2dp"
            android:visibility="invisible"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/btnOrder"
            app:layout_constraintStart_toStartOf="@id/btnOrder"
            app:layout_constraintTop_toTopOf="@id/btnOrder">
            <include
                android:id="@+id/quantity_controller"
                layout="@layout/include_input_quantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>


        <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->



    </androidx.constraintlayout.widget.ConstraintLayout>






</androidx.cardview.widget.CardView>