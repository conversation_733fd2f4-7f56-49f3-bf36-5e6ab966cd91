package com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.albarkahyper.common.*
import com.techcubics.data.local.SharedPreferencesManager

import com.techcubics.data.model.pojo.Offer
import com.techcubics.albarkahyper.databinding.FragmentOffersBinding
import com.techcubics.albarkahyper.ui.adapters.home.OffersAdapter
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class OffersFragment :  Fragment(), IPagePagedRowset<Offer>,
    IOnAdapterItemClickHandler {

    private  lateinit var binding: FragmentOffersBinding
    private lateinit var offersAdapter: OffersAdapter<Offer>
    private  val offersFragmentViewModel: SectionsViewModel by viewModel<SectionsViewModel>()
    private var isLoading:Boolean=false
    private lateinit var resultList:ArrayList<Offer>
    private  val TAG = "OffersFragment"
//    private lateinit var offersProgressButton: CircleProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
//    companion object {
//        val instance: OffersFragment = OffersFragment()
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(DialogFragment.STYLE_NORMAL, com.techcubics.style.R.style.bottomsheet_style)
//
//    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentOffersBinding.inflate(inflater, container, false)
//        dialog?.setOnShowListener {
//            val d: BottomSheetDialog = dialog as BottomSheetDialog
//            val bottomSheetInternal: View =
//                d.findViewById(com.google.android.material.R.id.design_bottom_sheet)!!
//            BottomSheetBehavior.from(bottomSheetInternal)
//                .setState(BottomSheetBehavior.STATE_EXPANDED)
//        }

        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {
        //SharedPreferencesManager.init(requireContext())

        binding.toolbarOffers.tvTitle.text=resources.getText(com.techcubics.style.R.string.section_offers_title)
        offersFragmentViewModel.getOffers(1)
        //
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvOffers.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
    }

    override fun observers() {
        offersFragmentViewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null&&it.message.toString()!= Constants.SERVER_ERROR) {
                if (it.status==true) {
//                    offersProgressButton.btnRoundFinishedSuccessfully()
                    bottomSheetAlertDialog.showDialog(getString(R.string.added_to_cart))
                }else{
                    if(it.message!!.contains(getString(R.string.unauthenticated))){
                        CoroutineScope(Dispatchers.Main).launch {
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
//                        offersProgressButton.btnRoundFinishedFailed()
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }
                }
                offersFragmentViewModel.addCartResponse.value = null
            }else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(com.techcubics.style.R.string.server_error)
                )
            }
        }
        offersFragmentViewModel.offersResponse.observe(this, Observer {


          Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

            try {
                if (it.status!!) {

                    if(it.data!=null){

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvOffers.visibility = View.VISIBLE

                        }else{

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })


    }

    override fun events() {

        binding.toolbarOffers.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
    }


    override fun showData(items: List<Offer>) {
        if ( offersFragmentViewModel.offersResponse.value?.pagingator?.currentPage == 1) {
            resultList = items as ArrayList<Offer>
            offersAdapter = OffersAdapter( clickHandler = this)
            offersAdapter.setItemsList(resultList)
            binding.rvOffers.adapter = offersAdapter
            binding.rvOffers.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            //
            var pageDelegate =
                PagingDelegate.Builder(offersAdapter).attachTo(binding.rvOffers)
                    .listenWith(this).build()


        } else {

            items.forEach {
                resultList.add(it)
            }
            offersAdapter.notifyDataSetChanged()
            onDonePaging()
        }

        Log.d(TAG, "showOffers: ${resultList.size}")

    }

    override fun onPage(p0: Int) {


        if (!isLoading) {
            Log.d(TAG, "onPage: ${p0}")
            if (  offersFragmentViewModel.offersResponse.value?.pagingator?.hasMorePages!!) {
                isLoading = true
                binding.pagingLoadingImg.visibility = View.VISIBLE
                offersFragmentViewModel.getOffers(
                    offersFragmentViewModel.offersResponse.value?.pagingator?.currentPage!! + 1
                )
            }
        }
    }

    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility = View.GONE
        isLoading = false
    }

    override fun showHidePlaceHolder(show: Boolean, type: LottieIconEnum?, message: String?,
                                     container: View?) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                } else -> throw IllegalStateException("error")
            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }
    override fun onItemClicked(itemId: Int?, type: String) {
        super.onItemClicked(itemId, type)
        val bundle = Bundle()
        itemId?.let { bundle.putInt(Constants.INTENT_ID, it) }
        when(type){
            RateTypesEnum.Offer.value->{
                findNavController().navigate(com.techcubics.albarkahyper.R.id.view_offerDetails,bundle)
            }
            RateTypesEnum.Store.value->{

            }
        }
    }
    override fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
        if (SharedPreferencesManager.isLoggedIn() =="true") {
//            offersProgressButton = progressButton
            offersFragmentViewModel.addToCart(pathEndPoint, shopId,modelType,modelId,qty)

        }else{
            findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }


}