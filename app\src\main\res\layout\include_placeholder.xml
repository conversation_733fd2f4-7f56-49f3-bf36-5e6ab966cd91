<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/layout">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/icon"
        android:layout_width="@dimen/icons_size_height_weight_12"
        android:layout_height="@dimen/icons_size_height_weight_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        />

    <TextView
        android:id="@+id/tvMessage"
        style="@style/message_dialogs"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon"
        android:textAlignment="center"/>
</androidx.constraintlayout.widget.ConstraintLayout>