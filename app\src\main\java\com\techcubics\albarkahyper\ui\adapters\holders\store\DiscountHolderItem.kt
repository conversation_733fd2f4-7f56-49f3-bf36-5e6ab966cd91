package com.techcubics.albarkahyper.ui.adapters.holders.store

import android.content.Context
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.StoreDetailsDiscountsData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.databinding.ItemStoreDiscountsBinding
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.common.QuantityButtonsController

import com.techcubics.shared.enums.RateTypesEnum
import org.koin.android.ext.android.get


class DiscountHolderItem<T> (val binding: ItemStoreDiscountsBinding, val context: Context,val margin:Int=0,
    val onClickListener: IOnAdapterItemClickHandler
): RecyclerView.ViewHolder(binding.root) {
    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    private lateinit var quantityButtonsController: QuantityButtonsController
    private lateinit var modelType: String
    private var discountId = -1
    private var discountPrice=0f
//    private lateinit var progressButton: CircleProgressButton
    private var qtyCart=0
    private var maxQty=0
    private var minQty=0
    fun bind(data: T){

        when (SharedPreferencesManager.getLanguage()){
            "ar"->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
            "eg"->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
            "sa"->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
            else ->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
        }

//        progressButton = CircleProgressButton(context)
//        progressButton.initRoundButton(binding.btnOrder)


        when(data){

            is StoreDetailsDiscountsData->{
                fillByStore(data)
            }
        }
        quantityButtonsController = QuantityButtonsController(
            binding.quantityController.root,
            null,
            context,
            maxQty = maxQty,
            minQty = minQty
        )
        quantityButtonsController.setIsItem(updateQuantity = ::updateQuantity,
            removeCartItem=::removeCartItem)
        quantityButtonsController.updateQuantity(qtyCart)
        quantityButtonsController.isBtnDelete()

        if (qtyCart>0){
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
        }else{
            binding.btnOrder.root.visibility = View.VISIBLE
            binding.quantityCont.visibility = View.INVISIBLE
        }

    }



    fun fillByStore(discount:StoreDetailsDiscountsData){
        modelType = RateTypesEnum.Discount.value
        discountId=discount.discountID
        discountPrice=discount.priceAfter
        qtyCart = discount.quantityCart.toInt()
        maxQty = discount.maxQty
        minQty = discount.minQty
        binding.tvTitle.text=discount.productName
        binding.tvDescription.text=discount.productDescription



        //binding.logo.visibility= View.GONE
        //binding.tvBranchName.visibility= View.GONE


        binding.tvPercentage.text="${discount.percent.toString()} %"
        binding.tvOldPrice.text="${discount.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        binding.tvCurrentPrice.text="${discount.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"

       // binding.tvOldPrice.paintFlags= Paint.STRIKE_THRU_TEXT_FLAG

        if(discount.Images.size>0) {
            Helper.loadImage(context, discount.Images[0].path, binding.imgThumb)
        }
        itemView.setOnClickListener {
            onClickListener.onItemClicked(discount.id, RateTypesEnum.Discount.value)
        }

        binding.btnOrder.constraintsLayout.setOnClickListener {

//            if (SharedPreferencesManager.isLoggedIn().equals("true")) {
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility=View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)

            onClickListener.addToCart(
                EndPointConstants.add_discount,
                -1,
                discount.modelType,
                discount.id,
                minQty,
                discountPrice,
                maxQty,
                minQty,
                0f,
                bindingAdapterPosition
            )
        }
    }
    private fun updateQuantity(
        qty: Int,
    ) {
        onClickListener.addToCart(
            EndPointConstants.add_discount,
            -1,
            modelType,
            discountId,
            qty,
            discountPrice,
            maxQty,
            minQty,
            0f,
            bindingAdapterPosition
        )
    }
    private fun removeCartItem(){
        binding.btnOrder.root.visibility = View.VISIBLE
        binding.quantityCont.visibility=View.INVISIBLE
        onClickListener.removeItemFromCart(
            modelType,
            discountId,
            0f,
            bindingAdapterPosition
        )
    }
}