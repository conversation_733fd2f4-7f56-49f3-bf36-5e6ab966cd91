package com.techcubics.albarkahyper.common

import android.view.View
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.shared.enums.LottieIconEnum

interface IPageBehaviour{

    fun init()
    fun observers()
    fun events()
    fun showHidePlaceHolder(show:<PERSON><PERSON><PERSON>, type: LottieIconEnum?, message:String?, container: View?=null){}

}

interface IPageDetails<T>:IPageBehaviour{

    fun showData( item:T)
}

interface IPageRowset<T>:IPageBehaviour{

    fun showData( items:List<T>)
}

interface IPagePagedRowset<T>:IPageBehaviour,PagingDelegate.OnPageListener{

    fun showData( items:List<T>)
    override fun onPage(p0: Int)
    override fun onDonePaging()
}


