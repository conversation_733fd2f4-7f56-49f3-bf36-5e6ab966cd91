<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingTop="16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/cart_product_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:maxHeight="450dp"
                app:layout_constraintHeight_max="450dp"
                app:layout_constraintTop_toTopOf="parent"
                tools:itemCount="2"
                tools:listitem="@layout/item_cart_product" />

            <TextView
                android:id="@+id/shipping_expenses_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="24dp"
                android:text="@string/shipping_expenses"
                android:theme="@style/label_cart_details_shipping_expenses_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cart_product_recycler_view" />

            <TextView
                android:id="@+id/shipping_expenses_note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="4dp"
                android:text="@string/shipping_note"
                android:theme="@style/label_shipping_expenses_note"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/shipping_expenses_title" />

            <TextView
                android:id="@+id/total_shipping_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/total_shipping_title"
                android:theme="@style/label_total_shipping_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/shipping_expenses_note" />

            <TextView
                android:id="@+id/total_shipping"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:theme="@style/label_total_shipping_value"
                app:layout_constraintBottom_toBottomOf="@id/total_shipping_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/total_shipping_title"
                tools:text="0 جنيه" />

            <LinearLayout
                android:id="@+id/btn_add_address"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_gray_ripple"
                android:clickable="true"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingVertical="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/total_shipping">

                <ImageView
                    android:id="@+id/img"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="5dp"
                    android:src="@drawable/ic_item_add"
                    app:tint="@color/color_gray_9" />

                <TextView
                    style="@style/label_btn_add_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/address_adding" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/address_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintHeight_max="400dp"
                app:layout_constraintTop_toBottomOf="@id/btn_add_address"
                tools:itemCount="2"
                tools:listitem="@layout/item_address" />

            <TextView
                android:id="@+id/coupon_code_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/coupon_code_title"
                android:theme="@style/label_coupon_code_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/address_recycler_view" />

            <LinearLayout
                android:id="@+id/coupon_code_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:weightSum="2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/coupon_code_title">

                <EditText
                    android:id="@+id/coupon_code"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="1dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:padding="3dp" />

                <include
                    android:id="@+id/btn_add_coupon_code"
                    layout="@layout/btn_progress"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginBottom="1dp"
                    android:layout_weight="1" />

            </LinearLayout>


            <TextView
                android:id="@+id/coupon_note"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_round_blue"
                android:gravity="center"
                android:padding="11dp"
                android:text="@string/deduction"
                android:textAlignment="center"
                android:theme="@style/label_coupon_note"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/coupon_code_container" />
            <TextView
                android:id="@+id/add_notes_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/add_notes_title"
                android:theme="@style/label_orders_summery_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/coupon_note" />

            <EditText
                android:id="@+id/add_notes"
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:layout_marginTop="12dp"
                android:gravity="start|top"
                android:padding="4dp"
                android:inputType="textMultiLine"
                android:textAlignment="viewStart"
                android:background="@drawable/et_style_with_borders"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/add_notes_title" />

            <TextView
                android:id="@+id/orders_summery_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/orders_summery_title"
                android:theme="@style/label_orders_summery_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/add_notes" />

            <TextView
                android:id="@+id/orders_summery_note"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/order_summery_note"
                android:theme="@style/label_orders_summery_note"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/orders_summery_title" />


            <TextView
                android:id="@+id/products_total_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/products_total_title"
                android:theme="@style/label_products_total_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/orders_summery_note" />

            <TextView
                android:id="@+id/products_total_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="61.99 جنيه"
                android:theme="@style/label_products_total_amount_value"
                app:layout_constraintBottom_toBottomOf="@id/products_total_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/products_total_title" />

            <TextView
                android:id="@+id/delivery_fees_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/delivery_fees_title"
                android:theme="@style/label_delivery_fees_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/products_total_title" />

            <TextView
                android:id="@+id/delivery_fees"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:theme="@style/label_delivery_fees_value"
                app:layout_constraintBottom_toBottomOf="@id/delivery_fees_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/delivery_fees_title"
                tools:text="20.99 جنيه" />

            <TextView
                android:id="@+id/coupon_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/coupon_amount_title"
                android:theme="@style/label_coupon_amount_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/delivery_fees_title" />

            <TextView
                android:id="@+id/coupon_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="20.99- جنيه"
                android:theme="@style/label_coupon_amount_value"
                app:layout_constraintBottom_toBottomOf="@id/coupon_amount_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/coupon_amount_title" />

            <TextView
                android:id="@+id/instant_discount_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/instant_discount"
                android:theme="@style/label_coupon_amount_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/coupon_amount_title" />

            <TextView
                android:id="@+id/instant_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="20.99- جنيه"
                android:theme="@style/label_coupon_amount_value"
                app:layout_constraintBottom_toBottomOf="@id/instant_discount_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/instant_discount_title" />

            <TextView
                android:id="@+id/total_amount_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="23dp"
                android:text="@string/total_amount_title"
                android:theme="@style/label_total_amount_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/instant_discount_title" />

            <TextView
                android:id="@+id/total_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="61.99 جنيه"
                android:theme="@style/label_total_amount_value"
                app:layout_constraintBottom_toBottomOf="@id/total_amount_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/total_amount_title" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/total_amount">

                <TextView
                    android:id="@+id/store_is_open_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/bg_round_note_yellow"
                    android:gravity="center"
                    android:padding="11dp"
                    android:visibility="gone"
                    android:text="@string/store_is_open_note"
                    android:textAlignment="center"
                    android:theme="@style/label_store_is_open_note" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop= "30dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btn_order_now"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="4dp"
                        android:layout_weight="1"
                        android:background="@drawable/btn_ripple_normal_gray"
                        android:text="@string/btn_order_now"
                        android:textAppearance="@style/label_button" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btn_back_to_shopping"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:background="@drawable/bg_round_stroke_ripple"
                        android:text="@string/btn_back_to_shopping"
                        android:textAppearance="@style/cart_backtoshop_label_button" />

                </LinearLayout>
                <Button
                    android:id="@+id/delete_cart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    style="@style/label_item_title"
                    android:layout_marginBottom="30dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/btn_delete_ripple"
                    android:text="@string/delete_cart"/>
            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>