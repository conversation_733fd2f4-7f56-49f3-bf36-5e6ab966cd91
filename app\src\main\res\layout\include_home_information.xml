<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imageView2"
        android:layout_width="@dimen/icons_size_height_weight_2"
        android:layout_height="@dimen/icons_size_height_weight_2"
        android:src="@drawable/portrait_banner_qr"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/imageView"
        android:layout_width="@dimen/icons_size_height_weight_7"
        android:layout_height="@dimen/icons_size_height_weight_7"
        android:src="@drawable/ic_camera_outline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:background="?attr/selectableItemBackground"/>

    <TextView
        android:id="@+id/tvScan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="@string/scan"
        app:layout_constraintBottom_toBottomOf="@+id/imageView"
        app:layout_constraintEnd_toStartOf="@+id/imageView2"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/imageView"
        app:layout_constraintTop_toTopOf="@+id/imageView"
        style="@style/label_scan_click_here"
        android:clickable="true"
        android:background="?attr/selectableItemBackground"/>

    <TextView

        style="@style/label_scan_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/section_suggestion"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/imageView2"
        app:layout_constraintTop_toBottomOf="@+id/imageView" />


</androidx.constraintlayout.widget.ConstraintLayout>