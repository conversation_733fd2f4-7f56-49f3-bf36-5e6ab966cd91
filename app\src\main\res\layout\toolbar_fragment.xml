<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="0.5dp"
    android:background="@color/white"
    >

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/main_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        app:navigationIcon="@drawable/ic_back">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_marginEnd="16dp">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                tools:text="@string/offer_price"
                android:maxLines="2"
                android:ellipsize="end"
                style="@style/toolbar_caption"
                android:layout_gravity="start|center_vertical"
                 />

            <ImageButton
                android:id="@+id/btnSearch"
                android:layout_width="@dimen/icons_size_height_weight_9"
                android:layout_height="@dimen/icons_size_height_weight_9"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_search"
                android:layout_gravity="start|center_vertical"
                android:visibility="gone"

                 />
            <ImageButton
                android:id="@+id/btnCart"
                android:layout_width="@dimen/icons_size_height_weight_9"
                android:layout_height="@dimen/icons_size_height_weight_9"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_cart"
                android:layout_gravity="start|center_vertical"
                android:layout_marginStart="10dp"
                android:visibility="gone"

                />
        </androidx.appcompat.widget.LinearLayoutCompat>


    </androidx.appcompat.widget.Toolbar>


</com.google.android.material.appbar.AppBarLayout>

