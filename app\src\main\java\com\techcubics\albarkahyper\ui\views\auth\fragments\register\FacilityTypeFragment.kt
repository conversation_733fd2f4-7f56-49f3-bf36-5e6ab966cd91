package com.techcubics.albarkahyper.ui.views.auth.fragments.register

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.FragmentFacilityTypeBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class FacilityTypeFragment : Fragment() {

    private lateinit var facilityTypeBinding: FragmentFacilityTypeBinding
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val signUpViewModel by viewModel<AuthViewModel>()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val mainViewModel: MainViewModel by activityViewModels()
    private var facilityInfo: FacilityInfo = FacilityInfo()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        facilityTypeBinding = FragmentFacilityTypeBinding.inflate(inflater, container, false)
        setFacilitiesRecyclerView()
        initViews()
        observeViews()
        listeners()
        return facilityTypeBinding.root
    }

    private fun listeners() {
        facilityTypeBinding.nameEdt.doOnTextChanged { text, start, before, count ->
            facilityInfo.facilityName = text.toString()
            mainViewModel.setFacilityInfo(facilityInfo)
        }
        facilityTypeBinding.facilityTypeEdt.doOnTextChanged { text, start, before, count ->
            facilityInfo.facilityType = text.toString()
            mainViewModel.setFacilityInfo(facilityInfo)
        }
    }

    private fun setFacilitiesRecyclerView() {

    }

    private fun initViews() {
    }

    private fun observeViews() {

    }


}
data class FacilityInfo(var facilityName:String?=null, var facilityType:String?=null)