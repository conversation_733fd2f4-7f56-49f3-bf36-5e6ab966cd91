package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.SubCategory
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemMainCategoryBinding
import com.techcubics.albarkahyper.databinding.ItemSliderBinding
import com.techcubics.albarkahyper.databinding.ItemSubCategoryBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.BannerHolderItem
import com.techcubics.albarkahyper.ui.adapters.holders.product.MainCategoryHolderItem
import com.techcubics.albarkahyper.ui.adapters.holders.product.SubCategoryHolderItem


class SubCategoryAdatper (val onClick: IOnAdapterItemClickHandler): RecyclerView.Adapter<SubCategoryHolderItem>() {

    lateinit var items:List<SubCategory>

    fun setItemsList(_items:List<SubCategory>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SubCategoryHolderItem {
        val itemBinding = ItemSubCategoryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SubCategoryHolderItem(itemBinding,parent.context)
    }


    override fun onBindViewHolder(holder: SubCategoryHolderItem, position: Int) {
        var data=items.get(position)

        holder.itemView.setOnClickListener {
            onClick?.onItemClicked(data.id,"category",data.name!!)
            notifyDataSetChanged()
        }

        holder.bind(data)
    }



    override fun getItemCount(): Int {
        return items.size
    }




}