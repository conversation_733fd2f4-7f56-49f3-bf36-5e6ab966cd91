<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:background="?attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout5"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:id="@+id/imgThumb"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            android:background="@color/color_gray_15"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/portrait_placeholder" />

        <TextView
            android:id="@+id/tvDiscountTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_price"
            style="@style/label_item_discount"
            android:text="@string/discount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="center"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"/>
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnAddFav"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:contentDescription="@string/add_to_favorite"
            android:src="@drawable/ic_add_favorite"
            app:backgroundTint="@color/white"
            app:fabSize="mini"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/label_item_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textAlignment="center"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imgThumb"
            tools:text="مياه غازية - 1.5 لتر" />

        <TextView
            android:id="@+id/tvDescription"
            style="@style/label_item_product_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:layout_marginVertical="2dp"
            android:maxLines="2"
            android:gravity="center"
            android:visibility="gone"
            android:textAlignment="center"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            tools:text="كرتونة الكرتونة 12 قطعة" />
        <LinearLayout
            android:id="@+id/min_qty_cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                style="@style/label_item_product_description"
                android:layout_height="wrap_content"
                android:text="@string/min_num"/>
            <TextView
                android:layout_width="wrap_content"
                style="@style/label_item_product_description"
                android:layout_height="wrap_content"
                android:id="@+id/min_qty"
                android:layout_marginStart="5dp"
                android:textColor="@color/app_color"
                tools:text="5"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/max_qty_cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/min_qty_cont"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                style="@style/label_item_product_description"
                android:layout_height="wrap_content"
                android:text="@string/max_num_title"/>
            <TextView
                android:layout_width="wrap_content"
                style="@style/label_item_product_description"
                android:layout_height="wrap_content"
                android:id="@+id/max_qty"
                android:layout_marginStart="5dp"
                android:textColor="@color/app_color"
                tools:text="20"/>
        </LinearLayout>

        <FrameLayout
            android:id="@+id/priceLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/max_qty_cont">

            <TextView
                android:id="@+id/tvCurrentPrice"
                style="@style/label_item_price_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textAlignment="center"
                android:textColor="@color/app_color"
                android:textStyle="bold"

                tools:text="10000 جنيه" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/priceDiscountLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp"

                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/tvBeforeDiscountPrice"
                    style="@style/label_item_old_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/line_diagonal"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvAfterDiscountPrice"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvAfterDiscountPrice"
                    app:layout_constraintTop_toTopOf="@+id/tvAfterDiscountPrice"
                    tools:text="50.99 جنيه" />

                <TextView
                    android:id="@+id/tvAfterDiscountPrice"

                    style="@style/label_item_price_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:textSize="13sp"
                    app:layout_constraintEnd_toStartOf="@id/tvBeforeDiscountPrice"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="41.99 جنيه" />





            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>



        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/branch_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:layout_marginVertical="2dp"
            app:layout_constraintBottom_toTopOf="@id/btnOrder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/priceLayout"
            app:layout_constraintStart_toStartOf="parent">

            <com.mikhaellopez.circularimageview.CircularImageView
                android:id="@+id/logo"
                android:layout_width="@dimen/icons_size_height_weight_8"
                android:layout_height="@dimen/icons_size_height_weight_8"
                android:scaleType="centerInside"
                app:civ_border_color="@color/gray"
                app:civ_border_width="1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:srcCompat="@tools:sample/avatars" />

            <TextView
                android:id="@+id/tvBranchName"
                style="@style/label_item_store_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="4dp"
                android:ellipsize="end"
                android:textAlignment="viewStart"
                android:maxLines="1"
                app:layout_constraintBottom_toBottomOf="@+id/logo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/logo"
                app:layout_constraintTop_toTopOf="@+id/logo"
                tools:text="branch name" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <include
            android:id="@+id/btnOrder"
            layout="@layout/btn_progress_circle"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <LinearLayout
            android:id="@+id/quantity_cont"
            android:layout_width="match_parent"
            android:visibility="visible"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/btnOrder"
            app:layout_constraintTop_toTopOf="@id/btnOrder">
            <include
                android:id="@+id/quantity_controller"
                layout="@layout/include_input_quantity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>