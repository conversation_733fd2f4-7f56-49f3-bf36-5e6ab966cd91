package com.techcubics.albarkahyper.ui.adapters.product

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemRateBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.RatesViewHolder
import com.techcubics.data.model.pojo.Rates

class RatesAdapter(var context: Context, var rates: List<Rates>) :
    RecyclerView.Adapter<RatesViewHolder>() {


    fun updateRates(rates: List<Rates>) {
        notifyItemRangeRemoved(0, itemCount)
        this.rates = rates
        notifyItemRangeInserted(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RatesViewHolder {
        return RatesViewHolder(
            context,
            ItemRateBinding.inflate(LayoutInflater.from(context), parent, false)
        )
    }

    override fun onBindViewHolder(holder: RatesViewHolder, position: Int) {
        holder.setData(rates[position])
    }

    override fun getItemCount(): Int {
        return rates.size
    }
}
