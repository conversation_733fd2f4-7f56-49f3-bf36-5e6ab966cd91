package com.techcubics.data.repos.chat

import com.techcubics.data.model.pojo.LiveChatRoomData
import com.techcubics.data.model.pojo.LiveMessageData
import com.techcubics.data.model.pojo.SendSupportData
import com.techcubics.data.model.pojo.SupportTicketData
import com.techcubics.data.model.requests.TicketSendCallRequest
import com.techcubics.data.model.requests.chat.LiveMessagesRequest
import com.techcubics.data.model.requests.chat.SendLiveRequest
import com.techcubics.data.model.requests.chat.SupportSendRequest
import com.techcubics.data.model.requests.chat.SupportTicketMessagesRequest
import com.techcubics.data.remote.BaseResponse

interface LiveChatRepo {

    suspend fun getHistory(request: LiveMessagesRequest): BaseResponse<MutableList<LiveMessageData>>?

    suspend fun send(request: SendLiveRequest): BaseResponse<Any>?

    suspend fun liveGetAllChats(): BaseResponse<MutableList<LiveChatRoomData>>?

}