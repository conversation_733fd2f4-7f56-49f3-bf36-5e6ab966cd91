package com.techcubics.albarkahyper.ui.adapters.profile

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Regoin
import com.techcubics.albarkahyper.databinding.ItemLanguageBinding
import com.techcubics.albarkahyper.ui.adapters.holders.profile.RegionHolderItem


class RegionAdapter(
    private val context: Context,
    private val itemName: List<Regoin>,
    private val popupMenuItemLiveData: MutableLiveData<Regoin?>,
    private val dialog: Dialog
):
    RecyclerView.Adapter<RegionHolderItem>(){

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RegionHolderItem {

        val itemBinding = ItemLanguageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return RegionHolderItem(itemBinding)
    }


    @SuppressLint("ResourceAsColor")
    override fun onBindViewHolder(holder: RegionHolderItem, position: Int) {
        holder.itemName.text = itemName.get(position).name
        holder.itemView.setOnClickListener {
            popupMenuItemLiveData.value = itemName.get(position)
            dialog.dismiss()
        }

    }

    override fun getItemCount() = itemName.size

}