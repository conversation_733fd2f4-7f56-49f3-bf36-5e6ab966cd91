package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mywallet

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentVodafonCashBinding
import com.techcubics.albarkahyper.common.ProgressButton

class VodafonCashFragment : Fragment() {


    lateinit var binding : FragmentVodafonCashBinding
    lateinit var progressButton: ProgressButton
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentVodafonCashBinding.inflate(layoutInflater,container,false)
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.signBtnProgress)
        initViews()
        return binding.root
    }

    private fun initViews() {
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.vodafon_cash_num)
        progressButton.binding.textView.text = getString(com.techcubics.style.R.string.save)

        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }


    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}