package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class StoreDetailsBranchesData(
    @SerializedName("id")
    val id:Int,
    @SerializedName("branch_id")
    val branch_id:Int,
    @SerializedName("name")
    val name:String,
    @SerializedName("address")
    val address:String,
    @SerializedName("phone")
    val phone:String,
    @SerializedName("email")
    val email:String,
    @SerializedName("times_of_week")
    val workingTimes:MutableList<StoreDetailsBranchesTime>,
    @SerializedName("logo")
    val logo:String,

)



