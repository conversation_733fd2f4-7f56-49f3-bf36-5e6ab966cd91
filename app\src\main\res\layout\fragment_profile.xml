<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/profile_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_31">

    <TextView
        android:id="@+id/myprofile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="16dp"
        android:text="@string/my_profile"
        android:textAppearance="@style/header_profile"
        android:textColor="@color/blue_black"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/myprofile">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="48dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/profile_data"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/orderToolbar">

                    <de.hdodenhof.circleimageview.CircleImageView
                        android:id="@+id/user_photo"
                        android:layout_width="75dp"
                        android:layout_height="75dp"
                        android:layout_marginStart="14dp"
                        android:src="@drawable/portrait_placeholder"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="9dp"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/user_photo"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="User Name"
                            android:textAppearance="@style/label_userdata_profile"
                            android:textSize="15dp" />

                        <TextView
                            android:id="@+id/shop_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="mm"
                            android:textAppearance="@style/label_userdata_profile"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/profile"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/indian_red"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_me" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/profile"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/language"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:backgroundTint="@color/black"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_lang" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/language"
                            android:textAppearance="@style/label_profile" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/country"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/teal_200"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_city" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/country"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/addresses"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="16dp"
                        android:paddingTop="10dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/salmon"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_home_fill" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/addresses"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:id="@+id/user_services"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/mypurchases"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/color_59"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_mypurchases" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="start"
                            android:text="@string/mypurchases"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/chat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/dark_violet"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_chat" />
                        </androidx.cardview.widget.CardView>


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/chat"
                            android:textAppearance="@style/label_profile" />

                        <TextView
                            android:id="@+id/chatUnReadNo"
                            style="@style/label_redcircle_profile"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_vertical"
                            android:background="@drawable/circle_red"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:visibility="gone"
                            tools:text="1" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/support"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/purple_700"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_support_chat" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/support"
                            android:textAppearance="@style/label_profile" />

                        <TextView
                            android:id="@+id/supportUnReadNo"
                            style="@style/label_redcircle_profile"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_vertical"
                            android:background="@drawable/circle_red"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:visibility="gone"
                            tools:text="1" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/my_wallet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/teal_200"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_wallet" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/my_wallet"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/setting"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/pyellow"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_settings" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/setting"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                    <LinearLayout
                        android:id="@+id/sign_in_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="8dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/purple_700"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_logout" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:id="@+id/sign_in"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/signin"
                            android:textAppearance="@style/label_profile"
                            android:textColor="@color/app_color" />
                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:id="@+id/co_link"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/website"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="8dp"
                        android:foreground="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/ic_lang"
                        android:backgroundTint="@color/pgreen" />

                    <ImageView
                        android:id="@+id/facebook"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="4dp"
                        android:foreground="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_gravity="center"
                        android:background="@drawable/ic_facebook" />
                    <ImageView
                        android:id="@+id/whatsapp"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:foreground="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/ic_whatsapp" />
                    <TextView
                        android:id="@+id/developed_by"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:gravity="start"
                        android:textAlignment="viewStart"
                        android:layout_gravity="center_vertical"
                        android:text="@string/developed_by_techcubics"
                        android:textAppearance="@style/label_profile" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/includeNetwork"
        layout="@layout/include_no_network_layout"
        android:visibility="gone" />


    <include
        android:id="@+id/page_loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
