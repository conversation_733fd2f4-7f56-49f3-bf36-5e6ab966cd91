package com.techcubics.albarkahyper.ui.adapters.product

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemProductImageBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductImageViewHolder
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.data.model.pojo.Image
import java.util.regex.Matcher
import java.util.regex.Pattern

class ProductsImagesAdapter(
    val context: Context,
    var images: List<Image>,
    private var video: String?,
    val viewModel: MainViewModel?,
    val youtubeSizeChangeListener: OnItemsChangedListener?
) :
    RecyclerView.Adapter<ProductImageViewHolder>() {

    constructor(context: Context, images: List<Image>)
            : this(context, images, null, null, null)

    fun updateImagesAndVideo(images: List<Image>, video: String?) {
        notifyItemRangeRemoved(0, itemCount)
        this.images = images
        this.video = video
        notifyItemRangeInserted(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductImageViewHolder {
        return ProductImageViewHolder(
            context,
            ItemProductImageBinding.inflate(
                LayoutInflater.from(context),
                parent,
                false
            ),
            youtubeSizeChangeListener,
            viewModel
        )
    }

    override fun onBindViewHolder(holder: ProductImageViewHolder, position: Int) {
        if (video != null && position >= images.size) {
            holder.setVideo(extractVideoId())
        } else {
            holder.setData(images[position])
        }
    }

    private fun extractVideoId(): String {
        val pattern = "(?<=youtu.be/|watch\\?v=|/videos/|embed/)[^#&?]*"
        val compiledPattern: Pattern = Pattern.compile(pattern)
        val matcher: Matcher = compiledPattern.matcher(video!!)
        return if (matcher.find()) {
            matcher.group()
        } else {
            "error"
        }
    }

    override fun getItemCount(): Int {
        return if (video != null) {
            images.size + 1
        } else {
            images.size
        }

    }
}
