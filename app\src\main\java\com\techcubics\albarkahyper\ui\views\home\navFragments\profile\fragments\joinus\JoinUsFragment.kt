package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.joinus

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.location.Geocoder
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.core.view.isEmpty
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.widget.Autocomplete
import com.google.android.libraries.places.widget.AutocompleteActivity
import com.google.android.libraries.places.widget.model.AutocompleteActivityMode
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentJoinUsBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Governerate
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.pojo.Regoin
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.requests.profile.JoinusRequest
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants.furniture
import com.techcubics.shared.utils.AuthUtils
import kotlinx.coroutines.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.DecimalFormat
import java.util.*

class JoinUsFragment : Fragment(), OnMapReadyCallback {


    lateinit var binding: FragmentJoinUsBinding
    lateinit var progressButton: ProgressButton
    private var branchesTypeList: MutableLiveData<ArrayList<StoreTypes>> =
        MutableLiveData<ArrayList<StoreTypes>>()
    private var branchTypeIdList : ArrayList<Int> = arrayListOf()
    private lateinit var popupDialog: PopupDialog

    private var governerateList: MutableLiveData<ArrayList<Governerate>> =
        MutableLiveData<ArrayList<Governerate>>()
    private var regionList: MutableLiveData<ArrayList<Regoin>> =
        MutableLiveData<ArrayList<Regoin>>()

    private var selectedCountryId = 0
    private var selectedGovernerateId = 0
    private var selectedRegionId = 0
    private var mapFragment: SupportMapFragment? = null
    private var googleMap: GoogleMap? = null
    private lateinit var choosenLocation: Latlng
    private lateinit var currentLocation: Latlng
    private var location: Latlng? = null
    private var checkGovCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkRegionCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkCountryCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentJoinUsBinding.inflate(layoutInflater, container, false)

        mapFragment =
            this.childFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?

        mapFragment?.getMapAsync(this)
        val ai: ApplicationInfo = requireContext().packageManager
            .getApplicationInfo(requireContext().packageName, PackageManager.GET_META_DATA)
        val value = ai.metaData["com.google.android.geo.API_KEY"]
        val placekey = value.toString()
        Log.i("here",placekey)
        Places.initialize(requireContext(), placekey)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.signBtnProgress)
        initViews()
        observeViews()
        return binding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                if (it.message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.joinusLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }
        profileViewModel.countriesPopupMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                binding.country.setText(it.name)
                profileViewModel.getGovernerateByCountryId(it.country_id)
                selectedCountryId = it.country_id
                profileViewModel.countriesPopupMenuItemLiveData.value = null
            }
        }
        profileViewModel.listOfgovernerateMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    governerateList.postValue(it.data!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
                profileViewModel.listOfgovernerateMutableLiveData.value = null
            }
        }
        profileViewModel.governeratesMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.governerate.setText(it.name)
                profileViewModel.getRegionBygovernorateId(
                    it.governorate_id,
                    sharedPreferencesManager.getCountryID().toInt()
                )
                selectedGovernerateId = it.governorate_id
                profileViewModel.governeratesMutableLiveData.value = null
            }
        }
        profileViewModel.listOfregionsMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    regionList.postValue(it.data!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
            }

        }
        profileViewModel.regionPopupMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                binding.region.setText(it.name)
                selectedRegionId = it.regionID
                profileViewModel.regionPopupMenuItemLiveData.value = null
            }
        }
        profileViewModel.branchTypeMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }else
                    branchesTypeList.postValue(it.data?.types!! as ArrayList<StoreTypes>?)
                profileViewModel.branchTypeMutableLiveData.value = null
            }
        }
        profileViewModel.branchTypePopMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                var shoptypeName = ""
                for (store in it){
                    Log.i("clothes","observer"+store.toString())
                    shoptypeName += store?.name + " "
                    branchTypeIdList.add(store?.branchTypeID!!)
                }
                binding.gelleryType.setText(shoptypeName)
                profileViewModel.branchTypePopMenuItemLiveData.value = null
            }
        }
        profileViewModel.joinusMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    progressButton.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.send_details),null)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    progressButton.btnFinishedFailed(getString(com.techcubics.style.R.string.send_details),null)
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else {
                    progressButton.btnFinishedFailed(getString(com.techcubics.style.R.string.send_details),null)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.joinusMutableLiveData.value = null
            }
        }

        governerateList.observe(viewLifecycleOwner) {
            if (it != null) {
                checkGovCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Log.i("clothes","clickedgov"+it)
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                binding.actionLoadingAnimation.root
                            )
                            popupDialog.showGovernerateDialog(
                                requireContext(),
                                R.layout.dialog_language,
                                governerateList.value!!,
                                profileViewModel.governeratesMutableLiveData
                            )
                        }
                        checkGovCLicked.value = null
                    }
                }

            }
        }
        regionList.observe(viewLifecycleOwner) {
            if (it != null) {
                checkRegionCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                binding.actionLoadingAnimation.root
                            )
                            popupDialog.showRegionDialog(
                                requireContext(), R.layout.dialog_language,
                                regionList.value!!, profileViewModel.regionPopupMenuItemLiveData
                            )
                        }
                        checkRegionCLicked.value = null
                    }
                }

            }
        }
        profileViewModel.countriesMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                val countryList = it.data
                checkCountryCLicked.observe(viewLifecycleOwner){
                    if(it != null){
                        if(it){
                            Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
                            popupDialog.showCounrtyDialog(
                                requireContext(),
                                R.layout.dialog_country,
                                countryList,
                                profileViewModel.countriesPopupMenuItemLiveData as MutableLiveData<Any?>,
                                Constants.JOIN
                            )
                        }
                        checkCountryCLicked.value = null
                    }
                }
            }
        }

    }

    private fun initViews() {
        checkAutherization()
        profileViewModel.storeTypesCall()
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.join_us)
        progressButton.binding.textView.text = getString(com.techcubics.style.R.string.send_details)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }

        Helper.loadingAnimationVisibility(View.VISIBLE,binding.actionLoadingAnimation.root)
//        profileViewModel.getCountries()

        binding.region.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)
        binding.governerate.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)
        binding.country.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)

        Log.i("clothes", sharedPreferencesManager.getCountryCode())
        binding.countryPicker.setDefaultCountryUsingNameCode(sharedPreferencesManager.getCountryCode())
        binding.countryPicker.resetToDefaultCountry()
        binding.countryPicker.setCcpClickable(false)

        //address clicked to show search
        binding.address.keyListener = null

        binding.address.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.address.callOnClick()
            }
        }

        binding.address.setOnClickListener {
            val fieldList =
                listOf(Place.Field.ADDRESS, Place.Field.LAT_LNG, Place.Field.NAME)
            val intent = Autocomplete.IntentBuilder(AutocompleteActivityMode.OVERLAY, fieldList)
                .build(requireActivity())
            startActivityForResult(intent, 100)
        }

        binding.country.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            checkCountryCLicked.postValue(true)
            profileViewModel.getCountries()
            binding.governerate.isEnabled = true
            binding.governerate.isClickable = true
            binding.governerate.isLongClickable = true
        }
        binding.governerate.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            checkGovCLicked.postValue(true)
            profileViewModel.getGovernerateByCountryId(selectedCountryId)
            binding.region.isEnabled = true
            binding.region.isClickable = true
            binding.region.isLongClickable = true
        }
        binding.region.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            checkRegionCLicked.postValue(true)
            profileViewModel.getRegionBygovernorateId(
                selectedGovernerateId,
                selectedCountryId
            )

        }
        binding.gelleryType.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.actionLoadingAnimation.root)
            branchesTypeList.observe(viewLifecycleOwner) {
                Helper.loadingAnimationVisibility(View.GONE,binding.actionLoadingAnimation.root)
                if (it != null) {
                    popupDialog.multiChoiceDialog(requireContext(),R.layout.dialog_shoptype,
                        branchesTypeList.value!!, profileViewModel.branchTypePopMenuItemLiveData)
                }
            }

        }

        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            if (checkUiValidity()) {
                progressButton.btnActivated()

                val notes: String = binding.additionalNotes.text.toString()
                val socialAccount: String = binding.socialAccount.text.toString()
                val email: String = binding.email.text.toString()
                val addressText = binding.address.text.toString()
                location = choosenLocation

                profileViewModel.joinus(
                    JoinusRequest(
                        binding.galleryName.text.toString(),
                        binding.bCount.text.toString().toInt(),
                        branchTypeIdList,
                        selectedCountryId.toString().toInt(),
                        selectedGovernerateId.toString().toInt(),
                        selectedRegionId.toString().toInt(),
                        location?.lat ?: sharedPreferencesManager.getCurrentLatlng().lat,
                        location?.lng ?: sharedPreferencesManager.getCurrentLatlng().lng,
                        binding.ownerName.text.toString(),
                        binding.phoneEdt.text.toString(),
                        email,
                        "time",
                        notes,
                        socialAccount,
                        addressText
                    )
                )
            }
        }


    }

    override fun onMapReady(p0: GoogleMap) {
        Log.i("here", "onMapReady")
        googleMap = p0
        googleMap?.uiSettings?.isZoomControlsEnabled = true

        currentLocation = sharedPreferencesManager.getCurrentLatlng()
        val currentLatLng =
            LatLng(roundToSevenDigits(currentLocation.lat.toDouble()).toDouble(),roundToSevenDigits(currentLocation.lng.toDouble()).toDouble())
        choosenLocation = currentLocation
        binding.address.setText(getAddress(currentLatLng.latitude, currentLatLng.longitude))
        placeMarker(currentLatLng)


    }

    private fun addLocationMarker(currentLatLng: LatLng) {
        if (googleMap != null) {
            val markerOption = MarkerOptions().position(currentLatLng)
                .title(getString(com.techcubics.style.R.string.choosen_location))
                .snippet(getAddress(currentLatLng.latitude, currentLatLng.longitude))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLng(currentLatLng))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0f))
            val currentMarker = googleMap?.addMarker(markerOption)
            currentMarker?.showInfoWindow()
        }
    }

    private fun placeMarker(currentLatLng: LatLng) {
        Helper.loadingAnimationVisibility(View.GONE,binding.actionLoadingAnimation.root)
        if (googleMap != null) {
            if (ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            googleMap?.isMyLocationEnabled = true
            googleMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0F))

        }

    }

    private fun getAddress(lat: Double, lng: Double): String {
        var returnAddress = ""
        runBlocking {
            if (<EMAIL> != null) {
                val geocoder =
                    Geocoder(requireContext(), Locale.getDefault())
                val address = geocoder.getFromLocation(lat, lng, 1)
                val addressJob = async { address?.get(0)?.getAddressLine(0).toString() }
                returnAddress = addressJob.await()

            }

        }
        return returnAddress
    }
    private fun roundToSevenDigits(value: Double): String {
        val df = DecimalFormat("##.#######").format(value)
        return convertArabic(df)
    }

    private fun convertArabic(arabicStr: String): String {
        val chArr = arabicStr.toCharArray()
        val sb = StringBuilder()
        for (ch in chArr) {
            if (Character.isDigit(ch)) {
                sb.append(Character.getNumericValue(ch))
            }else if (ch == '٫'){
                sb.append(".")
            }

            else {
                sb.append(ch)
            }
        }
        return sb.toString()
    }


    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == Activity.RESULT_OK) {

            //init place
            val place = data?.let { Autocomplete.getPlaceFromIntent(it) }

            //set Address on edittext
            place?.let {
                binding.address.setText(place.name)
                place.latLng?.let {
                        latlng -> addLocationMarker(latlng)
                    choosenLocation = Latlng(roundToSevenDigits(latlng.latitude),roundToSevenDigits(latlng.longitude))
                    Log.i("here",latlng.toString()+choosenLocation)
                }
            }


        } else if (resultCode == AutocompleteActivity.RESULT_ERROR) {

            //init status
            val status = data?.let { Autocomplete.getStatusFromIntent(it) }
            Log.i("here", "status" + status?.statusMessage)
        }
    }

    override fun onPause() {
        super.onPause()
        googleMap?.clear()
    }

    private fun checkUiValidity(): Boolean {

        var check = true

        if (binding.phoneEdt.text.isNullOrEmpty() || !(AuthUtils.validatePhone(
                binding.phoneEdt.text.toString(),
                binding.countryPicker.selectedCountryCode
            ))
        ) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.phone_not_correct))
            check = false
        } else if (binding.galleryName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_check_gallery_name))
            check = false
        } else if (binding.bCount.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_check_branches_count))
            check = false
        } else if (binding.country.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_Check_country))
            check = false
        } else if (binding.governerate.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_Check_governorate))
            check = false
        } else if (binding.region.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_Check_region))
            check = false
        } else if (binding.ownerName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.enter_valid_name))
            check = false
        } else if (binding.countryPicker.isEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.choose_country_code))
            check = false
        } else if (binding.address.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_add_address))
            check = false
        }


        return check
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }





    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}