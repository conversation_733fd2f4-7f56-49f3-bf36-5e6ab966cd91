package com.techcubics.albarkahyper.ui.adapters.home

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.ItemOffersBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.OfferHolderItem
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler

class OffersAdapter<T>(val margin:Int=0,private val clickHandler: IOnAdapterItemClickHandler): PagingAdapter() {

    lateinit var items:List<T>
    lateinit var itemBinding:ItemOffersBinding

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OfferHolderItem<T> {
         itemBinding = ItemOffersBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OfferHolderItem(itemBinding,parent.context,margin,clickHandler)
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as OfferHolderItem<T>
        holder.bind(items.get(position))
        holder.itemView.setOnLongClickListener(object  : View.OnLongClickListener{
            override fun onLongClick(p0: View?): Boolean {
                Log.i("here","lc")
                p0?.stopNestedScroll()
                return true
            }

        })
    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_offers
    }

    override fun getPagingItemCount(): Int {
       return items.size
    }
}