package com.techcubics.data.repos

import com.techcubics.data.remote.BaseResponse
import com.techcubics.shared.constants.Constants
import org.json.JSONObject
import retrofit2.HttpException
import retrofit2.Response
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLHandshakeException
import javax.net.ssl.SSLPeerUnverifiedException

interface RepositoryResponse {
    fun <T> getServerErrorResponse(): BaseResponse<T> {
        return BaseResponse(null, Constants.SERVER_ERROR, false)
    }
//    val <T: View> T.propName: Unit
//        get() = Unit
//
//    val <T> RepositoryResponseImpl.serverErrorResponse:BaseResponse<T>
//        get() =BaseResponse(null, Constants.SERVER_ERROR, false)

    fun <T> baseResponse(response: Response<BaseResponse<T>>?): BaseResponse<T>? {
        return when (response?.code()) {
            200 -> response.body()
            422 -> {
                val jsonObj = JSONObject(response.errorBody()?.charStream()!!.readText())
                BaseResponse(
                    message = handleMessageObject(jsonObj.getString("message")),
                    status = false
                )
            }
            500->{
                getServerErrorResponse()
            }
            else -> BaseResponse(message = response?.message()!!, status = false)
        }

    }
    fun<T> handleServerExceptions(ex:Exception):BaseResponse<T>{
        when(ex) {
            is UnknownHostException,
            is SocketTimeoutException,
            is SSLHandshakeException,
            is ConnectException,
            is HttpException,
            is SSLPeerUnverifiedException -> {
               return getServerErrorResponse()
            }
            else -> throw ex
        }
    }
    fun handleMessageObject(msg: String): String {
        var localmsg = msg
        localmsg = localmsg.replace("{", "")
        localmsg = localmsg.replace("}", "")
        localmsg = localmsg.replace("[", "")
        localmsg = localmsg.replace("]", "")
        localmsg = localmsg.replace("\"", "")
        localmsg = localmsg.replace(",", "\n")
        return localmsg
    }
}