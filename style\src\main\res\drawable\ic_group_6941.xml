<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="128dp"
    android:height="163dp"
    android:viewportWidth="128"
    android:viewportHeight="163">
  <path
      android:pathData="M24.81,58.45L11.84,54.22L10.69,51.17L8.06,51.09L3.28,50.18L3.85,51.17L9.87,55.54L11.35,56.28L24.91,63.36L24.81,58.45Z"
      android:strokeWidth="1.00396"
      android:fillColor="#ffffff"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M10.69,51.17L9.46,50.43L6.99,51.34L7.4,51.83L10.69,51.17Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.00396"
      android:fillColor="#ffffff"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M24.81,58.45L11.84,54.22L10.69,51.17L8.06,51.09L3.28,50.18L3.85,51.17L9.87,55.54L11.35,56.28L24.91,63.36L24.81,58.45Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M24.81,58.45L11.84,54.22L10.69,51.17L8.06,51.09L3.28,50.18L3.85,51.17L9.87,55.54L11.35,56.28L24.91,63.36L24.81,58.45Z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1.00396"
      android:fillColor="#D392E3"
      android:strokeColor="#263238"
      android:fillAlpha="0.5"/>
  <path
      android:pathData="M24.81,58.45L11.84,54.22L10.69,51.17L8.06,51.09L3.28,50.18L3.85,51.17L9.87,55.54L11.35,56.28L24.91,63.36L24.81,58.45Z"
      android:strokeWidth="1.00396"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M10.69,51.17L9.46,50.43L6.99,51.34L7.4,51.83L10.69,51.17Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M10.69,51.17L9.46,50.43L6.99,51.34L7.4,51.83L10.69,51.17Z"
      android:strokeAlpha="0.5"
      android:strokeLineJoin="round"
      android:strokeWidth="1.00396"
      android:fillColor="#D392E3"
      android:strokeColor="#263238"
      android:fillAlpha="0.5"/>
  <path
      android:pathData="M10.69,51.17L9.46,50.43L6.99,51.34L7.4,51.83L10.69,51.17Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.00396"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M27.56,30.99C27.56,30.99 27.36,32.76 27.86,33.9C28.36,35.03 31.13,38.89 31.13,38.89L25.95,43.04C25.95,43.04 23.8,38.71 23.39,38.01C22.99,37.31 22.49,34.29 23.83,32.13C25.16,29.97 27.08,29.39 27.56,30.99Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M24.9,36.68L27.15,41.34L25.95,43.04L23.69,39.01L24.9,36.68Z"
      android:strokeAlpha="0.5"
      android:fillColor="#D392E3"
      android:fillAlpha="0.5"/>
  <path
      android:pathData="M27.56,30.99C27.56,30.99 27.36,32.76 27.86,33.9C28.36,35.03 31.13,38.89 31.13,38.89L25.95,43.04C25.95,43.04 23.8,38.71 23.39,38.01C22.99,37.31 22.49,34.29 23.83,32.13C25.16,29.97 27.08,29.39 27.56,30.99Z"
      android:strokeWidth="1.00396"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M29.17,27.74C28.85,25.77 26.59,21.77 23.01,21.92C20.69,22.03 19.6,22.49 18.6,23.07C17.77,23.55 17.01,24.12 16.31,24.77C14.56,26.87 13.95,27.45 14.29,29.05C14.26,29.69 14.36,30.33 14.58,30.93C15.12,32.36 15.49,33.3 15.95,33.98C15.81,34.64 15.44,36.4 15.42,36.8C15.4,37.2 16.6,36.92 17.21,36.77C18.33,39.29 20.4,41.04 21.04,41.24C22.15,41.59 23.92,39.75 24.86,38.32C25.8,36.9 25.09,35.3 25.09,34.66C25.1,34.02 27.2,32.62 27.98,32.17C28.76,31.73 29.48,29.71 29.17,27.74Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M14.31,29.05C14.28,29.69 14.38,30.33 14.6,30.93C15.14,32.36 15.5,33.3 15.97,33.98C15.82,34.64 15.45,36.4 15.44,36.8C15.42,37.2 16.62,36.92 17.22,36.77C18.35,39.29 20.41,41.04 21.05,41.24C22.17,41.59 23.94,39.75 24.87,38.32C25.11,37.97 25.26,37.56 25.32,37.14C25.27,36.96 25.23,36.84 25.23,36.84C25.23,36.84 24.71,38.87 23.66,38.61C22.61,38.34 21.01,36.25 20.12,35.52C19.22,34.8 19.59,33.84 19.41,32.77C19.23,31.69 16.59,30.74 16.41,30.12C16.23,29.5 16.85,26.94 16.85,26.94C16.85,26.94 17.86,25.6 17.64,26.16C17.49,26.63 17.43,27.14 17.48,27.64C17.52,28.14 17.67,28.62 17.9,29.07C18.07,29.41 18.33,29.71 18.66,29.91C18.99,30.11 19.37,30.22 19.76,30.21L20.72,25.45L18.69,23.77L16.33,24.76C14.56,26.87 13.96,27.45 14.31,29.05Z"
      android:strokeAlpha="0.5"
      android:fillColor="#D392E3"
      android:fillAlpha="0.5"/>
  <path
      android:strokeWidth="1"
      android:pathData="M29.17,27.74C28.85,25.77 26.59,21.77 23.01,21.92C20.69,22.03 19.6,22.49 18.6,23.07C17.77,23.55 17.01,24.12 16.31,24.77C14.56,26.87 13.95,27.45 14.29,29.05C14.26,29.69 14.36,30.33 14.58,30.93C15.12,32.36 15.49,33.3 15.95,33.98C15.81,34.64 15.44,36.4 15.42,36.8C15.4,37.2 16.6,36.92 17.21,36.77C18.33,39.29 20.4,41.04 21.04,41.24C22.15,41.59 23.92,39.75 24.86,38.32C25.8,36.9 25.09,35.3 25.09,34.66C25.1,34.02 27.2,32.62 27.98,32.17C28.76,31.73 29.48,29.71 29.17,27.74Z"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:strokeWidth="1"
      android:pathData="M16.44,32.79L16.83,33.91C16.83,33.91 17.68,34.19 18.71,33.07"
      android:fillColor="#00000000"
      android:strokeColor="#263238"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M19.35,38.05C19.35,38.05 20.28,37.95 20.66,36.64"
      android:fillColor="#00000000"
      android:strokeColor="#263238"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M15.78,31.85C15.78,31.85 16.06,31 17.75,31"
      android:fillColor="#00000000"
      android:strokeColor="#263238"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M24.44,20.36C23.21,20.18 21.97,20.09 20.73,20.07C20.73,20.07 17.03,17.94 13.46,20.22C9.9,22.49 11.61,26.49 11.61,27.48C11.61,28.48 10.47,28.48 11.18,29.77C11.9,31.05 14.3,30.34 14.3,30.34C14.33,29.86 14.46,29.38 14.68,28.95C14.9,28.52 15.21,28.14 15.59,27.84C16.88,26.84 17.9,26.63 18.32,26.92C18.32,26.92 18.04,29.05 18.47,29.91C18.47,29.91 19.89,30.62 20.32,30.62C20.75,30.62 20.04,31.22 21.46,31.76C21.72,31.85 22,31.88 22.27,31.85C22.54,31.82 22.81,31.72 23.04,31.57C23.24,30.71 23.7,29.4 24.55,29.71C25.8,30.15 26.94,33.22 25.25,34.27C27.28,34.64 29.74,31.05 29.89,27.77C30.03,24.49 26.55,20.73 24.44,20.36Z"
      android:fillColor="#263238"/>
  <path
      android:pathData="M18.39,25.51C18.39,25.51 19.43,24.95 20.72,26.15C22,27.36 24.5,28.97 27.8,28.08"
      android:strokeLineJoin="round"
      android:strokeWidth="0.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M18.24,24.55C18.24,24.55 18.96,23.42 20.17,23.42C21.37,23.42 24.19,25.11 26.43,25.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M17.42,26.71C17.42,26.71 16.06,25.03 14.77,26.4C13.49,27.76 14.3,29.05 14.3,29.05"
      android:strokeLineJoin="round"
      android:strokeWidth="0.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11.96,27.84C12.75,27.13 13.23,26.16 13.32,25.11C13.49,23.34 15.9,21.81 17.42,23.26"
      android:strokeLineJoin="round"
      android:strokeWidth="0.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M14.45,21.41C14.45,21.41 16.86,19.08 18.87,22.05"
      android:strokeLineJoin="round"
      android:strokeWidth="0.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M34.09,135.99C34.09,135.99 35.57,138.3 35.41,139.78L35.25,141.27C35.25,141.27 29.8,142.76 28.65,142.76C27.5,142.76 21.39,142.18 21.39,142.18L21.13,141.13C21.11,141.06 21.12,140.98 21.15,140.9C21.19,140.83 21.24,140.77 21.32,140.74L26.76,138.2L28.48,136.32L34.09,135.99Z"
      android:strokeWidth="1.00396"
      android:fillColor="#ffffff"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M29.48,141.6C30.95,141.71 33.76,140.81 35.38,140.24L35.27,141.27C35.27,141.27 29.8,142.76 28.65,142.76C27.5,142.76 21.39,142.18 21.39,142.18L21.2,141.43C22.18,141.44 27.52,141.45 29.48,141.6Z"
      android:strokeWidth="1.00396"
      android:fillColor="#D392E3"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M26.77,138.2L18.84,141.65C18.84,141.65 18.17,143.35 18.84,143.68C19.52,144.02 34.28,143.68 34.28,143.68C34.28,143.68 35.3,141 33.69,139.21C32.08,137.41 26.77,138.2 26.77,138.2Z"
      android:strokeWidth="1.00396"
      android:fillColor="#ffffff"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M34.28,143.68C34.28,143.68 19.52,144.02 18.84,143.68C18.52,143.52 18.51,143.04 18.58,142.59H34.54C34.49,142.96 34.4,143.33 34.28,143.68Z"
      android:strokeWidth="1.00396"
      android:fillColor="#D392E3"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M23.02,78.28C23.02,78.28 20.95,87.46 20.17,93.2C19.38,98.94 18.92,110.44 19.09,111.76C19.25,113.07 27.5,135.17 27.5,135.17L28.32,137.14L34.09,135.99C34.09,135.99 31.45,127.58 30.46,122.96C29.48,118.34 27.32,110.76 27.32,109.44C27.32,108.12 32.43,82.93 32.8,81.28C33.16,79.63 34.83,77.09 34.83,77.09L23.78,76.86L23.02,78.28Z"
      android:strokeWidth="1.00396"
      android:fillColor="#37474F"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M23.02,78.28C23.02,78.28 21.81,86.66 22.31,92.49C22.81,98.32 23.88,110.72 23.88,112.57C23.88,114.41 26.86,125.44 27.03,128.9C27.3,133.91 26.93,136.38 26.93,136.38L26.75,138.2C26.75,138.2 30.22,138.69 30.98,138.91C31.86,139.14 32.77,139.23 33.67,139.2C33.67,139.2 34.94,133.64 34.94,128.9C34.94,124.16 33.89,114.4 33.36,110.45C32.84,106.5 34.49,91.25 35.42,88.41C36.36,85.56 38.41,79.54 37.03,76.46C35.65,73.38 23.02,78.28 23.02,78.28Z"
      android:strokeWidth="1.00396"
      android:fillColor="#37474F"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M38.56,74.91L38.44,78.66C38.44,78.66 36.19,79.11 33.17,79.63C30.67,80.06 24.41,79.52 22.3,79.31C22.1,79.29 21.93,79.2 21.8,79.05C21.68,78.91 21.62,78.72 21.63,78.52L21.84,75.14L38.56,74.91Z"
      android:fillColor="#263238"/>
  <path
      android:pathData="M29.79,37.56C29.79,37.56 26.25,41.39 24.91,43.77C23.58,46.15 23.05,49.8 22.55,52.67C22.05,55.55 23.41,62.29 23.03,65.39L21.82,75.15C21.82,75.15 21.56,75.81 22.43,76.54C23.3,77.26 27.54,77.52 31.38,77.72C32.78,77.79 36.97,76.89 37.65,76.84C38.48,76.79 38.51,75.74 38.54,74.91C38.71,71.03 39.41,60.67 39.87,56.16C40.82,46.96 35.63,38.08 29.79,37.56Z"
      android:strokeWidth="1.00396"
      android:fillColor="#ffffff"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M39.44,61.39C39.59,59.35 39.75,57.49 39.89,56.16C39.93,55.77 39.95,55.37 39.97,54.98L30.56,56.55L22.47,53.8C22.47,55.47 22.57,57.14 22.76,58.81L30.69,61.03L39.44,61.39Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M29.44,37.94C28.91,38.54 27.84,39.74 26.83,41.03L32.81,47.1L34.78,46.31L29.44,37.94Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M29.79,37.56C29.79,37.56 26.25,41.39 24.91,43.77C24.52,44.49 24.2,45.25 23.96,46.04L31.08,37.81C30.66,37.68 30.23,37.6 29.79,37.56Z"
      android:fillColor="#263238"/>
  <path
      android:pathData="M22.83,51.12L30.56,53.66L39.95,52.59C39.8,50.25 39.28,47.95 38.41,45.77L30.84,48.81L24.07,45.74C23.52,47.5 23.1,49.3 22.83,51.12Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M36.14,71.03C32.22,71.69 28.19,71.36 24.43,70.07L22.54,69.43L21.96,74.15L24.11,74.75C28.19,75.89 32.46,76.22 36.68,75.72L38.41,75.51C38.5,75.32 38.55,75.11 38.56,74.9C38.61,73.85 38.69,72.32 38.8,70.57L36.14,71.03Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M37.19,64.6C33.27,65.26 29.24,64.93 25.48,63.64L23.08,62.81C23.14,63.67 23.13,64.53 23.04,65.39L22.76,67.66L25.16,68.33C29.24,69.47 33.51,69.8 37.72,69.3L38.9,69.16C39,67.58 39.12,65.91 39.24,64.25L37.19,64.6Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M0.26,47.95L7.87,54.07L8.2,53.66L0.58,47.54L0.26,47.95Z"
      android:fillColor="#263238"/>
  <path
      android:pathData="M33.46,41.8C33.46,41.8 36.25,43.7 36.31,48.2C36.34,50.16 36.13,52 35.68,55.23C35.23,58.45 32.42,68.95 30.23,70.23C28.04,71.5 24.64,69.09 24.64,69.09L8.21,58.18L4.24,54.98L2.27,51.26L2.89,50.08L7.74,53.79L5.13,51.38L5.64,50.73L6.58,51.39L9.34,52.42L9.94,56.36L10.79,56.94L26.51,62.68C26.51,62.68 26.31,54.22 26.46,52.61C26.6,50.99 26.8,46.92 28.54,43.97"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M26.4,56.29C26.4,59.21 26.5,62.68 26.5,62.68L10.79,56.96L9.94,56.38L9.34,52.44L6.59,51.41L5.64,50.74L5.13,51.39L7.74,53.79L2.89,50.08L2.3,51.24L4.27,54.96L8.24,58.18L9.24,58.85L30.95,65.84L26.4,56.29Z"
      android:strokeAlpha="0.5"
      android:fillColor="#D392E3"
      android:fillAlpha="0.5"/>
  <path
      android:pathData="M33.46,41.8C33.46,41.8 36.25,43.7 36.31,48.2C36.34,50.16 36.13,52 35.68,55.23C35.23,58.45 32.42,68.95 30.23,70.23C28.04,71.5 24.64,69.09 24.64,69.09L8.21,58.18L4.24,54.98L2.27,51.26L2.89,50.08L7.74,53.79L5.13,51.38L5.64,50.73L6.58,51.39L9.34,52.42L9.94,56.36L10.79,56.94L26.51,62.68C26.51,62.68 26.31,54.22 26.46,52.61C26.6,50.99 26.8,46.92 28.54,43.97"
      android:strokeWidth="1.00396"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M26.51,62.68L32.51,67.01L34.06,64.26C34.06,64.26 35.14,63.41 35.29,62.77C35.45,62.14 35.25,61.1 35.41,60.46C35.57,59.83 36.38,59.23 36.45,57.68C36.53,56.14 36.45,54.85 36.45,54.85C36.45,54.85 37.75,49.51 37.61,47.19C37.47,44.87 35.23,40.76 33.43,40.67C31.64,40.58 29.3,40.85 27.49,43.59C25.68,46.33 26.05,54.33 25.77,54.83C25.49,55.33 25.88,57.8 25.88,57.8C25.72,57.92 25.59,58.06 25.5,58.24C25.4,58.41 25.34,58.6 25.33,58.79C25.3,59.44 25.99,61.01 25.99,61.01L26.51,62.68Z"
      android:strokeWidth="1.00396"
      android:fillColor="#ffffff"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M25.93,52.77C25.94,53.46 25.89,54.15 25.77,54.83C25.52,55.27 25.81,57.3 25.87,57.72C29.25,58.76 32.81,59.03 36.31,58.5C36.38,58.23 36.43,57.95 36.44,57.66C36.52,56.12 36.44,54.83 36.44,54.83C36.44,54.83 36.55,54.36 36.71,53.63C33.09,53.86 29.46,53.57 25.93,52.77Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M27.25,44.03C26.68,45.16 26.37,46.85 26.19,48.57C29.76,49.94 33.62,50.41 37.41,49.94C37.56,49.03 37.63,48.11 37.61,47.18C37.54,46.46 37.37,45.74 37.1,45.06C33.79,45.14 30.47,44.79 27.25,44.03Z"
      android:fillColor="#D392E3"/>
  <path
      android:pathData="M25.99,61.06L26.5,62.68L32.5,67.01L34.05,64.26C34.05,64.26 35.13,63.41 35.28,62.77C35.31,62.66 35.33,62.55 35.33,62.43C32.17,62.38 29.03,61.92 25.99,61.06Z"
      android:fillColor="#263238"/>
  <path
      android:pathData="M30.33,138.86C30.33,138.86 30.46,135.45 31.12,130.47C31.77,125.48 30.2,118.4 29.94,117.22C29.67,116.04 30.07,113.42 29.81,112.5C29.54,111.58 28.27,107.65 29.02,103.09C29.77,98.53 30.38,86.48 30.38,86.48C30.38,86.48 28.49,84.94 27.93,82.94C27.38,80.94 27.8,80.31 27.8,80.31"
      android:strokeWidth="1.00396"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M31.21,80.49L30.48,84.52"
      android:strokeWidth="1.00396"
      android:fillColor="#00000000"
      android:strokeColor="#263238"/>
  <path
      android:pathData="M125.87,79.07C125.87,79.07 123.01,77.49 120.21,84.25C118.72,87.7 117.46,91.24 116.45,94.86C116.45,94.86 115.68,96.29 114.67,95.47C113.67,94.65 112.19,92.23 110.63,92.81C109.02,93.41 108.62,95.29 109.15,97.97C109.68,100.65 110.62,106.57 110.7,107.1C110.78,107.63 110.54,108.43 109.76,107.98C108.99,107.54 104.4,103.16 102.45,105.47C100.51,107.77 103.2,115 103.48,115.79C103.76,116.59 103.63,116.95 103.26,117.18C102.89,117.41 101.48,115.79 100.13,114.6C98.77,113.41 96.98,112.27 95.97,113.69C94.96,115.12 95.04,120.69 101.03,129.29L104.06,133.09L108.9,132.43C119.18,130.4 123.1,126.45 123.36,124.72C123.63,123 121.55,122.58 119.75,122.49C117.94,122.41 115.81,122.59 115.7,122.17C115.6,121.75 115.75,121.4 116.5,121.02C117.26,120.64 124.21,117.31 124.42,114.3C124.62,111.29 118.27,111.25 117.41,111.04C116.54,110.82 116.92,110.07 117.35,109.75C117.77,109.43 122.56,105.82 124.8,104.26C127.04,102.7 128.06,101.07 127.32,99.51C126.6,98.02 123.85,98.73 122.56,98.62C121.27,98.51 121.71,96.95 121.71,96.95C123.49,93.64 125.05,90.22 126.37,86.7C129.05,79.89 125.81,79.04 125.81,79.04"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M104.23,132.08C106.21,127.08 109.01,120.21 112.14,112.62C115.25,105.04 118.07,98.17 120.09,93.19L122.47,87.28L123.12,85.68L123.29,85.26C123.33,85.17 123.35,85.12 123.35,85.12C123.35,85.12 123.34,85.17 123.31,85.27L123.15,85.69L122.53,87.31C121.99,88.71 121.19,90.73 120.2,93.23C118.21,98.23 115.41,105.1 112.29,112.68C109.17,120.27 106.35,127.14 104.34,132.12"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M112.57,96.29C112.63,96.39 112.68,96.49 112.72,96.6C112.81,96.81 112.92,97.12 113.07,97.5C113.35,98.25 113.71,99.31 114.09,100.48L115.04,103.49C115.15,103.85 115.25,104.15 115.33,104.4C115.38,104.51 115.41,104.62 115.42,104.74C115.36,104.64 115.31,104.53 115.27,104.42C115.18,104.21 115.07,103.91 114.93,103.53C114.65,102.76 114.31,101.71 113.93,100.53L112.96,97.53L112.66,96.63C112.61,96.52 112.58,96.4 112.57,96.29Z"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M115.88,104.79C115.86,104.75 118,103.9 120.66,102.91C123.32,101.91 125.5,101.14 125.51,101.18C125.53,101.22 123.39,102.07 120.72,103.06C118.06,104.06 115.89,104.83 115.88,104.79Z"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M110.56,116.22C110.67,116.19 110.79,116.17 110.91,116.17L111.89,116.1C112.71,116.03 113.84,115.94 115.09,115.79C116.33,115.65 117.46,115.48 118.28,115.35L119.24,115.2C119.36,115.18 119.48,115.16 119.6,115.17C119.49,115.21 119.37,115.25 119.25,115.27C119.03,115.32 118.7,115.39 118.29,115.47C117.49,115.63 116.36,115.82 115.11,115.96C113.86,116.1 112.72,116.18 111.89,116.21C111.48,116.23 111.15,116.24 110.92,116.24C110.8,116.25 110.68,116.24 110.56,116.22Z"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M105.87,109.19C105.96,109.26 106.03,109.35 106.09,109.44C106.22,109.61 106.4,109.86 106.62,110.17C107.06,110.79 107.66,111.65 108.32,112.61C108.98,113.57 109.56,114.45 109.98,115.08C110.19,115.4 110.36,115.66 110.47,115.84C110.54,115.93 110.59,116.03 110.63,116.13C110.55,116.06 110.48,115.97 110.42,115.88C110.29,115.71 110.11,115.46 109.89,115.15C109.44,114.53 108.84,113.67 108.18,112.71C107.53,111.75 106.94,110.88 106.52,110.24C106.32,109.92 106.15,109.66 106.03,109.48C105.97,109.39 105.91,109.29 105.87,109.19V109.19Z"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M99.9,118.68C100,118.78 100.09,118.89 100.17,119.01L100.85,119.94C101.42,120.74 102.2,121.83 103.06,123.06C103.91,124.28 104.67,125.39 105.22,126.2L105.86,127.16C105.95,127.27 106.02,127.4 106.08,127.53C105.98,127.43 105.89,127.32 105.81,127.2L105.13,126.26C104.56,125.47 103.78,124.37 102.92,123.15C102.07,121.93 101.3,120.82 100.76,120.01L100.12,119.05C100.03,118.93 99.96,118.81 99.9,118.68H99.9Z"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M105.99,127.27C106.14,127.24 106.29,127.21 106.44,127.2L107.67,127.06C108.71,126.94 110.14,126.78 111.72,126.55C113.3,126.32 114.72,126.08 115.75,125.9L116.97,125.68C117.12,125.65 117.27,125.63 117.42,125.63C117.28,125.68 117.13,125.72 116.98,125.75C116.7,125.82 116.29,125.91 115.77,126.01C114.75,126.22 113.33,126.49 111.74,126.71C110.16,126.94 108.72,127.1 107.68,127.18C107.16,127.22 106.74,127.25 106.44,127.27C106.29,127.28 106.14,127.29 105.99,127.27Z"
      android:fillColor="#E593DD"/>
  <path
      android:pathData="M67.11,136.78C62.61,135.82 54.33,135.41 46.73,135.54C39.13,135.66 32.15,136.26 25.99,136.96C21.29,137.51 16.88,138.13 13.72,138.9C10.41,139.71 8.58,140.64 7.51,141.58C4.26,144.46 7.97,147.46 15.02,150.17C22.08,152.88 32.35,155.35 42.72,157.79C47.35,158.88 52.01,159.96 57.28,160.97C60.96,161.68 65.48,162.4 71.29,162.5C79.13,162.63 85.64,161.59 90.2,160.58C100.8,158.24 108.13,155.57 117.99,153.15C122.78,151.97 128.39,150.73 127.68,149.34C127.29,148.56 124.91,147.84 122.46,147.16C109.42,143.54 67.11,136.78 67.11,136.78Z"
      android:strokeAlpha="0.0896"
      android:fillColor="#C5C5C5"
      android:fillType="evenOdd"
      android:fillAlpha="0.0896"/>
  <path
      android:pathData="M114.53,35.65H113.72V13.38C113.72,11.69 113.39,10.01 112.74,8.45C112.09,6.89 111.14,5.46 109.95,4.27C108.75,3.07 107.34,2.12 105.78,1.47C104.21,0.83 102.54,0.49 100.85,0.49H53.74C52.05,0.49 50.37,0.83 48.81,1.47C47.25,2.12 45.83,3.07 44.64,4.27C43.44,5.46 42.49,6.89 41.85,8.45C41.2,10.01 40.87,11.69 40.87,13.38V135.57C40.87,137.26 41.2,138.94 41.85,140.5C42.49,142.07 43.44,143.49 44.64,144.68C45.83,145.88 47.25,146.83 48.81,147.48C50.37,148.13 52.05,148.46 53.74,148.46H100.85C102.54,148.46 104.21,148.13 105.78,147.48C107.34,146.83 108.75,145.88 109.95,144.68C111.14,143.49 112.09,142.07 112.74,140.5C113.39,138.94 113.72,137.26 113.72,135.57V51.51H114.53V35.65Z"
      android:fillColor="#3F3D56"/>
  <path
      android:pathData="M101.37,3.85H95.22C95.5,4.54 95.61,5.29 95.53,6.04C95.46,6.79 95.2,7.5 94.78,8.12C94.36,8.75 93.8,9.26 93.14,9.61C92.48,9.96 91.74,10.15 90.99,10.15H64C63.25,10.15 62.51,9.96 61.85,9.61C61.19,9.26 60.63,8.75 60.21,8.12C59.79,7.5 59.53,6.79 59.46,6.04C59.38,5.29 59.49,4.54 59.77,3.85H54.03C52.77,3.85 51.52,4.09 50.35,4.58C49.18,5.06 48.13,5.77 47.23,6.66C46.34,7.56 45.63,8.62 45.15,9.79C44.67,10.96 44.42,12.21 44.42,13.47V135.48C44.42,136.74 44.67,137.99 45.15,139.16C45.63,140.33 46.34,141.39 47.23,142.29C48.13,143.18 49.18,143.89 50.35,144.37C51.52,144.86 52.77,145.11 54.03,145.11H101.37C102.63,145.11 103.88,144.86 105.05,144.37C106.21,143.89 107.27,143.18 108.17,142.29C109.06,141.39 109.76,140.33 110.25,139.16C110.73,137.99 110.98,136.74 110.98,135.48V13.47C110.98,10.92 109.97,8.47 108.17,6.66C106.36,4.86 103.92,3.85 101.37,3.85Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M102.67,22.9H52.73V65.8H102.67V22.9Z"
      android:fillColor="#AC22AF"
      android:fillAlpha="0.14"/>
  <path
      android:pathData="M67.55,74.75C69.57,74.75 71.2,73.11 71.2,71.09C71.2,69.07 69.57,67.43 67.55,67.43C65.53,67.43 63.9,69.07 63.9,71.09C63.9,73.11 65.53,74.75 67.55,74.75Z"
      android:fillColor="#A80381"/>
  <path
      android:pathData="M69.38,70.78H67.85V69.26H67.25V70.78H65.72V71.39H67.25V72.92H67.85V71.39H69.38V70.78Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M86.83,74.75C88.85,74.75 90.49,73.11 90.49,71.09C90.49,69.07 88.85,67.43 86.83,67.43C84.82,67.43 83.18,69.07 83.18,71.09C83.18,73.11 84.82,74.75 86.83,74.75Z"
      android:fillColor="#A80381"/>
  <path
      android:pathData="M88.66,71.39V70.78H85.01V71.39H88.66Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M79.56,28.25H75.62C75.48,28.25 75.34,28.31 75.25,28.41C75.15,28.51 75.09,28.64 75.09,28.78V30.51C75.09,30.65 75.15,30.79 75.25,30.88C75.34,30.98 75.48,31.04 75.62,31.04H76.27V33.64H78.91V31.04H79.56C79.7,31.04 79.83,30.98 79.93,30.88C80.03,30.79 80.08,30.65 80.08,30.51V28.78C80.08,28.64 80.03,28.51 79.93,28.41C79.83,28.31 79.7,28.25 79.56,28.25Z"
      android:fillColor="#E44A3D"/>
  <path
      android:pathData="M83.51,44.13C81.1,40.59 79.86,36.37 79.98,32.08C79.99,31.98 79.95,31.89 79.89,31.81C79.83,31.73 79.75,31.68 79.65,31.66V30.91H75.48V31.65H75.43C75.37,31.65 75.31,31.66 75.26,31.68C75.21,31.7 75.16,31.73 75.12,31.77C75.08,31.81 75.05,31.86 75.03,31.91C75.01,31.96 75,32.02 75,32.07C75,32.09 75,32.1 75,32.11C75.33,36.69 74.22,40.8 71.67,44.44C71.53,44.65 71.45,44.9 71.46,45.15L71.92,59.27C71.93,59.59 72.06,59.89 72.28,60.11C72.51,60.33 72.81,60.45 73.13,60.45H82.55C82.87,60.45 83.18,60.32 83.4,60.1C83.63,59.88 83.76,59.57 83.76,59.25L83.94,45.54C83.94,45.04 83.8,44.55 83.51,44.13Z"
      android:fillColor="#3F3D56"/>
  <path
      android:pathData="M78.74,28.82C78.74,29.12 78.62,29.41 78.41,29.62C78.2,29.83 77.91,29.95 77.61,29.95C77.31,29.95 77.02,29.83 76.81,29.62C76.6,29.41 76.48,29.12 76.48,28.82"
      android:strokeAlpha="0.2"
      android:fillColor="#000000"
      android:fillAlpha="0.2"/>
  <path
      android:pathData="M82.02,48.82H80.88C80.77,48.11 80.41,47.47 79.87,47C79.33,46.53 78.63,46.28 77.92,46.28C77.2,46.28 76.51,46.53 75.97,47C75.43,47.47 75.07,48.11 74.96,48.82H73.82C73.69,48.82 73.57,48.85 73.46,48.9C73.35,48.95 73.26,49.02 73.18,49.12C73.1,49.21 73.04,49.32 73.01,49.43C72.97,49.55 72.96,49.67 72.98,49.79L74.22,57.87H81.34L82.85,49.82C82.87,49.7 82.87,49.57 82.84,49.45C82.81,49.33 82.75,49.22 82.67,49.13C82.59,49.03 82.49,48.95 82.38,48.9C82.27,48.85 82.15,48.82 82.02,48.82V48.82Z"
      android:fillColor="#E44A3D"/>
  <path
      android:pathData="M102.67,83.29H52.73V126.19H102.67V83.29Z"
      android:fillColor="#AC22AF"
      android:fillAlpha="0.14"/>
  <path
      android:pathData="M67.55,135.13C69.57,135.13 71.2,133.49 71.2,131.47C71.2,129.45 69.57,127.81 67.55,127.81C65.53,127.81 63.9,129.45 63.9,131.47C63.9,133.49 65.53,135.13 67.55,135.13Z"
      android:fillColor="#A80381"/>
  <path
      android:pathData="M69.38,131.17H67.85V129.64H67.25V131.17H65.72V131.78H67.25V133.3H67.85V131.78H69.38V131.17Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M86.83,135.13C88.85,135.13 90.49,133.49 90.49,131.47C90.49,129.45 88.85,127.81 86.83,127.81C84.82,127.81 83.18,129.45 83.18,131.47C83.18,133.49 84.82,135.13 86.83,135.13Z"
      android:fillColor="#A80381"/>
  <path
      android:pathData="M88.66,131.78V131.17H85.01V131.78H88.66Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M87.53,113.76L87.56,113.76C88.5,113.77 89.42,113.5 90.2,112.97C90.98,112.45 91.59,111.7 91.94,110.83C93.04,108.03 93.3,104.97 92.68,102.03C92.06,99.08 90.59,96.39 88.45,94.27C86.31,92.16 83.6,90.72 80.66,90.14C77.71,89.56 74.66,89.86 71.88,91.01C69.1,92.15 66.72,94.09 65.03,96.58C63.35,99.07 62.43,102 62.39,105C62.36,108.01 63.2,110.96 64.83,113.49C66.46,116.02 68.79,118.02 71.54,119.23C72.4,119.61 73.35,119.72 74.27,119.55C75.18,119.38 76.03,118.93 76.69,118.27C78.11,116.84 79.8,115.71 81.66,114.93C83.52,114.16 85.52,113.76 87.53,113.76Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M79.67,109.12C82.46,109.12 84.72,106.86 84.72,104.06C84.72,101.26 82.46,99 79.67,99C76.87,99 74.61,101.26 74.61,104.06C74.61,106.86 76.87,109.12 79.67,109.12Z"
      android:fillColor="#F9A825"/>
</vector>
