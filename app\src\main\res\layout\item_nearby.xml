<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="220dp"
    android:layout_height="160dp">


    <FrameLayout
        android:layout_marginTop="50dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center|bottom"
                android:paddingBottom="16dp"
                android:background="@drawable/bg_round_shadow_ripple">
                <TextView
                    android:id="@+id/tvTitle"
                    style="@style/label_item_nearby_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"

                    tools:text="Test" />
            </LinearLayout>


        </androidx.cardview.widget.CardView>

    </FrameLayout>



    <com.mikhaellopez.circularimageview.CircularImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/icons_size_height_weight_3"
        android:layout_height="@dimen/icons_size_height_weight_3"
        android:background="@null"
        android:layout_gravity="center|top"
        tools:srcCompat="@tools:sample/avatars" />
</FrameLayout>