package com.techcubics.albarkahyper.ui.adapters.holders.profile

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.common.IRefreshListListener
import com.techcubics.albarkahyper.databinding.ItemAddressBinding
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Locations
import com.techcubics.albarkahyper.ui.adapters.profile.LocationsAdapter
import com.techcubics.shared.constants.EndPointConstants.location
import org.koin.android.ext.android.get

class LocationViewHolder(
    private val context: Context,
    private val isCart: Boolean,
    var listOfAddresses: List<Locations>,
    val locationItemLiveData: MutableLiveData<Locations?>,
    val selectedDeleteLocationMutableLiveData: MutableLiveData<Locations?>,
    val itempositionMutableLiveData: MutableLiveData<Int?>,
    val vm: CartFragmentViewModel?,
    val refresh: IRefreshListListener?,
    var getSelectedAddressLocation: () -> Locations?,
    private var binding: ItemAddressBinding,
    val updateSelectedLocation: (Locations?) -> Unit
) :
    RecyclerView.ViewHolder(binding.root) {

    private val sharedPreferencesManager: SharedPreferencesManager by lazy {
        (context.getActivity())!!.get()
    }

    private val itemName = binding.addressTv
    private val editicon = binding.edit
    private val deleteicon = binding.delete
    private val bottomSheetAlertDialog = BottomSheetAlertDialog()
    private var selectionState = false
    fun setData(location: Locations) {
        bottomSheetAlertDialog.init(context)
//        ("${location.address}").also { itemName.text = it }
        ("${location.governorate?.name} ," + "${location.region?.name}\n\n" +
                "${location.address}").also { itemName.text = it }

        val loc =
            listOfAddresses.find { l -> l.locationId == sharedPreferencesManager.getLocationID() }
        binding.radioSelected.isChecked = location.locationId == loc?.locationId
        selectionState = binding.radioSelected.isChecked
        if (!isCart) {

//            if (loc == null) {
//                loc = listOfAddresses[0]
//                loc.locationId?.let { sharedPreferencesManager.saveLocationID(it) }
//            }
            binding.radioSelected.isEnabled = false

        } else {
//            binding.radioSelected.isChecked =
//                location.locationId == getSelectedAddressLocation()?.locationId
            binding.radioSelected.setOnClickListener {
                if (getSelectedAddressLocation()?.locationId != location.locationId) {
                    setSelectedLocation(location)
                }
            }
        }

        editicon.setOnClickListener {
            locationItemLiveData.postValue(listOfAddresses[bindingAdapterPosition])
        }
        deleteicon.setOnClickListener {
            val id = if (getSelectedAddressLocation() != null) {
                getSelectedAddressLocation()?.locationId
            } else {
                sharedPreferencesManager.getLocationID()
            }
            if (id != location.locationId) {
                selectedDeleteLocationMutableLiveData.postValue(location)
                itempositionMutableLiveData.postValue(bindingAdapterPosition)
            } else {
                bottomSheetAlertDialog.showDialog(context.getString(com.techcubics.style.R.string.change_address_warning))
            }
        }

    }

    private fun setBtnSelection(location: Locations) {
        if (selectionState) {
            binding.radioSelected.isChecked = false
            sharedPreferencesManager.removeLocationID()
            updateSelectedLocation(null)

        } else {
            binding.radioSelected.isChecked = true
            if (sharedPreferencesManager.getLocationID() != location.locationId) {
                setSelectedLocation(location)
            }
        }
    }

    private fun setSelectedLocation(location: Locations) {
        if (sharedPreferencesManager.getLocationID() != location.locationId) {
            refresh?.location = location
//            location.locationId?.let { sharedPreferencesManager.saveLocationID(it) }
//            updateSelectedLocation(location)
            vm?.selectLocation(location.locationId)
        }
    }
}