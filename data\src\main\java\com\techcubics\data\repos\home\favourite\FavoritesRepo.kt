package com.techcubics.data.repos.home.favourite

import com.techcubics.data.model.pojo.FavouriteProduct
import com.techcubics.data.model.pojo.FavouriteStore
import com.techcubics.data.model.requests.home.AddRemoveFavoriteRequest
import com.techcubics.data.model.requests.home.MyFavoirtesRequest
import com.techcubics.data.remote.BaseResponse

interface FavoritesRepo {

    suspend fun addRemoveFav(request: AddRemoveFavoriteRequest): BaseResponse<String>?

    suspend fun getMyFavoirteProducts(request: MyFavoirtesRequest): BaseResponse<MutableList<FavouriteProduct>>?

    suspend fun getMyFavoirteStores(request: MyFavoirtesRequest): BaseResponse<MutableList<FavouriteStore>>?
}