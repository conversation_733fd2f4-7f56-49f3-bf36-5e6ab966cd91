<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="1dp"

    >

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
       >
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/offer_price"
            style="@style/toolbar_caption"
            android:layout_gravity="center_vertical|center"
            android:maxLines="2"
            android:ellipsize="end"
            />



    </androidx.appcompat.widget.Toolbar>


</com.google.android.material.appbar.AppBarLayout>

