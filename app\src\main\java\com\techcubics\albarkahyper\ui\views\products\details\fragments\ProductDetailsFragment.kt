package com.techcubics.albarkahyper.ui.views.products.details.fragments


import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.albarkahyper.common.*
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.ProductDetailsDto
import com.techcubics.data.model.pojo.Rates
import com.techcubics.albarkahyper.databinding.FragmentProductDetailsBinding
import com.techcubics.albarkahyper.ui.adapters.product.ProductsImagesAdapter
import com.techcubics.albarkahyper.ui.adapters.product.RatesAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.ProductDetailsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.shared.enums.ShareEnum
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*
import kotlin.collections.ArrayList

class ProductDetailsFragment : Fragment(), OnItemsChangedListener,
    ViewTreeObserver.OnWindowFocusChangeListener {

    private var _binding: FragmentProductDetailsBinding? = null

    private val binding get() = _binding!!

    private val viewModel by viewModel<ProductDetailsViewModel>()

    private val authViewModel by viewModel<AuthViewModel>()
    private lateinit var popupDialog: PopupDialog
    private var productId: Int = -1
    private var slug: String? = null
    private var selectedColorId = -1
    private var selectedSizeId = -1
    private lateinit var ratesAdapter: RatesAdapter
    private lateinit var imagesAdapter: ProductsImagesAdapter

    //    private lateinit var colorsAdapter: ProductDetailsColorsAdapter
    private var product: ProductDetailsDto? = null
    private var isUpdateRatesOly = false
    private var isUpdateFavoriteOnly = false
    private lateinit var reviewProgressBtn: ProgressButton
    private lateinit var addToCartProgressBtn: ProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var quantityButtonsController: QuantityButtonsController

    //    private var sizeIndex = 0
//    private lateinit var sizeController: SizeController
    private var rates: ArrayList<Rates>? = null
    private val mainViewModel: MainViewModel by activityViewModels()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Helper.hideSystemUIWithNavigation(requireActivity().window)
        if (_binding == null) {
            _binding = FragmentProductDetailsBinding.inflate(inflater, container, false)
            initViews()
            checkNetwork()
        }

        return binding.root
    }

    private fun checkNetwork() {
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            checkAuthorization()
//            setColors()
//            setSizes()
            setImages()
            setRatesRecyclerView()
            onRefresh(slug ?: productId.toString())
            setObservers()
            setBtnListeners()
            binding.productDetailsContainer.colorsTitle.visibility = View.GONE
            binding.productDetailsContainer.colorsRecyclerView.visibility = View.GONE
            binding.productDetailsContainer.includeSizeSection.root.visibility = View.GONE
//            sizeController = SizeController(
//                requireContext(),
//                this,
//                binding.productDetailsContainer.includeSizeSection.root
//            )
        } else {
            binding.scrollView.visibility = View.GONE
            binding.nonetworkView.visibility = View.VISIBLE
        }
    }

    private fun initQuantityController(maxQty: Int, minQty: Int) {
        quantityButtonsController = QuantityButtonsController(
            binding.productDetailsContainer.productQtyCartContainer.root,
            binding.productDetailsContainer.minOrderNote,
            requireContext(),
            this,
            maxQty,
            minQty
        )
        quantityButtonsController.setQuantity()
        val qty = if ((product?.qtyCart ?: 0) > 0||(product?.discount?.get(0)?.quantityCart?:0)>0) {
            if (product?.isDiscount == true){
                product?.discount?.get(0)?.quantityCart?:0
            }else{
                product?.qtyCart?:0
            }
        } else {
            minQty
        }
        quantityButtonsController.updateQuantity(qty)
        quantityButtonsController.isBtnDecreaseEnabled()
        quantityButtonsController.isBtnIncreaseEnabled()
    }

    private fun initViews() {
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        productId = arguments?.getInt(Constants.INTENT_ID, -1) ?: -1
        slug = arguments?.getString(Constants.INTENT_SLUG)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        reviewProgressBtn = ProgressButton(requireContext())
        addToCartProgressBtn = ProgressButton(requireContext())
        reviewProgressBtn.init(binding.productDetailsContainer.btnReview)
        addToCartProgressBtn.init(binding.productDetailsContainer.btnAddToCart)
    }

    private fun setBtnListeners() {
        binding.productDetailsContainer.btnReview.textView.text =
            getString(R.string.share_your_rate_hint)
        binding.productDetailsContainer.btnAddToCart.textView.text =
            getString(R.string.add_to_cart)
        binding.productImg.navigateBack.setOnClickListener {
            findNavController().popBackStack()
        }
        enableDisableButtons(product != null)
        binding.productDetailsContainer.btnAddToCart.constraintsLayout.setOnClickListener {
            if (sharedPreferencesManager.isLoggedIn() == "true") {
                addToCart(product)
            } else {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.product_details_to_login)
            }
        }
        binding.productDetailsContainer.btnReview.constraintsLayout.setOnClickListener {
            if (product != null) {

                addRate()
            }
        }
        binding.productImg.isFav.setOnClickListener {
            if (product != null) {
                binding.productImg.isFav.isFavorite = !product?.isFav!!
                product?.id?.let { it1 -> viewModel.addRemoveFav(it1) }
            }
        }
        binding.productImg.btnShare.setOnClickListener {
            val shareUrl = Constants.BASE_PRODUCT_SHARE_URL + slug
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            viewModel.getShareLink(ShareEnum.Product.value, url = shareUrl, id = slug!!)
        }
        binding.productDetailsContainer.branchContainer.setOnClickListener {
            product?.let { d ->
                val bundle = Bundle()
                bundle.putInt(Constants.INTENT_ID, d.shop?.id!!)

            }
        }
//        quantityButtonsController.setListeners()
        binding.productDetailsContainer.ratingContainer.setOnClickListener {
            if (rates != null) {
                val action = ProductDetailsFragmentDirections.viewRatings(rates!!.toTypedArray())
                findNavController().navigate(action)
            }
        }
    }

    private fun setObservers() {
        viewModel.shareLinkResponse.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            if (it != null) {
                if (it.status!!) {
                    Helper.shareLink(requireContext(), it.data!!)

                } else {
                    Helper.ShowErrorDialog(requireContext(), it.message!!)
                }
                viewModel.shareLinkResponse.value = null
            }
        }
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.message?.contains(getString(R.string.unauthenticated))!!) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.productdetailsLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.product_details_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(R.string.server_error)
                    )
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }

        viewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner) {
            if (it != null && it.message != Constants.SERVER_ERROR) {
                isUpdateFavoriteOnly = true
                onRefresh(productId.toString())
                viewModel.addRemoveFavoriteResponse.value = null
            } else if (it?.message?.contains(Constants.SERVER_ERROR) == true) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }
        }

        viewModel.addRateResponse.observe(viewLifecycleOwner) {
            if (it != null && it.message != Constants.SERVER_ERROR) {
                if (it.status!!) {
                    reviewProgressBtn.btnFinishedSuccessfully(
                        getString(R.string.share_your_rate_hint),
                        null
                    )
                } else {
                    reviewProgressBtn.btnFinishedFailed(
                        getString(R.string.share_your_rate_hint),
                        null
                    )
                }
                isUpdateRatesOly = true
                bottomSheetAlertDialog.showDialog(it.message.toString())
                binding.productDetailsContainer.comment.setText("")
                binding.productDetailsContainer.yourRating.rating = 0f
                onRefresh(productId.toString())
                viewModel.addRateResponse.value = null
            } else if (it?.message?.contains(Constants.SERVER_ERROR) == true) {
                reviewProgressBtn.btnFinishedFailed(getString(R.string.share_your_rate_hint), null)
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }
        }

        viewModel.productDetailsResponse.observe(viewLifecycleOwner) { productDetails ->
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)

            if (productDetails.message != Constants.SERVER_ERROR) {
                if (productDetails.data?.product != null) {
                    productId = productDetails?.data?.product?.productId ?: -1
                    slug = productDetails?.data?.product?.slug
                    binding.scrollView.visibility = View.VISIBLE
                    binding.animation.visibility = View.GONE

                    product = productDetails?.data?.product
                    if (isUpdateFavoriteOnly) {
                        setFav()
                        isUpdateFavoriteOnly = false
                    }
                    if (isUpdateRatesOly) {
                        setRates(productDetails.data?.rates)
                        isUpdateRatesOly = false
                    }
                    if (!isUpdateFavoriteOnly && !isUpdateRatesOly) {
                        setData(productDetails.data?.rates)
                    }
                } else {
                    binding.scrollView.visibility = View.GONE
                    binding.animation.visibility = View.VISIBLE

                    binding.animation.setAnimation(R.raw.empty_box_lottie)
                }
            } else if (productDetails?.message?.contains(Constants.SERVER_ERROR) == true) {
                binding.tvMessage.visibility = View.VISIBLE
                binding.tvMessage.text = getString(R.string.server_error)
                binding.animation.visibility = View.VISIBLE

                binding.animation.setAnimation(R.raw.lottie_error)
                binding.scrollView.visibility = View.GONE
            }

        }

        viewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null && it.message.toString() != Constants.SERVER_ERROR) {
                if (it.status == true) {
                    addToCartProgressBtn.btnFinishedSuccessfully(
                        getString(R.string.add_to_cart),
                        R.style.label_message
                    )
                    bottomSheetAlertDialog.showDialog(getString(R.string.added_to_cart))
                } else {
                    if (it.message!!.contains(getString(R.string.unauthenticated))) {
                        CoroutineScope(Dispatchers.Main).launch {
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            findNavController().navigate(com.techcubics.albarkahyper.R.id.product_details_to_login)
                        }
                    } else {
                        addToCartProgressBtn.btnFinishedFailed(
                            getString(R.string.add_to_cart),
                            R.style.label_message
                        )
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }

                }
                viewModel.addCartResponse.value = null
            } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                addToCartProgressBtn.btnFinishedFailed(
                    getString(R.string.add_to_cart),
                    R.style.label_message
                )
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }
        }
    }

    private fun setFav() {
        binding.productImg.isFav.isFavorite = product?.isFav ?: false
    }

    private fun setRates(rates: ArrayList<Rates>?) {
        this.rates = rates
        if ((rates?.size ?: 0) > 0) {
            binding.productDetailsContainer.reviewsRecyclerView.visibility = View.VISIBLE
            binding.productDetailsContainer.placeholder.root.visibility = View.GONE
            ratesAdapter.updateRates(rates ?: listOf())
        } else {
            binding.productDetailsContainer.placeholder.icon.setAnimation(R.raw.lottie_empty_no_comments)
            binding.productDetailsContainer.placeholder.tvMessage.text =
                getString(R.string.message_empty_list_nocomments)
            binding.productDetailsContainer.reviewsRecyclerView.visibility = View.GONE
            binding.productDetailsContainer.placeholder.root.visibility = View.VISIBLE
        }
    }

    private fun setData(rates: ArrayList<Rates>?) {
        if (product?.isDiscount == false) {
            binding.productDetailsContainer.priceDiscountLayout.visibility = View.GONE
            binding.productImg.tvDiscountTag.visibility = View.GONE
            "${product?.price} ${requireContext().getString(R.string.currency_name)}"
                .also { binding.productDetailsContainer.tvPrice.text = it }
        } else {
            binding.productDetailsContainer.tvPrice.visibility = View.GONE
            binding.productImg.tvDiscountTag.visibility = View.VISIBLE
            "${product?.discount?.get(0)?.priceBefore} ${requireContext().getString(R.string.currency_name)}"
                .also { binding.productDetailsContainer.tvBeforeDiscountPrice.text = it }
            "${product?.discount?.get(0)?.priceAfter} ${requireContext().getString(R.string.currency_name)}"
                .also { binding.productDetailsContainer.tvAfterDiscountPrice.text = it }
        }
        val maxQty = if (product?.isDiscount == true) product?.discount?.get(0)?.maxQty
            ?: 0 else product?.maxQty
        val minQty = if (product?.isDiscount == true) product?.discount?.get(0)?.minQty
            ?: 0 else product?.minQty
        binding.productDetailsContainer.maxQty.text = maxQty.toString()
        binding.productDetailsContainer.minQty.text = minQty.toString()
        modelType =
            if (product?.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        modelId =
            if (product?.isDiscount == true) product?.discount?.get(0)?.discountID
                ?: -1 else product?.productId ?: -1
        setEndPoint(product?.isDiscount)

        initQuantityController(
            maxQty = if (product?.isDiscount == true) product?.discount?.get(0)?.maxQty ?: Int.MAX_VALUE
            else product?.maxQty ?: Int.MAX_VALUE,
            minQty = if (product?.isDiscount==true) product?.discount?.get(0)?.minQty ?: 1 else product?.minQty?:1
        )
        //
        setFav()
        //
        setRates(rates)
        //
        setVisibilityOfDotsIndicator()
        //
        setShopData()
        //
//        sizeIndex=0
        calcPrice()
        if (product?.isDiscount == true) {
            product?.discount?.get(0)?.priceAfter?.let { setPrice(it, binding.productDetailsContainer.tvPrice) }
        } else {
            product?.price?.let { setPrice(it, binding.productDetailsContainer.tvPrice) }
        }
        //
        setProductData()
        //
        setRateData()
        //
//        setColorData()
        //
//        setSizesData()
//        sizeController.initDropdownList(product?.sizes)
        //
        enableDisableButtons(product != null)

    }

    private fun setEndPoint(discount: Boolean?) {
        endpoint = if (discount == true) {
            EndPointConstants.add_discount
        } else {
            EndPointConstants.add_product
        }
    }
//    private fun setSizesData() {
//        if (product?.sizes.isNullOrEmpty()) {
//            binding.productDetailsContainer.sizeTitle.visibility = View.GONE
//            binding.productDetailsContainer.sizesRecyclerView.visibility = View.GONE
//        } else {
//            binding.productDetailsContainer.sizeTitle.visibility = View.VISIBLE
//            binding.productDetailsContainer.sizesRecyclerView.visibility = View.VISIBLE
//        }
//        selectedSizeId =
//            if ((product?.sizes?.size ?: 0) > 0) product?.sizes!![0].sizeID else -1
//        sizesAdapter.setItemsList(product?.sizes ?: listOf())
//    }

//    private fun setColorData() {
//        if (product?.colors.isNullOrEmpty()) {
//            binding.productDetailsContainer.colorsTitle.visibility = View.GONE
//            binding.productDetailsContainer.colorsRecyclerView.visibility = View.GONE
//        } else {
//            binding.productDetailsContainer.colorsTitle.visibility = View.VISIBLE
//            binding.productDetailsContainer.colorsRecyclerView.visibility = View.VISIBLE
//        }
//        selectedColorId =
//            if ((product?.colors?.size ?: 0) > 0) product?.colors!![0].colorID else -1
//        colorsAdapter.setItemsList(product?.colors ?: listOf())
//    }

    private fun setRateData() {
        binding.productDetailsContainer.rating.rating = product?.rate ?: 0f
        binding.productDetailsContainer.tvRating.text = product?.rate.toString()
        "${product?.rateCount} ${getString(R.string.rate_title)}".also {
            binding.productDetailsContainer.rateCount.text = it
        }
    }

    private fun setProductData() {
        imagesAdapter.updateImagesAndVideo(
            product?.images ?: listOf(),
            product?.video
        )
        binding.productDetailsContainer.category.tvTitle.text = product?.category?.name
        product?.category?.image?.let {
            Helper.loadImage(
                requireContext(),
                it,
                binding.productDetailsContainer.category.icon
            )
        }
        binding.productDetailsContainer.productDetails.text = product?.description
        binding.productDetailsContainer.tvTitle.text = product?.name
    }

    private fun setShopData() {
        binding.productDetailsContainer.description.text = product?.shop?.description
        Helper.loadImage(
            requireContext(),
            product?.shop?.logo!!,
            binding.productDetailsContainer.galleryImg
        )

        binding.productDetailsContainer.galleryName.text = product?.shop?.name
    }

    private fun setVisibilityOfDotsIndicator() {
        val size = product?.images?.size ?: 0
        val videoNotFound = product?.video.isNullOrEmpty()
        val isSingleItem =
            if (videoNotFound) {
                size <= 1
            } else {
                (size + 1) <= 1
            }
        if (isSingleItem) {
            binding.productDetailsContainer.dotsIndicator.visibility = View.GONE
        }
    }


    private fun setPrice(price: Float, priceTextView: TextView) {
        "${
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(price)
        } ${getString(R.string.currency_name)}".also {
            priceTextView.text = it
        }
    }

    private fun getSelectedPrice(): Float {
        return if (product?.isDiscount == true) {
            product?.discount?.get(0)?.priceAfter ?: 0f
        } else {
            product?.price ?: 0f
        }
    }

    override fun calcPrice() {
        val price = getSelectedPrice()
        val priceCents = (price * 100f).toInt()
        val totalPriceCents = priceCents * quantityButtonsController.getQuantity()
        totalPriceCents.toFloat() / 100f
        setPrice((totalPriceCents.toFloat() / 100f), binding.productDetailsContainer.price)
    }

    private fun enableDisableButtons(isEnabled: Boolean) {
        binding.productDetailsContainer.btnAddToCart.constraintsLayout.isEnabled = isEnabled
        binding.productDetailsContainer.btnReview.constraintsLayout.isEnabled = isEnabled
    }

    private fun setRatesRecyclerView() {
        ratesAdapter = RatesAdapter(requireContext(), listOf())
        binding.productDetailsContainer.reviewsRecyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.productDetailsContainer.reviewsRecyclerView.adapter = ratesAdapter
    }

    private fun setImages() {
        imagesAdapter = ProductsImagesAdapter(
            requireContext(),
            listOf(),
            null,
            mainViewModel,
            this
        )
        binding.productImg.viewPager2.adapter = imagesAdapter
        binding.productDetailsContainer.dotsIndicator.attachTo(binding.productImg.viewPager2)
    }

//    private fun setColors() {
//        colorsAdapter = ProductDetailsColorsAdapter(listOf(), this)
//        binding.productDetailsContainer.colorsRecyclerView.layoutManager =
//            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
//        binding.productDetailsContainer.colorsRecyclerView.adapter = colorsAdapter
//    }

//    private fun setSizes() {
//        sizesAdapter = ProductSizesAdapter(listOf(), this)
//        binding.productDetailsContainer.sizesRecyclerView.layoutManager =
//            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
//        binding.productDetailsContainer.sizesRecyclerView.adapter = sizesAdapter
//    }

    private fun addRate() {
        if (productId > -1) {
            val rate = binding.productDetailsContainer.yourRating.rating.toInt()
            val comment = binding.productDetailsContainer.comment.text.toString()
            if (rate != 0 && comment.isNotEmpty()) {
                reviewProgressBtn.btnActivated()
                viewModel.addRate(
                    id = productId,
                    type = RateTypesEnum.Product,
                    rate,
                    comment

                )
            } else {
                bottomSheetAlertDialog.showDialog(getString(R.string.rate_warning))
            }
        }
    }

    private var endpoint = EndPointConstants.add_product
    private lateinit var modelType: String
    private var modelId = -1
    private fun addToCart(product: ProductDetailsDto?) {
        product.let { p ->
//            if (selectedColorId != -1 && sizeController.getSizeId() != -1) {
//                viewModel.addToCart(
//                    EndPointConstants.add_product,
//                    furnitureId = p?.shop?.id,
//                    modelType = p?.modelType,
//                    modelId = p?.id,
//                    qty = quantityButtonsController.getQuantity(),
//                    sizeId = sizeController.getSizeId(),
//                    colorId = selectedColorId
//                )
//            } else {
            viewModel.addToCart(
                endpoint,
                shopId = p?.shop?.id,
                modelType = modelType,
                modelId = modelId,
                qty = quantityButtonsController.getQuantity()
            )
            if(this.product?.isDiscount == true) {
                this.product?.discount?.get(0)?.quantityCart = quantityButtonsController.getQuantity()
            }else{
                this.product?.qtyCart =  quantityButtonsController.getQuantity()
            }
//            }
        }

    }

    override fun onColorSelected(colorId: Int) {
        selectedColorId = colorId
    }

    override fun onRefresh(id: String) {
        viewModel.getProductDetails(id)
    }

    private fun checkAuthorization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            binding.productImg.isFav.visibility = View.VISIBLE
            binding.productDetailsContainer.reviewContainer.visibility = View.VISIBLE
            authViewModel.checkAuthorization()
        } else {
            binding.productImg.isFav.visibility = View.GONE
            binding.productDetailsContainer.reviewContainer.visibility = View.GONE

        }
    }

    override fun onSizeSelected(sizeId: Int, index: Int) {
        selectedSizeId = sizeId
//        sizeIndex = index
        calcPrice()
    }

    override fun onYoutubeSizeChanged(seconds: Float, vidId: String, position: Int) {
        val bundle = Bundle()
        bundle.putFloat(Constants.YOUTUBE_SECONDS, seconds)
        bundle.putString(Constants.YOUTUBE_VID_ID, vidId)

        findNavController().navigate(
            com.techcubics.albarkahyper.R.id.action_productDetailsFragment3_to_youtubeFullScreenFragment3,
            bundle
        )
        requireActivity().requestedOrientation =
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.root.viewTreeObserver?.removeOnWindowFocusChangeListener(this)
        Helper.exitFullScreen(requireContext())
        product?.let { mainViewModel.updateProduct(it) }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUIWithNavigation(requireActivity().window)
        else Helper.showSystemUIWithNavigation(requireActivity().window)
    }
}


interface OnItemsChangedListener {
    fun onColorSelected(colorId: Int) {}
    fun onSizeSelected(sizeId: Int, index: Int) {}
    fun onRefresh(id: String) {}
    fun onYoutubeSizeChanged(seconds: Float, vidId: String, position: Int) {}
    fun calcPrice() {}
}
