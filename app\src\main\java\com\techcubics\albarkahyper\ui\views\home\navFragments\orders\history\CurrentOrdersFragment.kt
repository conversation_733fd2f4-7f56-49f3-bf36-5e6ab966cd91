package com.techcubics.albarkahyper.ui.views.home.navFragments.orders.history

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Order
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.FragmentCurrentOrdersBinding
import com.techcubics.albarkahyper.ui.adapters.order.OrdersAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.orders.viewmodels.OrdersViewModel
import com.techcubics.shared.constants.Constants
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class CurrentOrdersFragment : Fragment() {

    private lateinit var binding : FragmentCurrentOrdersBinding
    private lateinit var ordersList : ArrayList<Order>
    private lateinit var tempOrdersList : ArrayList<Order>
    private val orderViewModel by viewModel<OrdersViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCurrentOrdersBinding.inflate(layoutInflater,container,false)
        ordersList = arrayListOf()
        tempOrdersList = arrayListOf()
        initViews()
        observeViews()
        return binding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){

            if(it != null){
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                }
                else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                   Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
                    binding.currentErrorLayout.layout.visibility = View.VISIBLE
                    binding.currentErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.currentErrorLayout.tvMessage.text = getString(com.techcubics.style.R.string.server_error)
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }


        }
        orderViewModel.currentOrdersMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    if (it.data!!.isNotEmpty()) {
                        ordersList.clear()
                        tempOrdersList.clear()
                        ordersList.addAll(it.data!!)
                        tempOrdersList.addAll(it.data!!)
                        Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
                        binding.currentErrorLayout.layout.visibility = View.GONE
                        binding.currentRecycleview.visibility = View.VISIBLE
                        binding.currentRecycleview.adapter = OrdersAdapter(
                            requireActivity(),
                            tempOrdersList,
                            orderViewModel.currentItemClickedLiveData
                        )
                    } else {
                        Log.i("here", "empty" + it.data.toString())
                        Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

                        binding.currentRecycleview.visibility = View.GONE
                        binding.currentErrorLayout.layout.visibility = View.VISIBLE
                        binding.currentErrorLayout.icon.setFailureListener { t ->
                            t.localizedMessage?.let { it1 -> Log.i("here", it1) }
                        }
                        binding.currentErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                        binding.currentErrorLayout.tvMessage.text = getString(com.techcubics.style.R.string.message_empty_list_general)

                    }
                }
                else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

                    binding.currentErrorLayout.layout.visibility = View.VISIBLE
                    binding.currentErrorLayout.icon.setFailureListener { t ->
                        t.localizedMessage?.let { it1 -> Log.i("here", it1) }
                    }
                    binding.currentErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.currentErrorLayout.tvMessage.text = getString(com.techcubics.style.R.string.server_error)
                }
                else {
                    Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
                    binding.currentRecycleview.visibility = View.VISIBLE
                    // showDialog(it.message!!)
                }
            }
        }
        orderViewModel.currentItemClickedLiveData.observe(viewLifecycleOwner){
            Log.i("clothes","current post value"+it)
            if(it != null){
                Log.i("clothes","current post value")
                val args = Bundle()
                args.putInt(Constants.ORDER_ID, it.orderId!!)
                findNavController().navigate(com.techcubics.albarkahyper.R.id.action_ordersFragment_to_orderDetails,args)
                orderViewModel.currentItemClickedLiveData.value = null
            }

        }
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun initViews() {
        checkAutherization()
        orderViewModel.getCurrentOrders()
        val etSearch = binding.searchview.txtSearchWord

        etSearch.setOnEditorActionListener { v, actionId, _ ->
            when (actionId) {
                EditorInfo.IME_ACTION_SEARCH -> {
                    v.clearFocus()
                    v.hideKeyboard()
                    tempOrdersList.clear()
                    val searchText = v.text.toString().trim().lowercase(Locale.getDefault())
                    if(searchText.isNotEmpty()){
                        ordersList.forEach {
                            if((it.shop?.name?.lowercase(Locale.getDefault())?.contains(searchText)!!) || it.orderDetails[0].productName?.lowercase(
                                    Locale.getDefault()
                                )?.contains(searchText)!!){
                                tempOrdersList.add(it)
                                Log.i("here",it.shop?.name.toString()+" "+it.orderDetails[0].productName)
                            }
                        }
                        binding.currentRecycleview.adapter?.notifyDataSetChanged()
                    }else{
                        tempOrdersList.clear()
                        tempOrdersList.addAll(ordersList)
                        binding.currentRecycleview.adapter?.notifyDataSetChanged()
                    }
                    true
                }

                else -> false
            }
        }
    }


    private fun View.hideKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)

            authViewModel.checkAuthorization()
        }else{
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

            binding.currentErrorLayout.layout.visibility = View.VISIBLE
            binding.currentErrorLayout.icon.setFailureListener { t ->
                t.localizedMessage?.let { Log.i("here", it) }
            }
            binding.currentErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
            binding.currentErrorLayout.tvMessage.text = getString(com.techcubics.style.R.string.message_error_loading_login_required)
        }
    }


}
