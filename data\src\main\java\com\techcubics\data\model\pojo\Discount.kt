package com.techcubics.data.model.pojo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Discount (
    @SerializedName("id")
    val id:Int,
    @SerializedName("discount_id")
    val discountID:Int,
    @SerializedName("price_before")
    val priceBefore:Float,
    @SerializedName("price_after")
    val priceAfter:Float,
    @SerializedName("percent")
    val percent:Float,
    @SerializedName("product_name")
    val productName:String,
    @SerializedName("product_description")
    val productDescription:String,
    @SerializedName("images")
    val images:MutableList<Image>,
    @SerializedName("shop")
    val shop:Store?,
    @SerializedName("rate")
    val rate:Int,
    @SerializedName("rate_count")
    val rateCount:Int?,
    @SerializedName("model_type")
    val modelType:String,
    @SerializedName("qty_cart")
    var quantityCart:Int,
    @SerializedName("minimum_order_number"   ) var minQty      : Int     = 1,
    @SerializedName("maximum_order_number"   ) var maxQty      : Int     = 1,
):Parcelable

@Parcelize
data class DiscountDto (

    @SerializedName("id"                   ) var id                 : Int?              = null,
    @SerializedName("discount_id"          ) var discountID         : Int?              = null,
    @SerializedName("price_before"         ) var priceBefore        : Float?              = null,
    @SerializedName("price_after"          ) var priceAfter         : Float?           = null,
    @SerializedName("percent"              ) var percent            : Float?              = null,
    @SerializedName("product_name"         ) var productName        : String?           = null,
    @SerializedName("product_description"  ) var productDescription : String?           = null,
    @SerializedName("images"               ) var images             : ArrayList<Image> = arrayListOf(),
    @SerializedName("rate"                 ) var rate               : Int?              = null,
    @SerializedName("rate_count"           ) var rateCount          : String?           = null,
    @SerializedName("minimum_order_number" ) var minQty             : Int?              = null,
    @SerializedName("maximum_order_number" ) var maxQty             : Int?              = null,
    @SerializedName("model_type"           ) var modelType          : String?           = null,
    @SerializedName("qty_cart"             ) var quantityCart       : Int?              = null,
):Parcelable