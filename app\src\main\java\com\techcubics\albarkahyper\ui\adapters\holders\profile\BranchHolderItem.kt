package com.techcubics.albarkahyper.ui.adapters.holders.profile

import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemShoptypeBinding

class BranchHolderItem(itemView: ItemShoptypeBinding) :
        RecyclerView.ViewHolder(itemView.root) {
        val itemName = itemView.shopName
        val image = itemView.shopImage
        val itemChecked = itemView.checkbox
    }