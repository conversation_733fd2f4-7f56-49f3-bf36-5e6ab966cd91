<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/bg_style_light_gray"
            android:weightSum="3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/address_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1.5"
                android:drawablePadding="8dp"
                android:gravity="center_vertical|start"
                android:paddingTop="16dp"
                android:paddingBottom="15dp"
                android:text="ttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttt"
                android:textAlignment="viewStart"
                android:textAppearance="@style/label_cart_item_address"
                app:drawableStartCompat="@drawable/ic_location" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1">


                <ImageView
                    android:id="@+id/edit"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/bg_icon_ripple"
                    android:paddingStart="2dp"
                    android:paddingEnd="2dp"
                    android:src="@drawable/ic_edit_yellow" />

                <ImageView
                    android:id="@+id/delete"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_icon_ripple"
                    android:src="@drawable/ic_move_trash" />

                <RadioButton
                    android:id="@+id/radio_selected"
                    android:layout_width="27dp"
                    android:layout_height="27dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="5dp"
                    android:buttonTint="@color/app_color"
                    android:checked="true" />

            </LinearLayout>

        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>