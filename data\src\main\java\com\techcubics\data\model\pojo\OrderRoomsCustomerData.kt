package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class OrderRoomsCustomerData(

    @SerializedName("name")
    val name: String,
    @SerializedName("email")
    val email: String,
    @SerializedName("phone")
    val phone: String,
    @SerializedName("avatar")
    val avatar: String,
    @SerializedName("address")
    val address: String,
    @SerializedName("gender")
    val gender: String,
    @SerializedName("birthday")
    val birthday: String,
    @SerializedName("age")
    val age: Int,
    @SerializedName("id")
    val id: Int,
    @SerializedName("user_id")
    val user_id: Int
)