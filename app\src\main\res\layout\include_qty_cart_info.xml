<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cart_info"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:rotation="180"
        android:progressDrawable="@drawable/bg_cart_progress_horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/open_cart_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="10dp"
        android:background="?selectableItemBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/label_item_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/order_now"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            style="@style/label_item_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cash"
            android:textAlignment="center"
            android:textColor="@color/cold"
            android:textStyle="bold" />
    </LinearLayout>


    <View
        android:id="@+id/line"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:background="@color/color_gray_1"
        android:elevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/open_cart_btn"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/min_num"
        style="@style/label_item_area"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/min_num"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textStyle="normal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone" />

    <TextView
        android:id="@+id/min_order"
        style="@style/label_item_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/min_order"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/total_amount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/line"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/total_amount"
        style="@style/label_item_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/product_order_total_amount"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/line"
        app:layout_constraintTop_toBottomOf="@id/min_order" />

</androidx.constraintlayout.widget.ConstraintLayout>
