package com.techcubics.albarkahyper.ui.views.home.navFragments.orders.orderdetails

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Order
import com.techcubics.data.model.pojo.OrderDetails
import com.techcubics.data.model.pojo.ProductItem
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.albarkahyper.databinding.FragmentMyOrderDetailsBinding
import com.techcubics.albarkahyper.ui.adapters.order.ProductsDetailsAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.orders.viewmodels.OrdersViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class MyOrderDetailsFragment : Fragment() {


    private lateinit var binding: FragmentMyOrderDetailsBinding

    private var check = true
    private var pageType: String? = ""
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val orderViewModel by viewModel<OrdersViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()
    private lateinit var popupDialog: PopupDialog
    private var orderID: Int = -1
    private lateinit var productDetailsAdapter: ProductsDetailsAdapter
    private var order: Order? = null
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        orderID = arguments?.getInt(Constants.ORDER_ID) ?: -1
        pageType = arguments?.getString(Constants.INTENT_PAGE_TYPE)
        binding = FragmentMyOrderDetailsBinding.inflate(layoutInflater, container, false)
        Log.i("here", orderID.toString())
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        observeViews()
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
    }

    @SuppressLint("SetTextI18n", "ResourceAsColor")
    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.message!!.contains(getString(R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
//                        binding.orderdetialsLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    binding.scView.visibility = View.GONE
                    binding.errorLayout.layout.visibility = View.VISIBLE
                    binding.errorLayout.icon.setAnimation(R.raw.lottie_error)
                    binding.errorLayout.tvMessage.setText(R.string.server_error)
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }

        orderViewModel.orderDetailsMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.orderDetailView.visibility = View.VISIBLE
                Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
                if (it.status!!) {
                    binding.scView.visibility = View.VISIBLE
                    order = it.data!!
                    binding.specifiedTime.text = it.data?.time
                    binding.specifiedDay.text = " ," + it.data?.day
                    binding.orderNo.text = it.data?.orderCode
                    binding.orderFrom.text =
                        it.data?.shop?.name
                    if (!it.data?.location?.address.isNullOrEmpty()) {
                        binding.orderAddress.visibility = View.VISIBLE
                        binding.orderAddress.text = it.data?.location!!.address
                    } else {
                        binding.orderAddress.visibility = View.GONE
                    }

                    when (it.data?.orderStatus) {
                        "pending" -> binding.orderStatus.setTextColor(resources.getColor(R.color.salmon))
                        "onway" -> binding.orderStatus.setTextColor(resources.getColor(R.color.pyellow))
                        "cancelled" -> binding.orderStatus.setTextColor(resources.getColor(R.color.pred))
                        "delivered" -> binding.orderStatus.setTextColor(resources.getColor(R.color.pgreen))
                        "completed" -> binding.orderStatus.setTextColor(resources.getColor(R.color.pgreen))
                    }
                    binding.orderStatus.text = it.data?.orderStatus
                    binding.orderNotes.text = it.data?.note

                    var ordersDetailsList: ArrayList<OrderDetails> = arrayListOf()
                    var productsList : ArrayList<ProductItem> = arrayListOf()
                    ordersDetailsList = it.data!!.orderDetails
                    Log.i("albarkahyper", ordersDetailsList.size.toString())
                    for (order in ordersDetailsList){
                        productsList.add(ProductItem(order.productName,order.productIcon,order.price.toString(),order.qty.toString()))
                    }

                    productDetailsAdapter = ProductsDetailsAdapter(requireContext(),productsList)
                    binding.productsRV.adapter = productDetailsAdapter

                    var productsname = ""
                    for (names in it.data?.orderDetails!!) {
                        productsname += names.productName + "-"
                    }
                    productsname = productsname.substring(0, productsname.length - 1)

//
//                    binding.deliveryPrice.text =
//                        it.data?.deliveryPrice.toString() + " " + getString(R.string.currency_name)
                    binding.couponPrice.text =
                        it.data?.couponPrice.toString() + " " + getString(R.string.currency_name)
                    binding.productTotalAmount.text =
                        it.data?.totalPrice.toString() + " " + getString(R.string.currency_name)
                    binding.deliveryFees.text = it.data?.deliveryPrice.toString()+ " " + getString(R.string.currency_name)
                    binding.instantDiscount.text = it.data?.rangeDiscountAmount.toString()+ " " + getString(R.string.currency_name)
                    binding.totalAmount.text = it.data?.amount.toString()+ " " + getString(R.string.currency_name)
//                    binding.deliveryArea.text = it.data?.location?.address

                    Log.i("albarkahyper", it.data?.orderStatus.toString())
                    if (it.data?.orderStatus?.contains(getString(R.string.status_pending))!!) {
                        binding.chatOff.visibility = View.GONE
                        binding.chatOn.visibility = View.VISIBLE

                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    binding.scView.visibility = View.GONE
                    binding.errorLayout.layout.visibility = View.VISIBLE
                    binding.errorLayout.icon.setAnimation(R.raw.lottie_error)
                    binding.errorLayout.tvMessage.setText(R.string.server_error)
                } else {
                    if (it.message!!.contains(getString(R.string.unauthenticated))) {

                    } else {
                        bottomSheetAlertDialog.showDialog(it.message!!)
                    }
                }
            }

        }
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    private fun initViews() {
        binding.toolbar.tvTitle.text = getString(R.string.myorder)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            if (pageType.equals(Constants.CART)) {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.action_orderDetails_to_homeFragment)
            } else {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.action_orderDetails_to_orderFragment)

            }
        }
        checkAutherization()

        activity?.onBackPressedDispatcher?.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (pageType.equals(Constants.CART)) {
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.action_orderDetails_to_homeFragment)
                    } else {
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.action_orderDetails_to_orderFragment)

                    }

                }
            })

        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        binding.scView.visibility = View.GONE
//        binding.seeDetails.setOnClickListener {
//            if (check) {
//                binding.details.visibility = View.VISIBLE
//                binding.seeDetails.setCompoundDrawablesWithIntrinsicBounds(
//                    null, null, resources.getDrawable(R.drawable.ic_arrow_top), null
//                )
//                check = false
//            } else {
//                binding.details.visibility = View.GONE
//                binding.seeDetails.setCompoundDrawablesWithIntrinsicBounds(
//                    null, null, resources.getDrawable(R.drawable.ic_arrow_down), null
//                )
//                check = true
//            }
//
//
//        }
        binding.chatOn.setOnClickListener {
            if (order != null) {
                val name = order?.shop?.name
                val orderId = order?.orderId!!
                val bundle = Bundle()
                bundle.putInt(Constants.INTENT_ID, orderId)
                bundle.putString(Constants.INTENT_NAME, name)
                findNavController().navigate(
                    com.techcubics.albarkahyper.R.id.view_orderChatHistoryFragment,
                    bundle
                )
            }
        }
        orderViewModel.getOrderDetails(orderID)
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}