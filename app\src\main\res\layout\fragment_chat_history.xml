<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/chathistory_layout"
    >

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <include

            android:id="@+id/toolbar"
            layout="@layout/toolbar_chat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroller"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">


            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/linearLayoutCompat2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"

                >


<!--                <com.facebook.shimmer.ShimmerFrameLayout-->
<!--                    android:id="@+id/shimmerID"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginTop="24dp"-->
<!--                    android:layout_marginEnd="16dp">-->

<!--                    <androidx.appcompat.widget.LinearLayoutCompat-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:orientation="vertical">-->

<!--                        <include-->
<!--                            layout="@layout/include_shimmer_chat_send"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginBottom="16dp" />-->

<!--                        <include-->
<!--                            layout="@layout/include_shimmer_chat_send"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_marginBottom="16dp" />-->


<!--                    </androidx.appcompat.widget.LinearLayoutCompat>-->

<!--                </com.facebook.shimmer.ShimmerFrameLayout>-->


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvHistory"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="16dp"
                    tools:listitem="@layout/item_category" />


            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/include_chat_send_box"
            layout="@layout/include_chat_send_box"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"


            />

    </androidx.appcompat.widget.LinearLayoutCompat>


    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        />
    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_centerInParent="true" />
</RelativeLayout>