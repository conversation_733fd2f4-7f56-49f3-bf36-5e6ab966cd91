<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/timerSection"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layoutDirection="ltr"
        android:orientation="horizontal"
        >

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_gray_thick"
            android:elevation="4dp"
            android:gravity="center"
            android:paddingHorizontal="15dp"
            android:orientation="vertical"
            android:layout_marginEnd="4dp"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginTop="4dp"


            >

            <TextView
                android:id="@+id/tvHours"
                style="@style/label_item_timer_tick_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginBottom="-4dp"
                android:layout_marginTop="2dp"
                android:text="00" />

            <TextView
                style="@style/label_item_timer_tick_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/timer_hours" />
        </androidx.appcompat.widget.LinearLayoutCompat>


        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_gray_thick"
            android:elevation="4dp"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginTop="4dp"
            >

            <TextView
                android:id="@+id/tvMinutes"
                style="@style/label_item_timer_tick_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginBottom="-4dp"
                android:layout_marginTop="2dp"
                android:text="00" />

            <TextView
                style="@style/label_item_timer_tick_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/timer_minutes" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_gray_thick"
            android:elevation="4dp"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="15dp"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginTop="4dp"
            >

            <TextView
                android:id="@+id/tvSeconds"
                style="@style/label_item_timer_tick_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginBottom="-4dp"
                android:layout_marginTop="2dp"
                android:text="00" />

            <TextView
                style="@style/label_item_timer_tick_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"

                android:text="@string/timer_seconds" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.appcompat.widget.LinearLayoutCompat>


</androidx.appcompat.widget.LinearLayoutCompat>
