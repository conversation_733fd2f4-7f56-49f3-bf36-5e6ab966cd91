package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.databinding.ItemBranchTypeBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemSliderBinding
import com.techcubics.shared.constants.Constants


class BannerHolderItem (val binding: ItemSliderBinding, val context: Context ): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "BannerHolderItem"
    private lateinit var intent:Intent

    fun bind(banner: BannerData){


        Helper.loadImage(context,banner.image,binding.img)

        binding.root.setOnClickListener {view->
            val bundle=Bundle()
            /*bundle.putInt(Constants.INTENT_ID,branchType.id)
            bundle.putString(Constants.INTENT_NAME, branchType.name)
            view.findNavController().navigate(com.techcubics.albarkahyper.R.id.branchTypeMainCategoriesFragment,bundle)
            Log.d(TAG, "bind: test")*/
        }

    }

}