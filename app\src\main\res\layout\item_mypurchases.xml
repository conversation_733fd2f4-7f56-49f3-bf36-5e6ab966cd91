<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:cardElevation="4dp"
    app:cardCornerRadius="8dp"
    android:layout_marginHorizontal="5dp"
    android:layout_marginVertical="8dp"
    android:layoutDirection="ltr">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:orientation="horizontal"
        android:layout_gravity="center_vertical">

        <CheckBox
            android:id="@+id/purchaseCheckBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:buttonTint="@color/app_color"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"/>
        <TextView
            android:id="@+id/item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/label_cart_item_address"
            android:layout_gravity="center_vertical"
            android:text="gggggggggggggggggggg"/>

        <TextView
            android:id="@+id/date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="11-11-2022"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="16dp"
            android:layout_marginTop="7dp"
            android:textSize="11dp"
            />

    </LinearLayout>

</androidx.cardview.widget.CardView>