package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class StoreDetailsMostWantedData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("product_id")
    val productID: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("price")
    val price: Float,
    @SerializedName("video")
    val video: String,
    @SerializedName("icon")
    val icon: String,
    @SerializedName("sizes")
    val Sizes: MutableList<Size>,
    @SerializedName("images")
    val Images: MutableList<Image>,
    @SerializedName("colors")
    val colorObjects: MutableList<ColorObject>,
    @SerializedName("is_fav")
    var isFav: Boolean,
    @SerializedName("rate")
    val rate: Float,
    @SerializedName("rate_count")
    val rate_count: Int,
    @SerializedName("model_type")
    val model_type: String,
    @SerializedName("qty_cart")
    val qtyCart: Int,
    @SerializedName("minimum_order_number"   ) var minQty     : Int=1,
    @SerializedName("maximum_order_number"   ) var maxQty     : Int=1,

    @SerializedName("is_discount"          ) var isDiscount         : Boolean?          = null,
//    @SerializedName("discount_id"          ) var discountId         : Int?              = null,
//    @SerializedName("price_before"         ) var priceBefore        : Float?              = null,
//    @SerializedName("price_after"          ) var priceAfter         : Float?           = null,
//    @SerializedName("percent"              ) var percent            : Double?           = null,
    @SerializedName("discounts"              ) var discount          : ArrayList<DiscountDto>?           = null,

)
