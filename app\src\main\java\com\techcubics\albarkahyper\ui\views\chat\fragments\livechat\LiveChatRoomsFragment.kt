package com.techcubics.albarkahyper.ui.views.chat.fragments.livechat

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.model.pojo.LiveChatRoomData
import com.techcubics.data.model.pojo.OrderRoomsData
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentChatRoomsBinding
import com.techcubics.albarkahyper.databinding.FragmentLiveChatRoomsBinding
import com.techcubics.albarkahyper.ui.adapters.chat.ChatRoomsHistoryAdapter
import com.techcubics.albarkahyper.ui.views.chat.fragments.ChatTabsFragmentDirections
import com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat.ChatRoomsFragmentDirections
import com.techcubics.albarkahyper.ui.views.chat.viewmodels.ChatViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

class LiveChatRoomsFragment : Fragment(), IPageRowset<LiveChatRoomData>,
    IItemClickListener {

    private lateinit var binding: FragmentLiveChatRoomsBinding
    private lateinit var chatRoomsHistoryAdapter: ChatRoomsHistoryAdapter<LiveChatRoomData>
    private val chatViewModel by viewModel<ChatViewModel>()
    private lateinit var popupDialog: PopupDialog
    private lateinit var refreshClickListener: IItemClickListener
    private  val TAG = "LiveChatRoomsFragment"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLiveChatRoomsBinding.inflate(inflater, container, false)
        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {

//      binding.toolbar.tvTitle.text = resources.getText(com.techcubics.style.R.string.chat)
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        //
        chatViewModel.getAllLiveChatRooms()
        //
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.rvHistory.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
    }

    override fun observers() {

        chatViewModel.liveChatRoomsResponse.observe(viewLifecycleOwner, Observer {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            try {
                if (it.status!!) {

                    if (it.data != null) {

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvHistory.visibility = View.VISIBLE

                        } else {

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })


    }

    override fun events() {


//        binding.toolbar.tvTitle.setOnClickListener {
//
//            findNavController().popBackStack()
//        }

        refreshClickListener=object:IItemClickListener{
            override fun perform() {
                chatViewModel.getAllLiveChatRooms()
            }
        }


    }

    override fun showData(items: List<LiveChatRoomData>) {
        Log.i("here", items.toString())
        chatRoomsHistoryAdapter = ChatRoomsHistoryAdapter<LiveChatRoomData>(onItemClicked = this )
        chatRoomsHistoryAdapter.setItemsList(items)
        binding.rvHistory.adapter = chatRoomsHistoryAdapter
        binding.rvHistory.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    if(message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!){
                        CoroutineScope(Dispatchers.Main).launch {
                            binding.chatroomLayout.visibility = View.GONE
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
                        binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                        binding.placeholder.tvMessage.text = message
                    }
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onItemClick(id: Int, name: String,logo:String) {

        var action= ChatTabsFragmentDirections.chatTabsFragmentToLiveChatFragment(refreshClickListener,id,name,logo)
        Navigation.findNavController(requireParentFragment().requireView()).navigate(action)

        /*val b=Bundle()
        b.putInt(Constants.INTENT_ID,id)
        b.putString(Constants.INTENT_NAME, name)
        b.putString(Constants.INTENT_URL, logo)
        findNavController().navigate(R.id.action_liveChatRoomsFragment_to_liveChatFragment,b)*/

    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}