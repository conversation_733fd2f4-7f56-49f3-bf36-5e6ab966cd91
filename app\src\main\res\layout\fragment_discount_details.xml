<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.FullScreen"
    tools:context=".ui.views.products.details.fragments.DiscountDetailsFragment">
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animation"
        android:layout_width="200dp"
        android:layout_height="200dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="50dp"
        android:visibility="gone"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_speed="1" />
    <TextView
        android:id="@+id/tvMessage"
        style="@style/label_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textAlignment="center"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/animation"
        app:layout_constraintStart_toStartOf="@id/animation"
        app:layout_constraintTop_toBottomOf="@id/animation" />
    <LinearLayout
        android:id="@+id/nonetwork_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <include layout="@layout/include_no_network_layout" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <include
                android:id="@+id/discount_img"
                android:layout_width="match_parent"
                android:layout_height="450dp"
                layout="@layout/include_section_details_images" />

            <include
                android:id="@+id/discount_details_container"
                layout="@layout/include_discount_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"  />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>