package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.addresses

import android.Manifest
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.location.Geocoder
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.core.view.isEmpty
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.widget.Autocomplete
import com.google.android.libraries.places.widget.AutocompleteActivity
import com.google.android.libraries.places.widget.model.AutocompleteActivityMode
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentAddAddressBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.profile.location.AddLoactionRequest
import com.techcubics.data.model.requests.profile.location.UpdateLocationRequest
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants.furniture
import com.techcubics.shared.utils.AuthUtils
import kotlinx.coroutines.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.DecimalFormat
import java.util.*


class AddAddressFragment : Fragment(), OnMapReadyCallback, EventListener {

    private lateinit var binding: FragmentAddAddressBinding
    private lateinit var progressBtn: ProgressButton
    private var mapFragment: SupportMapFragment? = null
    private var googleMap: GoogleMap? = null
    private var fragmentTag = ""
    private var locationId = ""
    private var location: Latlng? = null
    private lateinit var choosenLocation: Latlng
    private lateinit var currentLocation: Latlng
    private lateinit var editLocation: LatLng
    private var furnitureId = -1
    private lateinit var popupDialog: PopupDialog
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private val mainViewModel: MainViewModel by activityViewModels()
    private var countriesList: MutableLiveData<ArrayList<DeliveryAreaId>>? =
        MutableLiveData<ArrayList<DeliveryAreaId>>()
    private var governerateList: MutableLiveData<ArrayList<Governerate>>? =
        MutableLiveData<ArrayList<Governerate>>()
    private var regionList: MutableLiveData<ArrayList<Regoin>>? =
        MutableLiveData<ArrayList<Regoin>>()
    private var check = false
    private var selectedCountryId: Int? = 0
    private var selectedGovernerateId: Int? = 0
    private var selectedRegionId: Int? = 0
    private var deliveryAreaId = -1
    private  var checkGovCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private  var checkRegionCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkCountryCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAddAddressBinding.inflate(inflater, container, false)
        furnitureId = arguments?.getInt(Constants.SHOP_ID, -1) ?: -1
        fragmentTag = arguments?.getString(Constants.FRAGMENT_TAG)!!
        fragmentTag = arguments?.getString(Constants.FRAGMENT_TAG)!!
        if (fragmentTag == Constants.UPDATE) {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            locationId = arguments?.getString(Constants.LOCATION_ID)!!
            profileViewModel.showLocation(
                locationId.toInt(), sharedPreferencesManager.getCountryID().toInt(),
                sharedPreferencesManager.getCountryCode()
            )
            binding.governerate.isEnabled = true
            binding.governerate.isClickable = true
            binding.governerate.isLongClickable = true
            binding.region.isEnabled = true
            binding.region.isClickable = true
            binding.region.isLongClickable = true
        } else {
            Log.i("here", "add")
            binding.governerate.isEnabled = false
            binding.governerate.isClickable = false
            binding.governerate.isLongClickable = false
            binding.region.isEnabled = false
            binding.region.isClickable = false
            binding.region.isLongClickable = false
        }

        mapFragment =
            this.childFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?

        mapFragment?.getMapAsync(this)

        val ai: ApplicationInfo = requireContext().packageManager
            .getApplicationInfo(requireContext().packageName, PackageManager.GET_META_DATA)
        val value = ai.metaData["com.google.android.geo.API_KEY"]
        val placekey = value.toString()
        Log.i("here", placekey)
        Places.initialize(requireContext(), placekey)

        progressBtn = ProgressButton(requireContext())
        progressBtn.init(binding.signBtnProgress)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        initViews()
        observeViews()
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        return binding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {

            if (it != null) {
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.addaddressLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }
        profileViewModel.countriesPopupMenuItemLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.country.setText(it.name)
                selectedCountryId = it.country_id
                if(furnitureId == -1){
                    it.country_id?.let { it1 -> profileViewModel.getGovernerateByCountryId(it1) }
                }else{
                    profileViewModel.getGovernorateAreasByDeliveryId(deliveryAreaId,
                        selectedCountryId!!)
                }
                profileViewModel.countriesPopupMenuItemLiveData.value = null
            }
        }
        profileViewModel.deliveryAreasResponse.observe(viewLifecycleOwner) {
            if (it != null) {
                Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)

                if (it.status!!) {
                    countriesList?.postValue(it.data!!)
                    deliveryAreaId =
                        it.data!!.find { d -> d.country?.id == selectedCountryId }?.deliveryAreaId
                            ?: -1
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    Log.i("here", it.message!!)
                }
                profileViewModel.deliveryAreasResponse.value = null
            }
        }
        countriesList?.observe(viewLifecycleOwner){
            if(it != null){
                val countryList = it
                checkCountryCLicked.observe(viewLifecycleOwner){
                    if(it != null){
                        if(it){
                            Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
                            popupDialog.showCounrtyDialog(
                                requireContext(),
                                R.layout.dialog_country,
                                countryList,
                                profileViewModel.countriesAndDeliveryAreasPopupMenuItemLiveData as MutableLiveData<Any?>,
                                Constants.ADD
                            )
                        }
                        checkCountryCLicked.value = null
                    }
                }
            }
        }
        profileViewModel.countriesAndDeliveryAreasPopupMenuItemLiveData.observe(
            viewLifecycleOwner
        ) {
            if (it != null) {
                binding.country.setText(it.country?.name)
                deliveryAreaId = it.deliveryAreaId!!
                profileViewModel.getGovernorateAreasByDeliveryId(
                    it.deliveryAreaId!!,
                    sharedPreferencesManager.getCountryID().toInt()
                )
                selectedCountryId = it.country?.countryID
                profileViewModel.countriesAndDeliveryAreasPopupMenuItemLiveData.value = null
            }
        }
        governerateList?.observe(viewLifecycleOwner) {
            if (it != null) {
                checkGovCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Log.i("clothes","clickedgov"+it)
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                binding.actionLoadingAnimation.root
                            )
                            popupDialog.showGovernerateDialog(
                                requireContext(),
                                R.layout.dialog_language,
                                governerateList?.value!!,
                                profileViewModel.governeratesMutableLiveData
                            )
                        }
                        checkGovCLicked.value = null
                    }
                }

            }
        }
        regionList?.observe(viewLifecycleOwner) {
            if (it != null) {
                Log.i("clothes","regionlist1"+it)
                checkRegionCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Log.i("clothes","clicked"+it)
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                binding.actionLoadingAnimation.root
                            )
                            Log.i("clothes","regionlist2"+regionList?.value)
                            popupDialog.showRegionDialog(
                                requireContext(), R.layout.dialog_language,
                                regionList?.value!!, profileViewModel.regionPopupMenuItemLiveData
                            )
                        }
                        checkRegionCLicked.value = null
                    }
                }

            }
        }
        profileViewModel.listOfgovernerateMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    governerateList?.postValue(it.data!!)
                    check = true
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.listOfgovernerateMutableLiveData.value = null
            }

        }
        profileViewModel.governeratesMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.governerate.setText(it.name)
                selectedGovernerateId = it.governorate_id
                if(furnitureId == -1){
                    profileViewModel.getRegionBygovernorateId(
                        selectedGovernerateId!!,
                        selectedCountryId!!
                    )
                }else
                {
                    profileViewModel.getRegionByGovernorateAndDeliveryAreaId(
                        it.governorate_id, deliveryAreaId,
                        sharedPreferencesManager.getCountryID().toInt()
                    )
                }
                profileViewModel.governeratesMutableLiveData.value = null
            }
        }
        profileViewModel.listOfregionsMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    regionList?.postValue(it.data!!)

                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.listOfregionsMutableLiveData.value = null
            }

        }
        profileViewModel.regionPopupMenuItemLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.region.setText(it.name)
                sharedPreferencesManager.saveRegionID(it.regionID.toString())
                selectedRegionId = it.regionID
                profileViewModel.regionPopupMenuItemLiveData.value = null
            }
        }
        profileViewModel.addLocationResponseMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    progressBtn.btnFinishedSuccessfully(
                        getString(com.techcubics.style.R.string.address_adding),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(it.message!!)
                    mainViewModel.setLocationId(true)
                    findNavController().popBackStack()
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    progressBtn.btnFinishedFailed(
                        getString(com.techcubics.style.R.string.address_adding),
                        null
                    )
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    progressBtn.btnFinishedFailed(
                        getString(com.techcubics.style.R.string.address_adding),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.addLocationResponseMutableLiveData.value = null
            }

        }
        profileViewModel.showLocationMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
            if (it != null) {
                if (it.status!!) {
                    selectedCountryId = it.data!!.country!!.countryID
                    selectedGovernerateId = it.data!!.governorate!!.governorate_id
                    selectedRegionId = it.data!!.region!!.regionID
                    editLocation = LatLng(it.data!!.lat!!, it.data!!.lng!!)
                    addLocationMarker(LatLng(it.data!!.lat!!, it.data!!.lng!!))
                    binding.address.setText(it.data!!.address)
                    binding.country.setText(it.data!!.country?.name)
                    binding.governerate.setText(it.data!!.governorate?.name)
                    binding.governerate.setTextAppearance(com.techcubics.style.R.style.label_edittext_addaddress)
                    binding.region.setText(it.data!!.region?.name)
                    choosenLocation = Latlng(it.data!!.lat.toString(), it.data!!.lng.toString())
                    if (furnitureId != -1) {
                        profileViewModel.getCountriesAndDeliveryAreasByFurnitureId(
                            furnitureId,
                            sharedPreferencesManager.getCountryID().toInt()
                        )
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }

                profileViewModel.showLocationMutableLiveData.value = null
            }
        }
        profileViewModel.updateLocationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    progressBtn.btnFinishedSuccessfully(
                        getString(com.techcubics.style.R.string.address_adding),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(it.message!!)
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    progressBtn.btnFinishedFailed(
                        getString(com.techcubics.style.R.string.address_adding),
                        null
                    )
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    progressBtn.btnFinishedFailed(
                        getString(com.techcubics.style.R.string.address_adding),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.updateLocationMutableLiveData.value = null
            }

        }

    }

    private fun initViews() {

        Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
        checkAutherization()

        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }

        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.address_adding)
        binding.signBtnProgress.textView.text =
            getString(com.techcubics.style.R.string.address_adding)

        binding.region.setTextAppearance(com.techcubics.style.R.style.label_edittext_addaddress)
        binding.governerate.setTextAppearance(com.techcubics.style.R.style.label_edittext_addaddress)
        binding.country.setTextAppearance(com.techcubics.style.R.style.label_edittext_addaddress)
        //address clicked to show search
        binding.address.keyListener = null

        binding.address.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.address.callOnClick()
            }
        }

        binding.country.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
            checkCountryCLicked.postValue(true)
            if (furnitureId == -1) {
                profileViewModel.getCountries()

            } else {
                profileViewModel.getCountriesAndDeliveryAreasByFurnitureId(
                    furnitureId,
                    sharedPreferencesManager.getCountryID().toInt()
                )
            }
            binding.governerate.isEnabled = true
            binding.governerate.isClickable = true
            binding.governerate.isLongClickable = true
        }
        binding.governerate.setOnClickListener {
            if(selectedCountryId != 0){
                checkGovCLicked.postValue(true)
                Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
                if (fragmentTag == Constants.ADD) {

                } else {
                    if (furnitureId == -1) {
                        profileViewModel.getGovernerateByCountryId(selectedCountryId!!)
                    } else{
                        profileViewModel.getGovernorateAreasByDeliveryId(
                            deliveryAreaId,
                            selectedCountryId!!
                        )
                    }
                }

                binding.region.isEnabled = true
                binding.region.isClickable = true
                binding.region.isLongClickable = true
            }
        }
        binding.region.setOnClickListener {
            if(selectedGovernerateId != 0){
                checkRegionCLicked.postValue(true)
                Helper.loadingAnimationVisibility(View.VISIBLE, binding.actionLoadingAnimation.root)
                if (fragmentTag == Constants.ADD) {

                } else {
                    if (furnitureId == -1) {

                        profileViewModel.getRegionBygovernorateId(
                            selectedGovernerateId!!,
                            selectedCountryId!!
                        )

                    } else
                        profileViewModel.getRegionByGovernorateAndDeliveryAreaId(
                            selectedGovernerateId!!,
                            deliveryAreaId,
                            selectedCountryId!!
                        )
                }

            }



            Log.i("here", countriesList?.value.toString())


        }

        binding.address.setOnClickListener {
            val fieldList =
                listOf(Place.Field.ADDRESS, Place.Field.LAT_LNG, Place.Field.NAME)
            val intent = Autocomplete.IntentBuilder(AutocompleteActivityMode.OVERLAY, fieldList)
                .build(requireActivity())
            startActivityForResult(intent, 100)
        }
        //-----**-------


        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            if (checkUiValidity()) {
                progressBtn.btnActivated()

                val addressText = binding.address.text.toString()
                location = choosenLocation

                if (fragmentTag == Constants.ADD) {
                    profileViewModel.addLocation(
                        AddLoactionRequest(
                            location?.lat ?: sharedPreferencesManager.getCurrentLatlng().lat,
                            location?.lng ?: sharedPreferencesManager.getCurrentLatlng().lng,
                            addressText,
                            sharedPreferencesManager.getCountryCode(),
                            selectedRegionId?:-1,
                            selectedGovernerateId?:-1,
                            selectedCountryId?:-1
                        )
                    )

                } else {
                    profileViewModel.updateLocation(
                        UpdateLocationRequest(
                            locationId.toInt(),
                            location?.lat ?: sharedPreferencesManager.getCurrentLatlng().lat,
                            location?.lng ?: sharedPreferencesManager.getCurrentLatlng().lng,
                            addressText,
                            sharedPreferencesManager.getCountryCode(),
                            selectedCountryId.toString(),
                            selectedGovernerateId.toString(),
                            selectedRegionId.toString()
                        )
                    )
                }

            }
        }

    }

    private fun roundToSevenDigits(value: Double): String {
        val df = DecimalFormat("##.#######").format(value)
        return convertArabic(df)
    }

    private fun convertArabic(arabicStr: String): String {
        val chArr = arabicStr.toCharArray()
        val sb = StringBuilder()
        for (ch in chArr) {
            if (Character.isDigit(ch)) {
                sb.append(Character.getNumericValue(ch))
            } else if (ch == '٫') {
                sb.append(".")
            } else {
                sb.append(ch)
            }
        }
        return sb.toString()
    }

    override fun onMapReady(p0: GoogleMap) {
        Log.i("here", "onMapReady")
        googleMap = p0
        googleMap?.uiSettings?.isZoomControlsEnabled = true
        if (fragmentTag == Constants.ADD) {
            currentLocation = sharedPreferencesManager.getCurrentLatlng()
            val currentLatLng =
                LatLng(
                    roundToSevenDigits(currentLocation.lat.toDouble()).toDouble(),
                    roundToSevenDigits(currentLocation.lng.toDouble()).toDouble()
                )
            choosenLocation = currentLocation
            binding.address.setText(getAddress(currentLatLng.latitude, currentLatLng.longitude))
            placeMarker(currentLatLng)

        }

    }

    private fun addLocationMarker(currentLatLng: LatLng) {
        if (googleMap != null) {
            Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
            val markerOption = MarkerOptions().position(currentLatLng)
                .title(getString(com.techcubics.style.R.string.choosen_location))
                .snippet(getAddress(currentLatLng.latitude, currentLatLng.longitude))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLng(currentLatLng))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0f))
            val currentMarker = googleMap?.addMarker(markerOption)
            currentMarker?.showInfoWindow()
        }
    }

    private fun placeMarker(currentLatLng: LatLng) {
        Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)
        if (googleMap != null) {
            if (ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            googleMap?.isMyLocationEnabled = true
            googleMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0F))

        }

    }

    private fun getAddress(lat: Double, lng: Double): String {
        var returnAddress = ""
        runBlocking {
            if (<EMAIL> != null) {
                val geocoder = Geocoder(requireContext(), Locale.getDefault())
                val address = geocoder.getFromLocation(lat, lng, 1)
                val addressJob = async { address?.get(0)?.getAddressLine(0).toString() }
                returnAddress = addressJob.await()

            }

        }
        return returnAddress
    }

    private fun checkUiValidity(): Boolean {

        var check = true
         if (binding.address.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_add_address))
            check = false
        }


        return check
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == RESULT_OK) {

            //init place
            val place = data?.let { Autocomplete.getPlaceFromIntent(it) }

            //set Address on edittext
            place?.let {
                binding.address.setText(place.name)
                place.latLng?.let { latlng ->
                    addLocationMarker(latlng)
                    choosenLocation = Latlng(
                        roundToSevenDigits(latlng.latitude),
                        roundToSevenDigits(latlng.longitude)
                    )
                    Log.i("here", latlng.toString() + choosenLocation)
                }
            }


        } else if (resultCode == AutocompleteActivity.RESULT_ERROR) {

            //init status
            val status = data?.let { Autocomplete.getStatusFromIntent(it) }
            Log.i("here", "status" + status?.statusMessage)
        }
    }

    override fun onPause() {
        super.onPause()
        googleMap?.clear()
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}