{"v": "5.6.9", "fr": 60, "ip": 0, "op": 182, "w": 800, "h": 600, "nm": "motion design school домашка 1", "ddd": 0, "assets": [], "fonts": {"list": [{"fName": "Roboto-Regular", "fFamily": "Roboto", "fStyle": "Regular", "ascent": 76.1993408203125}]}, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Null 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16.801, "s": [-22]}, {"t": 60, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.801, "s": [400, 198, 0], "to": [0, -20.333, 0], "ti": [0, 22.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28.801, "s": [400, 76, 0], "to": [0, -22.833, 0], "ti": [0, -21.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50.4, "s": [400, 61, 0], "to": [0, 21.833, 0], "ti": [0, -24.333, 0]}, {"t": 60, "s": [400, 207, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 1.5;\nfreq = 2;\ndecay = 6;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.38, 0.38, 0.667], "y": [1, 0.95, 1]}, "o": {"x": [0.458, 0.458, 0.333], "y": [0.022, 2.24, 0]}, "t": 16.801, "s": [0, 100, 100]}, {"t": 50.400390625, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Слой 2 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 13, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-32.5, 1, 0], "ix": 2}, "a": {"a": 0, "k": [33.25, 26.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33, -26], [-24, -26], [-33, 26], [33, 26]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.957000014361, 0.961000031116, 0.980000035903, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.25, 26.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Слой 22 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 13, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [95.5, 0.502, 0], "ix": 2}, "a": {"a": 0, "k": [38.25, 26.754, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.04, -17.659], [-22.294, -0.003], [5.73, 12.883], [0.716, 1.611], [0.909, 1.254], [2.622, 0.693], [2.149, -0.001], [13.783, -0.006]], "o": [[22.293, 0.003], [-5.73, -12.883], [-0.715, -1.61], [-0.634, -1.424], [-1.612, -2.223], [-2.078, -0.549], [-13.783, 0.007], [3.04, 17.66]], "v": [[-28.881, 26.495], [38, 26.504], [20.811, -12.145], [18.663, -16.975], [16.606, -21.38], [9.753, -25.952], [3.349, -26.503], [-38, -26.483]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.957000014361, 0.961000031116, 0.980000035903, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.25, 26.754], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Слой 21 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 13, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.5, 1, 0], "ix": 2}, "a": {"a": 0, "k": [33.25, 26.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-33, -26], [24, -26], [33, 26], [-33, 26]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.246999987434, 0.289999988032, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.25, 26.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Слой 20 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 13, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-94.5, 0.502, 0], "ix": 2}, "a": {"a": 0, "k": [38.25, 26.754, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [22.294, -0.003], [-5.729, 12.883], [-0.717, 1.611], [-0.909, 1.254], [-2.622, 0.693], [-2.148, -0.001], [-13.784, -0.006]], "o": [[-22.294, 0.003], [5.729, -12.883], [0.716, -1.61], [0.633, -1.424], [1.612, -2.223], [2.078, -0.549], [13.783, 0.007], [0, 0]], "v": [[28.881, 26.495], [-38, 26.504], [-20.812, -12.145], [-18.663, -16.975], [-16.607, -21.38], [-9.753, -25.952], [-3.35, -26.503], [38, -26.483]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.246999987434, 0.289999988032, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.25, 26.754], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Слой 19 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10.001, "s": [0]}, {"t": 13.00078125, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [99.75, 25.75, 0], "ix": 2}, "a": {"a": 0, "k": [33.5, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 62.399, "s": [100, 0, 100]}, {"t": 72.00078125, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 7;\nfreq = 1.8;\ndecay = 7;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.18, 0], [0, 0], [0, 18.271], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-18.271, 0], [0, 0], [0, 0], [0, 0], [0, 18.18]], "v": [[0.083, 33], [0.083, 33], [-33, -0.083], [-33, -33], [33, -33], [33, 0.083]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.791999966491, 0.804000016755, 0.862999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.25, 33.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 7.2, "op": 247.44024024024, "st": 7.2, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Слой 18 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9.999, "s": [0]}, {"t": 12.99921875, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [33.75, 25.75, 0], "ix": 2}, "a": {"a": 0, "k": [33.5, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 59.999, "s": [100, 0, 100]}, {"t": 69.60078125, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 7;\nfreq = 1.8;\ndecay = 7;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.18, 0], [0, 0], [0, 18.271], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-18.271, 0], [0, 0], [0, 0], [0, 0], [0, 18.18]], "v": [[0.083, 33], [0.083, 33], [-33, -0.083], [-33, -33], [33, -33], [33, 0.083]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851000019148, 0.187999994615, 0.234999997008, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.25, 33.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4.8, "op": 245.04024024024, "st": 4.8, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Слой 17 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 12.999609375, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-32.249, 25.75, 0], "ix": 2}, "a": {"a": 0, "k": [33.5, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 57.599, "s": [100, 0, 100]}, {"t": 67.20078125, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 7;\nfreq = 1.8;\ndecay = 7;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.179, 0], [0, 0], [0, 18.271], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-18.271, 0], [0, 0], [0, 0], [0, 0], [0, 18.18]], "v": [[0.083, 33], [0.083, 33], [-33, -0.083], [-33, -33], [33, -33], [33, 0.083]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.791999966491, 0.804000016755, 0.862999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.25, 33.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 2.4, "op": 242.64024024024, "st": 2.4, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Слой 16 Outlines", "parent": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 13, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-98.249, 25.75, 0], "ix": 2}, "a": {"a": 0, "k": [33.5, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 55.199, "s": [100, 0, 100]}, {"t": 64.80078125, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 7;\nfreq = 1.8;\ndecay = 7;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.179, 0], [0, 0], [0, 18.271], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-18.271, 0], [0, 0], [0, 0], [0, 0], [0, 18.18]], "v": [[0.083, 33], [0.083, 33], [-33, -0.083], [-33, -33], [33, -33], [33, 0.083]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.851000019148, 0.187999994615, 0.234999997008, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [33.25, 33.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 3, "nm": "Null 5", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33.6, "s": [-71]}, {"t": 52.80078125, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 9;\nfreq = 2.5;\ndecay = 3;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.6, "s": [471, 308, 0], "to": [0, 7, 0], "ti": [0, -7, 0]}, {"t": 52.80078125, "s": [471, 350, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 6;\nfreq = 1;\ndecay = 9;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33.6, "s": [0, 0, 100]}, {"t": 52.80078125, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 4;\nfreq = 1.8;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 5, "nm": "Open", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [20.085, 12.803, 0], "ix": 2}, "a": {"a": 0, "k": [-0.196, -4.266, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 12, "f": "Roboto-Regular", "t": "OPEN", "j": 2, "tr": 14, "lh": 14.4, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Слой 14 Outlines", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.469, 24.778, 0], "ix": 2}, "a": {"a": 0, "k": [20.25, 12.472, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20, 12.222], [-20, 12.222], [-20, -12.222], [20, -12.222]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.195999998205, 0.195999998205, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.25, 12.472], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Слой 13 Outlines", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.5, 7.5, 0], "ix": 2}, "a": {"a": 0, "k": [16, 14.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-8.5, 7], [-0.5, -7], [8.5, 7]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.282000014361, 0.246999987434, 0.328999986836, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16, 14.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 3, "nm": "Null 2", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21.6, "s": [-83]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33.6, "s": [6.8]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43.199, "s": [6.1]}, {"t": 62.400390625, "s": [-47.5]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.832, "y": 0.877}, "o": {"x": 0.121, "y": 0}, "t": 21.6, "s": [363.75, 436, 0], "to": [0.062, -2.237, 0], "ti": [17.991, 10.421, 0]}, {"i": {"x": 0.539, "y": 1}, "o": {"x": 0.601, "y": 0}, "t": 45.609, "s": [348.84, 354.657, 0], "to": [-25.816, -14.954, 0], "ti": [3.501, -14.903, 0]}, {"t": 62.400390625, "s": [273, 448.8, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.141]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 62.4, "s": [92.5, 92.5, 100]}, {"t": 72, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 16;\nfreq = 2;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Слой 12 Outlines", "parent": 25, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 31.199, "s": [14.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43.199, "s": [53.9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57.6, "s": [53.9]}, {"t": 64.80078125, "s": [47.2]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 15;\nfreq = 2.7;\ndecay = 5;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [32.562, -23.248, 0], "ix": 2}, "a": {"a": 0, "k": [21.5, 87, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 21.6, "s": [0, 0, 100]}, {"t": 26.400390625, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.153, 0.394], [0, 0], [1.931, -0.98], [1.297, -0.827], [1.427, -2.058], [0, -3.018], [-0.071, -0.795], [-0.25, -0.684], [0.73, -1.461], [0.957, -1.83], [0.255, -1.805], [0, -1.902], [-0.143, -1.401], [-0.541, -1.159], [-0.484, -0.895], [-0.248, -0.742], [0, -0.86], [0, 0], [-0.275, -1.926], [-2, -4], [-2, -1], [-2, 0], [-3, 3], [-1, 3], [1, 2], [-1, 4], [-2, 7], [2, 6], [0.578, 0.992], [0.269, 1.567], [0, 1.832], [0, 0], [0.783, 1.337], [0.758, 0.845]], "o": [[0, 0], [-2.128, 0], [-1.37, 0.696], [-2.058, 1.311], [-1.809, 2.608], [0, 0.794], [0.068, 0.76], [0.585, 1.6], [-0.924, 1.849], [-0.836, 1.599], [-0.267, 1.887], [0, 1.404], [0.131, 1.28], [0.431, 0.922], [0.355, 0.655], [0.278, 0.833], [0, 0], [0, 1.941], [0, 0.001], [2, 4], [2, 1], [2, 0], [3, -3], [1, -3], [-1, -2], [1, -4], [2, -4], [-0.591, -0.984], [-0.776, -1.335], [-0.308, -1.805], [0, 0], [0, -1.556], [-0.572, -0.98], [-2.105, -2.35]], "v": [[4.102, -42.985], [0.716, -42.985], [-4.938, -42.035], [-8.952, -39.78], [-14.616, -34.942], [-15.898, -26.158], [-15.96, -23.775], [-15.259, -21.856], [-15.898, -16.985], [-18.921, -11.58], [-20.835, -6.481], [-20.898, -0.666], [-20.89, 3.548], [-19.513, 7.181], [-18.147, 9.913], [-17.049, 12.001], [-16.898, 14.514], [-16.898, 19.981], [-16.898, 26.015], [-13.898, 37.015], [-8.898, 42.015], [-2.898, 42.015], [7.102, 36.015], [13.102, 29.015], [14.102, 17.015], [12.102, 5.015], [16.102, -1.985], [19.102, -15.985], [17.351, -18.95], [15.133, -23.137], [15.102, -28.842], [15.102, -31.887], [14.166, -35.863], [12.168, -38.614]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.102000000898, 0.725, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.352, 43.266], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Слой 8 Outlines", "parent": 25, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28.801, "s": [29.3]}, {"t": 36, "s": [47.9]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 7;\nfreq = 1.8;\ndecay = 7;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [19.499, -14.539, 0], "ix": 2}, "a": {"a": 0, "k": [5.5, 27, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 21.6, "s": [0, 0, 100]}, {"t": 26.400390625, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5, 13], [-5, 13], [-5, -13], [5, -13]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.57599995931, 0.430999995213, 0.282000014361, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [5.25, 13.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Слой 6 Outlines", "parent": 25, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [29.3]}, {"t": 31.19921875, "s": [47.9]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 4;\nfreq = 1.8;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [-0.535, 3.563, 0], "ix": 2}, "a": {"a": 0, "k": [18.5, 28, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 21.6, "s": [0, 0, 100]}, {"t": 26.400390625, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-18, -13.5], [18, -13.5], [15, 13.5], [-15, 13.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.709999952129, 0.486000001197, 0.128999986836, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18.25, 13.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "Shape Layer 4", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [257, 176], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.792156862745, 0.803921568627, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.5, 62], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "Слой 11 Outlines", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.6, "s": [472, 518.25, 0], "to": [0, -23.5, 0], "ti": [0, 23.5, 0]}, {"t": 43.19921875, "s": [472, 377.25, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 5;\nfreq = 2;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [39.529, 69.779, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.5, 63.75], [-33.5, 63.75], [-33.5, -63.75], [33.5, -63.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.282000014361, 0.246999987434, 0.328999986836, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 8.527, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.592000026329, 0.913999968884, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [39.529, 69.779], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 3, "nm": "Null 4", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.551, "y": 0}, "t": 28.801, "s": [148, 440.1, 0], "to": [0, -23.35, 0], "ti": [0, 23.35, 0]}, {"t": 38.400390625, "s": [148, 300, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 4;\nfreq = 1.8;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "Shape Layer 3", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [257, 176], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.792156862745, 0.803921568627, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.5, 62], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "Слой 10 Outlines", "parent": 35, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [204.654, 63, 0], "ix": 2}, "a": {"a": 0, "k": [29.163, 43.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.059, -43], [-28.913, 43], [9.059, 43], [28.913, -43]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.823999980852, 0.952999997606, 0.976000019148, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.163, 43.249], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "Shape Layer 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [257, 176], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.792156862745, 0.803921568627, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.5, 62], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 4, "nm": "Слой 9 Outlines", "parent": 35, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [200.659, 63, 0], "ix": 2}, "a": {"a": 0, "k": [67.698, 55.04, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[60.659, 48], [-60.659, 48], [-60.659, -48], [60.659, -48]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.282000014361, 0.246999987434, 0.328999986836, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 9.956, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.592000026329, 0.913999968884, 0.987999949736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [67.698, 55.04], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 460, 0], "ix": 2}, "a": {"a": 0, "k": [2.5, 160.5, 0], "ix": 1}, "s": {"a": 0, "k": [101.926, 94.737, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 12, "s": [0, 19]}, {"t": 24, "s": [305, 19]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.792156862745, 0.803921568627, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.5, 160.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "Слой 5 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [401.25, 456.75, 0], "ix": 2}, "a": {"a": 0, "k": [128, 11, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 28.801, "s": [100, 0, 100]}, {"t": 45.599609375, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 5;\nfreq = 1.5;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.005, 0], [0, 0], [0, 0.005], [0, 0], [-0.006, 0], [0, 0], [0, -0.005]], "o": [[0, 0.005], [0, 0], [-0.006, 0], [0, 0], [0, -0.005], [0, 0], [0.005, 0], [0, 0]], "v": [[127.5, 4.99], [127.49, 5], [-127.49, 5], [-127.5, 4.99], [-127.5, -4.99], [-127.49, -5], [127.49, -5], [127.5, -4.99]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.722000002394, 0.725, 0.74900004069, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [127.75, 5.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "Слой 4 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [401.25, 446.75, 0], "ix": 2}, "a": {"a": 0, "k": [127, 27, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 26.4, "s": [100, 0, 100]}, {"t": 40.80078125, "s": [102, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 5;\nfreq = 1.5;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-126.5, 13], [126.5, 13], [126.5, -13], [-126.5, -13]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.769000004787, 0.305999995213, 0.149000010771, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [126.75, 13.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 4, "nm": "Слой 3 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [401.25, 446.75, 0], "ix": 2}, "a": {"a": 0, "k": [128, 222, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 19.199, "s": [100, 0, 100]}, {"t": 33.599609375, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 5;\nfreq = 1.5;\ndecay = 8;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, $bm_div(amp, 100)), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[127.5, 110.5], [-127.5, 110.5], [-127.5, -110.5], [127.5, -110.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.607999973671, 0.277999997606, 0.109999997008, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [127.75, 110.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240.24024024024, "st": 0, "bm": 0}], "markers": [], "chars": [{"ch": "O", "size": 12, "style": "Regular", "w": 68.16, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [5.387, 5.746], [8.594, 0], [5.192, -5.762], [0, -8.659], [0, 0], [-5.193, -5.729], [-8.301, 0], [-5.388, 5.73], [0, 8.691]], "o": [[0, -8.691], [-5.388, -5.745], [-8.301, 0], [-5.193, 5.762], [0, 0], [0, 8.691], [5.192, 5.73], [8.594, 0], [5.387, -5.729], [0, 0]], "v": [[62.598, -41.846], [54.517, -63.501], [33.545, -72.119], [13.306, -63.477], [5.518, -41.846], [5.518, -29.199], [13.306, -7.568], [33.545, 1.025], [54.517, -7.568], [62.598, -29.199]], "c": true}, "ix": 2}, "nm": "O", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [3.499, -4.15], [5.957, 0], [3.336, 4.167], [0, 6.576], [0, 0], [-3.337, 4.167], [-5.599, 0], [-3.516, -4.166], [0, -6.51]], "o": [[0, 6.609], [-3.5, 4.15], [-5.599, 0], [-3.337, -4.166], [0, 0], [0, -6.51], [3.336, -4.166], [5.924, 0], [3.516, 4.167], [0, 0]], "v": [[52.979, -29.199], [47.729, -13.062], [33.545, -6.836], [20.142, -13.086], [15.137, -29.199], [15.137, -41.943], [20.142, -57.959], [33.545, -64.209], [47.705, -57.959], [52.979, -41.943]], "c": true}, "ix": 2}, "nm": "O", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "O", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Roboto"}, {"ch": "P", "size": 12, "style": "Regular", "w": 64.06, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.199, 3.857], [0, 6.478], [4.199, 3.906], [7.584, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[7.584, 0], [4.199, -3.857], [0, -6.38], [-4.199, -3.906], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[35.986, -28.516], [53.662, -34.302], [59.961, -49.805], [53.662, -65.234], [35.986, -71.094], [8.789, -71.094], [8.789, 0], [18.408, 0], [18.408, -28.516]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-2.393, -2.632], [0, -3.931], [2.376, -2.583], [4.817, 0], [0, 0]], "o": [[0, 0], [4.785, 0], [2.393, 2.632], [0, 3.932], [-2.377, 2.583], [0, 0], [0, 0]], "v": [[18.408, -63.525], [35.986, -63.525], [46.753, -59.577], [50.342, -49.731], [46.777, -39.959], [35.986, -36.084], [18.408, -36.084]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "P", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Roboto"}, {"ch": "E", "size": 12, "style": "Regular", "w": 58.45, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[50.977, -40.527], [18.408, -40.527], [18.408, -63.525], [55.42, -63.525], [55.42, -71.094], [8.789, -71.094], [8.789, 0], [55.957, 0], [55.957, -7.568], [18.408, -7.568], [18.408, -32.959], [50.977, -32.959]], "c": true}, "ix": 2}, "nm": "E", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "E", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Roboto"}, {"ch": "N", "size": 12, "style": "Regular", "w": 71.34, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[62.549, -71.094], [52.93, -71.094], [52.93, -16.309], [52.637, -16.211], [18.408, -71.094], [8.789, -71.094], [8.789, 0], [18.408, 0], [18.408, -54.883], [18.701, -54.98], [52.93, 0], [62.549, 0]], "c": true}, "ix": 2}, "nm": "N", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "N", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Roboto"}]}