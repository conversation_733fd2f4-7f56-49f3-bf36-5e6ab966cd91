package com.techcubics.albarkahyper.ui.adapters.profile

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Mypurchase
import com.techcubics.albarkahyper.common.IRefreshListListenerWithCalender
import com.techcubics.albarkahyper.databinding.ItemMypurchasesBinding
import com.techcubics.albarkahyper.ui.adapters.holders.profile.MyPurchaseHolderItem
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel


class MyPurchaseAdapter(
    private val context: Context,
    private var itemList: ArrayList<Mypurchase>,
    private val profileViewModel: ProfileViewModel,
    private val TAG : String
) :
    RecyclerView.Adapter<MyPurchaseHolderItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyPurchaseHolderItem {

        val itemBinding =
            ItemMypurchasesBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MyPurchaseHolderItem(itemBinding)
    }


    @SuppressLint("ResourceAsColor")
    override fun onBindViewHolder(holder: MyPurchaseHolderItem, position: Int) {
        holder.itemName.text = itemList.get(position).item
        holder.itemDate.text = itemList.get(position).date
        if (itemList.get(position).buying.equals(0)) {
            holder.itemCheckbox.isChecked = false
        } else
            holder.itemCheckbox.isChecked = true
        holder.itemCheckbox.setOnClickListener {

            profileViewModel.markAsBuying(itemList.get(position).id)

        }

        holder.itemView.setOnClickListener {
            Toast.makeText(
                context,
                context.getString(com.techcubics.style.R.string.swip),
                Toast.LENGTH_LONG
            ).show()
        }

    }

    override fun getItemCount() = itemList.size

    fun deleteItem(position: Int, refreshListListener: IRefreshListListenerWithCalender, root: ProgressBar) {
        profileViewModel.deletePurchase(itemList.get(position).id)
        refreshListListener.onItemClick(itemList.get(position).date, TAG)
        root.visibility = View.VISIBLE
    }

    fun editItem(position: Int, refreshListListener: IRefreshListListenerWithCalender) {
        profileViewModel.showPurchase(itemList.get(position).id)
        refreshListListener.onItemClick(itemList.get(position).date, TAG)
        Log.i("market","adapter"+TAG)

    }


}