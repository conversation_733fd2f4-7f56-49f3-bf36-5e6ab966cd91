package com.techcubics.albarkahyper.ui.views.home.navFragments.orders.history

import android.os.Bundle
import android.util.Log
import android.view.Gravity
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.ogaclejapan.smarttablayout.utils.v4.FragmentPagerItemAdapter
import com.ogaclejapan.smarttablayout.utils.v4.FragmentPagerItems
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.ui.views.home.navFragments.orders.viewmodels.OrdersViewModel
import com.techcubics.albarkahyper.databinding.FragmentOrdersBinding
import com.techcubics.albarkahyper.common.NetworkChangeReceiver
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.shared.constants.Constants
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class OrdersFragment : Fragment() {
    private lateinit var binding : FragmentOrdersBinding
    private val authViewModel by viewModel<AuthViewModel>()
    private val orderViewModel by viewModel<OrdersViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog : PopupDialog

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentOrdersBinding.inflate(layoutInflater,container,false)

        binding.orderToolbar.tvTitle.text = getString(R.string.myorders)
        binding.orderToolbar.tvTitle.setCompoundDrawablesWithIntrinsicBounds(null,null,null,null)
        if(NetworkChangeReceiver.isOnline(requireContext())){
            setUpTabBar()
            initViews()
            observeViews()
            binding.ordersView.visibility = View.VISIBLE
            binding.includeNetwork.networkView.visibility = View.GONE
        }else{
            binding.ordersView.visibility = View.GONE
            binding.includeNetwork.networkView.visibility = View.VISIBLE
        }

        binding.includeNetwork.refresh.setOnClickListener {

            Toast.makeText(requireContext(),"clicked", Toast.LENGTH_SHORT).show()
            Log.i("clothes","refresh clicked")
            findNavController().run {
                popBackStack()
                navigate(com.techcubics.albarkahyper.R.id.ordersFragment)
            }
        }
        return binding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){

            if(it != null){
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.ordersLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.ordertologin)
                    }
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }


        }

//        orderViewModel.pastItemClickedLiveData.observe(viewLifecycleOwner) {
//            if(it != null){
//                val args = Bundle()
//                args.putInt(Constants.ORDER_ID, it.orderId!!)
//                Log.i("clothes","past post value")
//                findNavController().navigate(com.techcubics.albarkahyper.R.id.action_ordersFragment_to_orderDetails,args)
//                orderViewModel.pastItemClickedLiveData.value = null
//            }
//
//        }
//
//        orderViewModel.currentItemClickedLiveData.observe(viewLifecycleOwner){
//            Log.i("clothes","current post value"+it)
//            if(it != null){
//                Log.i("clothes","current post value")
//                val args = Bundle()
//                args.putInt(Constants.ORDER_ID, it.orderId!!)
//                findNavController().navigate(com.techcubics.albarkahyper.R.id.action_ordersFragment_to_orderDetails,args)
//                orderViewModel.currentItemClickedLiveData.value = null
//            }
//
//        }
    }

    private fun initViews() {
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        checkAutherization()
        activity?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.action_ordersFragment_to_home)
            }
        })

    }

    private fun setUpTabBar() {
        val adapter = FragmentPagerItemAdapter(
            childFragmentManager,
            FragmentPagerItems.with(activity)
                .add(getString(R.string.current_orders), CurrentOrdersFragment::class.java)
                .add(getString(R.string.past_orders), PastOrdersFragment::class.java)
                .create()
        )

        binding.viewpager.adapter = adapter
        binding.viewpagertab.setupWithViewPager(binding.viewpager)

        for (i in 0 until binding.viewpagertab.getTabCount()) {
            val tab = (binding.viewpagertab.getChildAt(0) as ViewGroup).getChildAt(i)
            when(i){
                0-> tab.setBackgroundResource(com.techcubics.style.R.drawable.tab_shape_right)
                1-> tab.setBackgroundResource(com.techcubics.style.R.drawable.tab_shape_left)
            }

            tab.requestLayout()
        }
    }
    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
    }

}