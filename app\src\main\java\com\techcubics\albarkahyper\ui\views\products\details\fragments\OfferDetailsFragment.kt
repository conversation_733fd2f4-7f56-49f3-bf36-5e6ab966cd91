package com.techcubics.albarkahyper.ui.views.products.details.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.albarkahyper.common.*
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Offer
import com.techcubics.albarkahyper.ui.adapters.product.ProductsImagesAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.ProductDetailsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.albarkahyper.databinding.FragmentOfferDetailsBinding
import com.techcubics.albarkahyper.ui.adapters.product.OfferProductsAdapter
import com.techcubics.style.R
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class OfferDetailsFragment : Fragment(),OnItemsChangedListener , ViewTreeObserver.OnWindowFocusChangeListener {
    private var _binding: FragmentOfferDetailsBinding? = null
    private val binding get() = _binding!!
    private var offerId: Int= -1
    private var slug: String? = null
    private lateinit var imagesAdapter: ProductsImagesAdapter
    private var offer: Offer? = null
    private lateinit var addToCartProgressButton: ProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val productsAdapter: OfferProductsAdapter = OfferProductsAdapter(true)

    private val authViewModel by viewModel<AuthViewModel>()

    private val viewModel by viewModel<ProductDetailsViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var quantityButtonsController: QuantityButtonsController
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        offerId = arguments?.getInt(Constants.INTENT_ID, -1) ?: -1
        slug = arguments?.getString(Constants.INTENT_SLUG)
        _binding = FragmentOfferDetailsBinding.inflate(inflater, container, false)
        Helper.hideSystemUIWithNavigation(requireActivity().window)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        addToCartProgressButton = ProgressButton(requireContext())
        addToCartProgressButton.init(binding.offerDetailsContainer.btnAddToCart)
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)

            viewModel.getOfferDetails(slug?:offerId.toString())

            quantityButtonsController = QuantityButtonsController(
                binding.offerDetailsContainer.productQtyCartContainer.root,
                binding.offerDetailsContainer.minOrderNote,
                requireContext(),
                this,
                Int.MAX_VALUE,
                1
            )
            quantityButtonsController.setQuantity()

            checkAuthorization()
            setBtnListeners()
            setImages()
            setObservers()
        } else {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            binding.scrollView.visibility = View.GONE
            binding.nonetworkView.visibility = View.VISIBLE
        }


    }


    private fun setBtnListeners() {
        binding.offerDetailsContainer.btnAddToCart.textView.text = getString(R.string.add_to_cart)
        binding.offerImg.navigateBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.offerDetailsContainer.btnAddToCart.constraintsLayout.setOnClickListener {
            if (sharedPreferencesManager.isLoggedIn() == "true") {
                addToCart(offer)
            } else {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.offer_details_to_login)
            }
        }
        binding.offerImg.btnShare.setOnClickListener {
            val shareUrl = Constants.BASE_OFFER_SHARE_URL + slug
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            viewModel.getShareLink(
                RateTypesEnum.Offer.value,
                url = shareUrl,
                id = slug!!
            )
        }

        binding.offerDetailsContainer.branchContainer.setOnClickListener {
            offer?.let { d ->
                val bundle = Bundle()
                bundle.putInt(Constants.INTENT_ID, d.shop.id)

            }
        }
//       quantityButtonsController.setListeners()
    }

    private fun setObservers() {
        viewModel.shareLinkResponse.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            if (it != null) {
                if (it.status!!) {
                    Helper.shareLink(requireContext(), it.data!!)

                } else {
                    Helper.ShowErrorDialog(requireContext(), it.message!!)
                }
                viewModel.shareLinkResponse.value = null
            }
        }
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {

            if (it?.message?.contains("Unauthenticated")!!) {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.offer_details_to_login)
            } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }

        }
        viewModel.offerDetailsResponse.observe(viewLifecycleOwner) { offerDetails ->
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            offer = offerDetails.data
            if (offerDetails.message.toString() != Constants.SERVER_ERROR) {
                binding.tvMessage.visibility = View.GONE
                if (offer != null) {
                    offerId = offerDetails?.data?.offerID?:-1
                    slug = offerDetails?.data?.slug
                    binding.animation.visibility = View.GONE
                    binding.scrollView.visibility = View.VISIBLE
                    setData()
                } else {
                    binding.animation.visibility = View.VISIBLE
                    binding.animation.setAnimation(R.raw.empty_box_lottie)
                    binding.scrollView.visibility = View.GONE
                }
            } else {
                binding.tvMessage.visibility = View.VISIBLE
                binding.tvMessage.text = getString(R.string.server_error)
                binding.animation.visibility = View.VISIBLE
                binding.animation.setAnimation(R.raw.lottie_error)
                binding.scrollView.visibility = View.GONE
            }


        }
        viewModel.addCartResponse.observe(viewLifecycleOwner)
        {
            if (it != null && it.message.toString() != Constants.SERVER_ERROR) {
                if (it.status == true) {
                    addToCartProgressButton.btnFinishedSuccessfully(
                        getString(R.string.add_to_cart),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(getString(R.string.added_to_cart))
                } else {
                    addToCartProgressButton.btnFinishedFailed(getString(R.string.add_to_cart), null)
                    bottomSheetAlertDialog.showDialog(it.message.toString())
                }
                viewModel.addCartResponse.value = null
            } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                addToCartProgressButton.btnFinishedFailed(getString(R.string.add_to_cart), null)
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }
        }
    }

    private fun setData() {
        val minQty=if ((offer?.quantityCart ?:1)>0){
            offer?.quantityCart ?:1
        }else{
            1
        }
        quantityButtonsController.updateQuantity(minQty)
        quantityButtonsController.isBtnDecreaseEnabled()
        val size = offer?.Images?.size ?: 0
        if (size <= 1) {
            binding.offerDetailsContainer.dotsIndicator.visibility = View.GONE
        }
        imagesAdapter.updateImagesAndVideo(
            offer!!.Images,
            null
        )

        Helper.loadImage(
            requireContext(),
            offer!!.shop.logo,
            binding.offerDetailsContainer.galleryImg
        )

        binding.offerDetailsContainer.galleryName.text = offer!!.shop.name
        binding.offerDetailsContainer.tvTitle.text = offer!!.name
        calcPrice()
        offer?.price?.let { setPrice(it,binding.offerDetailsContainer.tvPrice) }
        setProductList()

    }
    override fun calcPrice() {
        val price = offer?.price?:0f
        val priceCents = (price * 100f).toInt()
        val totalPriceCents = priceCents * quantityButtonsController.getQuantity()
        totalPriceCents.toFloat() / 100f
        setPrice((totalPriceCents.toFloat() / 100f),binding.offerDetailsContainer.price)
    }

    private fun setPrice(price: Float,priceTextView: TextView) {
        "${java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(price)} ${getString(R.string.currency_name)}".also{
            priceTextView.text = it
        }
    }

    private fun setProductList() {
        productsAdapter.items = offer?.products!!
        binding.offerDetailsContainer.productRecyclerView.adapter = productsAdapter
        binding.offerDetailsContainer.productRecyclerView.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL,false)
    }

    private fun setImages() {
        imagesAdapter = ProductsImagesAdapter(
            requireContext(),
            listOf()
        )
        binding.offerImg.viewPager2.adapter = imagesAdapter
        binding.offerDetailsContainer.dotsIndicator.attachTo(binding.offerImg.viewPager2)
    }

    private fun addToCart(offer: Offer?) {
        offer.let { off ->
//            binding.progressBar.visibility = View.VISIBLE
            addToCartProgressButton.btnActivated()
            viewModel.addToCart(
                EndPointConstants.add_offer,
                shopId = off?.shop!!.id,
                modelType = off.modelType,
                modelId = off.offerID,
                qty = quantityButtonsController.getQuantity()
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.root.viewTreeObserver?.removeOnWindowFocusChangeListener(this)
        _binding = null
        Helper.exitFullScreen(requireContext())
    }
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUIWithNavigation(requireActivity().window)
        else Helper.showSystemUIWithNavigation(requireActivity().window)
    }
    private fun checkAuthorization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}