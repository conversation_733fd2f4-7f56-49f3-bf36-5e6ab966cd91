package com.techcubics.data.model.pojo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ProductByCategoryStoreData(

    @SerializedName("id")
    val id: Int? = -1,
    @SerializedName("shop_id")
    val shopID: Int? = -1,
    @SerializedName("branch_type_id")
    val branchTypeID: Int? = -1,
    @SerializedName("branch_type_name")
    val branchTypeName: String? = "",
    @SerializedName("name")
    val name: String? = "",
    @SerializedName("slug")
    var slug: String? = null,
    @SerializedName("description")
    val description: String? = "",
    @SerializedName("logo")
    val logo: String? = "",
    @SerializedName("address")
    val address: String? = "",
    @SerializedName("is_fav")
    val isFavorite: Boolean? = false,
    @SerializedName("rate")
    val rate: Float? = 0f,
    @SerializedName("rate_count")
    val rate_count: String? = "",
    @SerializedName("open")
    val isOpened: Boolean? = false,
    @SerializedName("link_google_play")
    val linkGooglePlay: String? = "",
    @SerializedName("link_apple_store")
    val linkAppleStore: String? = "",
    @SerializedName("qr_image")
    val qrImage: String? = "",
    @SerializedName("lat")
    val latitute: String? = "",
    @SerializedName("lng")
    val longitude: String? = "",
    @SerializedName("share_link")
    var shareLink: String? = null

) : Parcelable
