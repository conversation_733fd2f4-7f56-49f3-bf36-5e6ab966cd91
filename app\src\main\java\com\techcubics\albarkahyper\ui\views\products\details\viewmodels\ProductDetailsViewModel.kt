package com.techcubics.albarkahyper.ui.views.products.details.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.GetShareLinkCallRequest
import com.techcubics.data.model.requests.home.AddRateRequest2
import com.techcubics.data.model.requests.home.AddRemoveFavoriteRequest
import com.techcubics.data.model.requests.home.ProductDetailsRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.favourite.FavoritesRepo
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.data.repos.store.StoresRepo
import com.techcubics.shared.enums.FavoriteTypesEnum
import com.techcubics.shared.enums.RateTypesEnum
import kotlinx.coroutines.launch

class ProductDetailsViewModel(
    private val productsRepo: ProductsRepo,
    private val favRepo: FavoritesRepo,
    private val storesRepo: StoresRepo
) : ViewModel() {

    val productDetailsResponse: MutableLiveData<BaseResponse<ProductData>> by lazy {
        MutableLiveData()
    }
    val discountDetailsResponse: MutableLiveData<BaseResponse<Discount>> by lazy {
        MutableLiveData()
    }
    val offerDetailsResponse: MutableLiveData<BaseResponse<Offer>> by lazy {
        MutableLiveData()
    }
    val saveDetailsResponse: MutableLiveData<BaseResponse<Save>> by lazy {
        MutableLiveData()
    }
    val addRemoveFavoriteResponse: MutableLiveData<BaseResponse<String>> by lazy {
        MutableLiveData()
    }
    val addRateResponse: MutableLiveData<BaseResponse<Any>> by lazy {
        MutableLiveData()
    }
    val addCartResponse: MutableLiveData<BaseResponse<CartData>> by lazy {
        MutableLiveData()
    }
    val shareLinkResponse = MutableLiveData<BaseResponse<String>>()

//    private val productsRepo = ProductsRepoImpl()
//    private val favRepo = FavoritesRepoImpl()
    // private val token = "Bearer 1232|1BlZiyvllA4g9TAo6ysvlZkzkRTMk2DN1vlQmSqK"

    fun getProductDetails(productId: String) {
        viewModelScope.launch {
            val request = ProductDetailsRequest(productId)
            val rs = productsRepo.getProductDetails(request)
            productDetailsResponse.postValue(rs!!)
        }

    }

    fun addRemoveFav(id: Int) {
        viewModelScope.launch {
            val request = AddRemoveFavoriteRequest(type = FavoriteTypesEnum.Product.value, id = id)
            val rs = favRepo.addRemoveFav(request)
            addRemoveFavoriteResponse.postValue(rs!!)
        }

    }

    fun addRate(id: Int, type: RateTypesEnum, rate: Int, comment: String) {
        viewModelScope.launch {
            val request =
                AddRateRequest2(rateID = id, type = type.value, degree = rate, comment = comment)
            val rs = productsRepo.addRate(request)
            rs?.let { addRateResponse.postValue(it) }
        }
    }

    fun addToCart(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        sizeId: Int,
        colorId: Int
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId, modelType, modelId, qty, sizeId, colorId)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }

    fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(shopId, modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }

    fun getDiscountDetails(discountId: Int) {
        viewModelScope.launch {
            val rs = productsRepo.getDiscountDetails(discountId)
            rs?.let { discountDetailsResponse.postValue(it) }
        }
    }

    fun getOfferDetails(offerId: String) {
        viewModelScope.launch {
            val rs = productsRepo.getOfferDetails(offerId)
            rs?.let { offerDetailsResponse.postValue(it) }
        }
    }

    fun getSaveDetails(saveId: String) {
        viewModelScope.launch {
            val rs = productsRepo.getSaveDetails(saveId)
            rs?.let { saveDetailsResponse.postValue(it) }
        }
    }

    fun getShareLink(type: String, url: String, id: String) {
        viewModelScope.launch {
            val request = GetShareLinkCallRequest(
                type = type,
                url = url, item_id = id
            )
            val rs = storesRepo.getShareLink(request)
            shareLinkResponse.postValue(rs!!)
        }
    }
}