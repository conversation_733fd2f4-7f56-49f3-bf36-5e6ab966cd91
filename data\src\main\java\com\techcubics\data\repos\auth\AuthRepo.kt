package com.techcubics.data.repos.auth

import com.techcubics.data.model.pojo.ForgetPasswordConfirmResponseData
import com.techcubics.data.model.pojo.ForgetPasswordResponseData
import com.techcubics.data.model.pojo.SettingData
import com.techcubics.data.model.pojo.User
import com.techcubics.data.model.requests.auth.*
import com.techcubics.data.remote.BaseResponse

interface AuthRepo {
    suspend fun checkAutherization(): BaseResponse<User>?
    suspend fun login(request: LoginRequest): BaseResponse<User>?
    suspend fun register(request: RegisterRequest): BaseResponse<User>?
    suspend fun socialLoginCall(request: SocialRequest): BaseResponse<User>?
    suspend fun forgetPasswordPhoneCall(request: ForgetPasswordRequest): BaseResponse<ForgetPasswordResponseData>?
    suspend fun forgetPasswordEmailCall(request: ForgetPasswordRequest): BaseResponse<ForgetPasswordResponseData>?
    suspend fun forgetPasswordConfirmCall(request: ForgetPasswordConfirmRequest): BaseResponse<ForgetPasswordConfirmResponseData>?
    suspend fun forgetPasswordResetCall(request: ForgetPasswordResetRequest): BaseResponse<Nothing>?
    suspend fun logout(): BaseResponse<String>?
    suspend fun updatePasswordResetCall(request: UpdatePasswordRequest) : BaseResponse<Nothing>?
    suspend fun getSettings():BaseResponse<SettingData>?

}