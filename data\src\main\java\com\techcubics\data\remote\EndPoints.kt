package com.techcubics.data.remote
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.*
import com.techcubics.data.model.requests.auth.*
import com.techcubics.data.model.requests.chat.SendLiveRequest
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.model.requests.profile.ContactusRequest
import com.techcubics.data.model.requests.profile.JoinusRequest
import com.techcubics.data.model.requests.profile.MyPurchaseRequset
import com.techcubics.data.model.requests.profile.location.AddLoactionRequest
import com.techcubics.data.model.requests.profile.location.UpdateLocationRequest
import com.techcubics.data.model.requests.profile.location.UpdatePurchaseRequest
import com.techcubics.shared.constants.EndPointConstants
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.*

interface EndPoints {

    //Auth
    //autherization(if token expired or not)
    @GET(EndPointConstants.profile)
    suspend fun checkAutherization(): Response<BaseResponse<User>>

    @POST(EndPointConstants.login)
    suspend fun normalloginCall(@Body loginRequest: LoginRequest): Response<BaseResponse<User>>

    @POST(EndPointConstants.register)
    suspend fun registerCall(@Body registerRequest: RegisterRequest): Response<BaseResponse<User>>

    @POST(EndPointConstants.Social)
    suspend fun socialLoginCall(@Body socialRequest: SocialRequest): Response<BaseResponse<User>>

    @POST(EndPointConstants.Forgetget_password)
    suspend fun forgetPasswordMobileCall(@Body forgetPasswordRequest: ForgetPasswordRequest) : Response<BaseResponse<ForgetPasswordResponseData>>

    @POST(EndPointConstants.Forget_password_web)
    suspend fun forgetPasswordEmailCall(@Body forgetPasswordRequest: ForgetPasswordRequest): Response<BaseResponse<ForgetPasswordResponseData>>

    @POST(EndPointConstants.Confirm_password)
    suspend fun forgetPasswordConfirmCall(@Body forgetPasswordConfirmRequest: ForgetPasswordConfirmRequest): Response<BaseResponse<ForgetPasswordConfirmResponseData>>

    @POST(EndPointConstants.reset_password)
    suspend fun forgetPasswordResetCall(@Body forgetPasswordResetRequest: ForgetPasswordResetRequest): Response<BaseResponse<Nothing>>

    @POST(EndPointConstants.update_password)
    suspend fun updatePasswordResetCall(@Body updatePasswordRequest: UpdatePasswordRequest): Response<BaseResponse<Nothing>>

    @GET(EndPointConstants.logout)
    suspend fun logoutCall(): Response<BaseResponse<String>>


    @POST(EndPointConstants.firebase_token)
    suspend fun updateFireBaseTokenCall(

      @Body request:UpdateFireBaseTokenCallRequest
    ): Response<BaseResponse<String>>

    //profile
    @GET(EndPointConstants.profile)
    suspend fun profileCall(): Response<BaseResponse<User>>

    @Multipart
    @POST(EndPointConstants.update_profile)
    suspend fun updateProfileCall(
        @Part("name") name: RequestBody,
        @Part("phone") phone: RequestBody,
        @Part avatar: MultipartBody.Part?,
        @Part("address") address: RequestBody,
        @Part("country_code") country_code: RequestBody,
        @Part("facility_name") facilityName : RequestBody,
        @Part("branch_type") branch_type: RequestBody,
        @Part("lat") lat : RequestBody,
        @Part("lng") lng : RequestBody,
        @Part("country_id") country_id : RequestBody,
        @Part("governorate_id") governorate_id : RequestBody,
        @Part("region_id") region_id : RequestBody
    ): Response<BaseResponse<User>>

    @GET(EndPointConstants.countries)
    suspend fun getCountries(): Response<BaseResponse<List<Countries>>>

    @GET(EndPointConstants.governorate_by_country)
    suspend fun getGovernerateByCountryId(@Query("country_id") country_id: Int): Response<BaseResponse<ArrayList<Governerate>>>

    @GET(EndPointConstants.region_by_governerate)
    suspend fun getregionBygovernorateId(@Query("governorate_id") governorate_id: Int, @Query("country_id") country_id: Int): Response<BaseResponse<ArrayList<Regoin>>>

    @POST(EndPointConstants.store_location)
    suspend fun storeLocation(@Body addLocationRequest: AddLoactionRequest): Response<BaseResponse<String>>

    @POST(EndPointConstants.update_location)
    suspend fun updateLocation(@Body updateLocationRequest: UpdateLocationRequest): Response<BaseResponse<String>>

    @GET(EndPointConstants.delete_location)
    suspend fun deleteLocation(@Query("location_id") location_id: Int, @Query("country_id") country_id: Int, @Query("country_code") country_code: String): Response<BaseResponse<String>>

    @GET(EndPointConstants.location)
    suspend fun listLocation(@Query("country_id") country_id: Int,@Query("country_code") country_code: String): Response<BaseResponse<ArrayList<Locations>>>

    @GET(EndPointConstants.show_location)
    suspend fun showLocation(@Query("location_id") location_id: Int, @Query("country_id") country_id: Int, @Query("country_code") country_code: String): Response<BaseResponse<Locations>>

    @POST(EndPointConstants.join_us)
    suspend fun joinus(@Body joinusRequest: JoinusRequest): Response<BaseResponse<String>>

    @POST(EndPointConstants.contact_us)
    suspend fun contactUsCall(@Body contactusRequest: ContactusRequest): Response<BaseResponse<Nothing>>

    @GET("order/{order_id}")
    suspend fun getOrderDetails(@Path("order_id") order_id: Int): Response<BaseResponse<Order>>

    @GET(EndPointConstants.previous_order)
    suspend fun getPastOrders(): Response<BaseResponse<ArrayList<Order>>>

    @GET(EndPointConstants.current_order)
    suspend fun getCurrentOrders(): Response<BaseResponse<ArrayList<Order>>>

    @GET(EndPointConstants.languages)
    suspend fun languagesCall():Response<BaseResponse<List<Language>>>

    @POST(EndPointConstants.store_purchase)
    suspend fun addPurchase(@Body purchaseRequset: MyPurchaseRequset):Response<BaseResponse<String>>

    @GET(EndPointConstants.purchases_list)
    suspend fun getPurchasesList(@Query("date") date: String):Response<BaseResponse<ArrayList<Mypurchase>>>

    @GET(EndPointConstants.show_purchase)
    suspend fun showPurchase(@Query("listing_id") listing_id: Int):Response<BaseResponse<Mypurchase>>

    @GET(EndPointConstants.delete_purchase)
    suspend fun deleteLocation(@Query("listing_id") listing_id: Int):Response<BaseResponse<String>>

    @POST(EndPointConstants.update_purchase)
    suspend fun updatePurchase(@Body updatePurchaseRequest: UpdatePurchaseRequest): Response<BaseResponse<String>>

    @GET(EndPointConstants.purchase_mark_buying)
    suspend fun markPurchaseAsBuying(@Query("listing_id") listing_id: Int): Response<BaseResponse<String>>


    //--------------------------------------------------------------------------


    //Home
    @GET(EndPointConstants.home)
    suspend  fun homeCall():Response<BaseResponse<HomeData>>
    //pages
    @GET(EndPointConstants.pages)
    suspend fun pagesCall(@Query("page_name") page_name: String): Response<BaseResponse<PageData>>

    @GET(EndPointConstants.offers)
    suspend fun offersCall(@Query("price") price:String,@Query("page") page:Int):Response<BaseResponse<MutableList<Offer>>>
    @GET(EndPointConstants.saves)
    suspend fun savesCall(@Query("price") price:String,@Query("page") page:Int):Response<BaseResponse<MutableList<Save>>>
    /*@GET(EndPointConstants.discounts)
    suspend fun discountsCall(@Query("price") price:String,@Query("page") page:Int):Response<BaseResponse<MutableList<Discount>>>*/


    @GET(EndPointConstants.branch_types)
    suspend fun storeTypesCall():Response<BaseResponse<NearStoreTypes>>

    @GET(EndPointConstants.categories)
    suspend fun categoriesCall():Response<BaseResponse<MutableList<Category>>>


    @POST(EndPointConstants.add_rate)
    suspend fun addRateCall(

        @Body request: AddRateRequest,
       ):Response<BaseResponse<String>>

    //favorites

    @POST(EndPointConstants.add_favourite)
    suspend fun addRemoveFavoriteCall(
        @Body request:AddRemoveFavoriteRequest
    ): Response<BaseResponse<String>>

    @GET(EndPointConstants.myFavourites)
    suspend fun myFavoriteProductsCall( @Query("type") type:String,@Query("page") page:Int):Response<BaseResponse<MutableList<FavouriteProduct>>>
    @GET(EndPointConstants.myFavourites)
    suspend fun myFavoriteStoresCall(@Query("type") type:String,@Query("page") page:Int):Response<BaseResponse<MutableList<FavouriteStore>>>

    //stores

    @GET(EndPointConstants.shops_search)
    suspend fun getStoresByCategory(@Query("branch_type_id") branch_type_id:Int, @Query("search") key_search:String?=null, @Query("page") page:Int):Response<BaseResponse<MutableList<StoreByCategoryData>>>

    @POST(EndPointConstants.furniture_nearby)
    suspend fun nearbyStoresCall( @Body request:NearbyStoresCallRequest):Response<BaseResponse<MutableList<StoresNearbyData>>>


    @POST(EndPointConstants.furnitures)
    suspend fun nearbyStoresByWordCall(@Body request:NearbyStoresByWordCallRequest):Response<BaseResponse<StoreNearbyByWordData>>

    @GET(EndPointConstants.furniture+"/{id}")
    suspend fun storeDetailsCall(@Path("id") id:String):Response<BaseResponse<StoreDetailsData>>


    @POST(EndPointConstants.details_by_qrcode)
    suspend fun storeDetailsByQRCall( @Body request:StoreDetailsByQRCallRequest):Response<BaseResponse<StoreDetailsData>>


    @POST(EndPointConstants.get_share_link)
    suspend fun getShareLinkCall( @Body request:GetShareLinkCallRequest):Response<BaseResponse<String>>

    //products
    @POST(EndPointConstants.product_search)
    suspend fun productSearchCall(@Body request : ProductSearchRequest): Response<BaseResponse<List<ProductSearchData>>>


    @GET(EndPointConstants.product_by_category+"/{cate_id}/{branch_type_id}/{store_id}")
    suspend fun productByCategoryCall(
        @Path("cate_id") categoryID: Int,
        @Path("branch_type_id") branch_type_id: Int,
        @Path("store_id") storeID: Int,
        @Query("paginator")paginator:Int=1,
        @Query("page") page:Int,
        @Query("price") price: String
    ): Response<BaseResponse<MutableList<ProductDetailsDto>>>

    @GET(EndPointConstants.product_by_sub_category+"/{subCat_id}/{branch_type_id}/{store_id}")
    suspend fun productBySubCategoryCall(
        @Path("subCat_id") subCatId: Int,
        @Path("branch_type_id") branch_type_id: Int,
        @Path("store_id") storeID: Int,
        @Query("paginator")paginator:Int=1,
        @Query("page") page:Int,
        @Query("price") price: String
    ): Response<BaseResponse<MutableList<ProductDetailsDto>>>

    @GET(EndPointConstants.p+"/{store_id}")
    suspend fun storeDiscountsCall(@Path("store_id") storeID: Int,@Query("page") page: Int):Response<BaseResponse<MutableList<ProductDetailsDto>>>

     @GET(EndPointConstants.most_wanted_products+"/{store_id}/{category_id}")
     suspend fun mostWantedProductsCall(
         @Path("store_id") store_id: Int,
         @Path("category_id") category_id: Int,
         @Query("page") page: Int
     ):Response<BaseResponse<MutableList<ProductDetailsDto>>>

    @GET(EndPointConstants.product_colors)
    suspend fun productColorsCall():Response<BaseResponse<MutableList<ColorObject>>>

    @GET(EndPointConstants.product_colors_sizes)
    suspend fun productColorsSizesCall():Response<BaseResponse<ProductColorsSizesData>>

    //product details
    @GET(EndPointConstants.product_details)
    suspend fun getProductDetails(
        @Path("id") productID: String

    ): Response<BaseResponse<ProductData>>

    @GET(EndPointConstants.rates)
    suspend fun getRates(
        @Query("model_type") type: String,
        @Query("model_id") id:Int

    ): Response<BaseResponse<RatesData>>

    @POST("${EndPointConstants.cart}/{route}")
    suspend fun addToCart(
        @Path("route") pathEndPoint:String,
        @Body request: AddCartRequest

    ): Response<BaseResponse<CartData>>

    @GET(EndPointConstants.cart)
    suspend fun getCart(

    ): Response<BaseResponse<ArrayList<CartData>>>

    @POST(EndPointConstants.add_rate)
    suspend fun addRate(
        @Body request2: AddRateRequest2,

    ): Response<BaseResponse<Any>>

    @POST(EndPointConstants.remove_cart_item)
    suspend fun removeModelType(

        @Body request: ItemRemovingRequest
    ): Response<BaseResponse<CartData>>


    @POST(EndPointConstants.submit_coupon)
    suspend fun submitCoupon(

      @Body request:SubmitCouponRequest
    ): Response<BaseResponse<Coupon>>


    @POST(EndPointConstants.select_location)
    suspend fun selectLocation(
        @Body request:SelectLocationRequest
    ): Response<BaseResponse<SelectedLocationData>>

    @POST(EndPointConstants.checkout)
    suspend fun checkout(
        @Body request:CheckoutRequest

    ): Response<BaseResponse<Order>>

    @GET(EndPointConstants.discount_details)
    suspend fun getDiscountDetails(
        @Path("id") discountId: Int,

    ): Response<BaseResponse<Discount>>

    @GET(EndPointConstants.save_details)
    suspend fun getSaveDetails(
        @Path("id") saveId: String,

    ): Response<BaseResponse<Save>>

    @GET(EndPointConstants.offer_details)
    suspend fun getOfferDetails(
        @Path("id") offerId: String,

    ): Response<BaseResponse<Offer>>

    //support chat
    @GET(EndPointConstants.ticket)
    suspend fun ticketCall():Response<BaseResponse<MutableList<SupportTicketData>>>

    @GET(EndPointConstants.live_message_send)
    suspend fun liveGetAllChats(): Response<BaseResponse<MutableList<LiveChatRoomData>>>

    @POST(EndPointConstants.ticket_send)
    suspend fun ticketSendCall(@Body request:TicketSendCallRequest ): Response<BaseResponse<SendSupportData>>
    //orders chat
    @GET(EndPointConstants.chats)
    suspend fun roomsCall():Response<BaseResponse<MutableList<OrderRoomsData>>>

    @POST(EndPointConstants.message_send)
    suspend fun orderSendCall(@Body request:OrderSendCallRequest ): Response<BaseResponse<SendOrderData>>
    @GET(EndPointConstants.room_messages+"/{order_id}")
    suspend fun roomMessagesCall(@Path("order_id") order_id:Int):Response<BaseResponse<OrderMessageData>>
    //live chat
    @GET(EndPointConstants.live_chats+"/{store_id}")
    suspend fun liveChatMessagesCall(@Path("store_id") storeID:Int,@Query("country_id") country_id:Int): Response<BaseResponse<MutableList<LiveMessageData>>>

    @POST(EndPointConstants.live_message_send)
    suspend fun liveSendCall(@Query("country_id") country_id:Int,@Body request: SendLiveRequest): Response<BaseResponse<Any>>

    @GET(EndPointConstants.delivery_areas)
    suspend fun getDeliveryAreasByFurnitureId(
        @Path("furId") furnitureId: Int,
        @Query("country_id") countryId: Int
    ):Response<BaseResponse<ArrayList<DeliveryAreaId>>>

    @GET(EndPointConstants.governorate_delivery_areas)
    suspend fun getGovernorateAreasByDeliveryAreaId(
        @Path("deliveryId") deliveryId: Int,
        @Query("country_id") countryId: Int
    ):Response<BaseResponse<ArrayList<Governerate>>>

    @GET(EndPointConstants.region_delivery_area)
    suspend fun getRegionByGovernorateAndDeliveryAreaId(
        @Path("govId") governorateId: Int,
        @Path("deliveryId") deliveryId: Int,
        @Query("country_id") countryId: Int
    ):Response<BaseResponse<ArrayList<Regoin>>>


    @GET(EndPointConstants.notifications)
    suspend fun notifications(@Query("page") page:Int):Response<BaseResponse<MutableList<Notification>>>

    @GET(EndPointConstants.products_by_subcategory)
    suspend fun getProductsBySubCategory(
        @Path("shopID") shopID: Int,
        @Path("subCatId") subCategoryId: Int

    ): Response<BaseResponse<ArrayList<StoreDetailsMenuProductData>>>

    @Multipart
    @POST(EndPointConstants.prescription_add)
    suspend fun prescriptionAddCall(

        @Part("shop_id") shop_id:RequestBody,
        @Part avatar: MultipartBody.Part?,

    ): Response<BaseResponse<Nothing>>

    @GET(EndPointConstants.settings)
    suspend fun getSettings(
    ): Response<BaseResponse<SettingData>>

    @GET(EndPointConstants.branch_types)
    suspend fun nearStoreTypesCall():Response<BaseResponse<NearStoreTypes>>

    @GET(EndPointConstants.branchTypesCategories+"/{id}")
    suspend fun getBranchTypesCategories(
        @Path("id") id:Int
    ): Response<BaseResponse<MutableList<Category>>>
    @GET(EndPointConstants.categories+"/{id}")
    suspend fun getCategoriesByShopId(@Path("id")shopId:Int): Response<BaseResponse<MutableList<Category>>>
    @GET(EndPointConstants.sub_categories+"/{id}")
    suspend fun getSubCategoriesByCatId(@Path("id")catId:Int): Response<BaseResponse<MutableList<SubCategory>>>

    @GET(EndPointConstants.banners)
    suspend fun getBanners(
    ): Response<BaseResponse<MutableList<BannerData>>>

    @GET(EndPointConstants.banners_home)
    suspend fun getHomeBanner() : Response<BaseResponse<MutableList<BannerData>>>

    @GET(EndPointConstants.read_notifications)
    suspend fun readNotifications(
    ): Response<BaseResponse<Any>>

    @GET(EndPointConstants.remove_all_cart)
    suspend fun removeAllCart(): Response<BaseResponse<CartData>>



}