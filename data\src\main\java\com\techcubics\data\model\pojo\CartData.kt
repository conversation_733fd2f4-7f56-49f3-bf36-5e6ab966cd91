package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName


data class CartData(
    @SerializedName("cart_id") var cartId: Int? = null,
    @SerializedName("shop") var shop: ProductDetailsStore? = ProductDetailsStore(),
    @SerializedName("location") var location: Locations? = null,
    @SerializedName("delivery_area") var deliveryArea: DeliveryArea? = null,
    @SerializedName("delivery_price") var deliveryPrice: Float? = null,
    @SerializedName("coupon_code") var couponCode: String? = null,
    @SerializedName("coupon_price") var couponPrice: Float? = null,
    @SerializedName("total_price") var totalPrice: Float? = null,
    @SerializedName("range_discount") var rangeDiscount: Float? = null,
    @SerializedName("range_discount_amount") var rangeDiscountAmount: Float? = null,
    @SerializedName("range_price_from") var rangePriceFrom: Float? = null,
    @SerializedName("range_price_to") var rangePriceTo: Float? = null,
    @SerializedName("amount") var amount: Float? = null,
    @SerializedName("cart_details") var cartDetails: ArrayList<CartDetails> = arrayListOf(),
)