package com.techcubics.albarkahyper.ui.views.chat.fragments.livechat

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.LiveMessageData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IPageRowset
import com.techcubics.albarkahyper.databinding.FragmentLiveChatBinding
import com.techcubics.albarkahyper.ui.adapters.chat.ChatHistoryAdapter
import com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat.OrderChatHistoryFragmentArgs
import com.techcubics.albarkahyper.ui.views.chat.viewmodels.ChatViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.collections.ArrayList


class LiveChatFragment :Fragment() , IPageRowset<LiveMessageData> {

    private  lateinit var binding: FragmentLiveChatBinding
    private lateinit var chatHistoryAdapter: ChatHistoryAdapter<LiveMessageData>
    private val chatViewModel by viewModel<ChatViewModel>()
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private var storeID:Int?=null
    private  var receiverName:String?=null
    private  var iconUrl:String?=null
    private val args: LiveChatFragmentArgs? by navArgs()


    private  val TAG = "LiveChatFragment"


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding= FragmentLiveChatBinding.inflate(inflater,container,false)

        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {


        arguments?.let { arg->

            storeID = arg.getInt(Constants.INTENT_ID, -1)
            receiverName = arg.getString(Constants.INTENT_NAME)
            iconUrl = arg.getString(Constants.INTENT_URL)

            chatViewModel.getLiveChatHistory(storeID!!)

            binding.toolbar.tvTitle.text=receiverName
            Helper.loadImage(requireContext(),iconUrl!!,binding.toolbar.photo)
            /*binding.toolbar.tvTitle.setCompoundDrawablesWithIntrinsicBounds(0,0,
                com.techcubics.style.R.drawable.ic_support_user,0)*/

        }
        //
        showHidePlaceHolder(show=false,type= null,message=null)
        binding.rvHistory.visibility=View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
    }
    override  fun observers(){

        chatViewModel.liveMessagesResponse.observe(viewLifecycleOwner, androidx.lifecycle.Observer {

//            binding.shimmerID.stopShimmer()
//            binding.shimmerID.visibility=View.GONE
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            try {
                if (it.status==true) {

                    if(it.data!=null){
                        it?.data!!.let { f->
                           // it?.data!!.messages.add(getDefaultMessage(f))
                            val data = if(it?.data!!.size==0) {
                                arrayListOf<LiveMessageData>(getDefaultMessage())
                            }else{
                                it?.data!!
                            }
                            showData(data)
                            binding.rvHistory.visibility = View.VISIBLE
                            try {
                                args?.refresh?.perform()
                            }catch (ex:Exception){}

                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Error,
                            message = it.message
                        )

                    }

                } else {

                    if (it.data!=null){

                        if(it?.data!!.size==0) {
                            var s = ArrayList<LiveMessageData>()
                            s.add(getDefaultMessage())
                            showData(s)
                            binding.rvHistory.visibility = View.VISIBLE
                        }
                    }else {
                        //error
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Error,
                            message = it.message
                        )
                    }
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })

        chatViewModel.sendLiveResponse.observe(viewLifecycleOwner, androidx.lifecycle.Observer {

            binding.includeChatSendBox.btnSend.isEnabled=true
            try {
                if(it.status!!){

                    chatViewModel.getLiveChatHistory(storeID = storeID!!)
                    binding.includeChatSendBox.txtMessage.setText("")

                }else{

                    //error
                    Helper.showDialog(requireContext(),it.message!!)
                }

            }catch(ex:Exception) {
                //error
                Helper.showDialog(requireContext(),ex.message.toString())
            }
        })


    }
    override fun events(){

        binding.toolbar.tvTitle.setOnClickListener {

            findNavController().popBackStack()
        }

        binding.toolbar.btnBack.setOnClickListener {

            findNavController().popBackStack()
        }

        binding.includeChatSendBox.btnSend.setOnClickListener{

            if(binding.includeChatSendBox.txtMessage.text.isNotEmpty() && binding.includeChatSendBox.txtMessage.text.isNotBlank()){

                binding.includeChatSendBox.btnSend.isEnabled=false
                chatViewModel.sendToLive(binding.includeChatSendBox.txtMessage.text.trim().toString(), store = storeID!!)

            }

        }
    }
    override fun showData( items:List<LiveMessageData>){



        chatHistoryAdapter = ChatHistoryAdapter<LiveMessageData>()
        chatHistoryAdapter.setItemsList(items)
        binding.rvHistory.adapter = chatHistoryAdapter
        binding.rvHistory.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL,true)

        binding.scroller.postDelayed(Runnable {
            binding.scroller.fullScroll(View.FOCUS_DOWN)
        }, 100)


    }
    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                } else -> throw IllegalStateException("error")
            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }

    fun getDefaultMessage(): LiveMessageData {

//2022-11-24T14:43:33.000000Z
        val timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
        var t = LocalDateTime.now().format(timeFormatter)

        return LiveMessageData(senderId = SharedPreferencesManager.getUserID(), message = getString(
            com.techcubics.style.R.string.message_live_chat_defult), createdAt = t)
    }





}


