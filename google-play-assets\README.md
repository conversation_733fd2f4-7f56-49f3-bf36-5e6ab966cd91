# AlBarkaHyper - Google Play Store Assets

هذا المجلد يحتوي على جميع الملفات المطلوبة لرفع تطبيق AlBarkaHyper على متجر Google Play.

## الملفات المطلوبة:

### 1. ملفات التطبيق
- **APK File**: `app-release.apk` - ملف التطبيق الرئيسي
- **AAB File**: `app-release.aab` - ملف Android App Bundle (مفضل لـ Google Play)

### 2. الصور والأيقونات
- **App Icon**: أيقونة التطبيق بدقة 512x512 بكسل (PNG)
- **Feature Graphic**: صورة مميزة بدقة 1024x500 بكسل (PNG/JPG)
- **Screenshots**: لقطات شاشة للهواتف والأجهزة اللوحية

### 3. معلومات التطبيق
- **App Description**: وصف التطبيق باللغة العربية والإنجليزية
- **Privacy Policy**: سياسة الخصوصية
- **Terms of Service**: شروط الاستخدام

### 4. معلومات تقنية
- **Target SDK**: API Level 35 (Android 15)
- **Min SDK**: API Level 24 (Android 7.0)
- **Version Code**: 30
- **Version Name**: 1.1.8

## متطلبات Google Play Store:

### الصور المطلوبة:
1. **أيقونة التطبيق**: 512x512 بكسل (PNG، 32-bit)
2. **صورة مميزة**: 1024x500 بكسل (PNG أو JPG)
3. **لقطات شاشة للهاتف**: 
   - الحد الأدنى: 2 صورة
   - الحد الأقصى: 8 صور
   - الأبعاد: 320-3840 بكسل
   - نسبة العرض إلى الارتفاع: 2:1 إلى 1:2

4. **لقطات شاشة للجهاز اللوحي** (اختيارية):
   - الحد الأدنى: 1 صورة
   - الحد الأقصى: 8 صور
   - الأبعاد: 320-3840 بكسل

### النصوص المطلوبة:
1. **العنوان**: حتى 50 حرف
2. **الوصف المختصر**: حتى 80 حرف
3. **الوصف الكامل**: حتى 4000 حرف

### الفئات والتصنيفات:
- **الفئة**: Shopping / تسوق
- **التصنيف**: للجميع
- **الكلمات المفتاحية**: تسوق، متجر، البركة هايبر، منتجات

## ملاحظات مهمة:
1. تأكد من أن جميع الصور بجودة عالية وواضحة
2. يجب أن تكون النصوص خالية من الأخطاء الإملائية
3. تأكد من أن سياسة الخصوصية متاحة ومحدثة
4. اختبر التطبيق على أجهزة مختلفة قبل الرفع
5. تأكد من أن التطبيق يعمل على أحدث إصدارات Android

## خطوات الرفع:
1. قم بتسجيل الدخول إلى Google Play Console
2. أنشئ تطبيق جديد أو حدث التطبيق الموجود
3. ارفع ملف AAB أو APK
4. أضف جميع الصور والنصوص المطلوبة
5. راجع جميع المعلومات
6. أرسل للمراجعة

## روابط مفيدة:
- [Google Play Console](https://play.google.com/console)
- [متطلبات Google Play](https://support.google.com/googleplay/android-developer/answer/9859348)
- [دليل النشر](https://developer.android.com/distribute/googleplay)
