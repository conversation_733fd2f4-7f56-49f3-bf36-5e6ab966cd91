package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemRateBinding
import com.techcubics.data.model.pojo.Rates

class RatesViewHolder(val context: Context, private var binding: ItemRateBinding) :
    RecyclerView.ViewHolder(binding.root) {
    fun setData(rate: Rates) {
        with(binding) {
            Helper.loadImage(context, rate.avatar!!, logo)
            tvName.text = rate.user
            rateBar.rating = rate.degree?.toFloat() ?: 0f
            tvDate.text = rate.date
            tvDescription.text = rate.comment

        }
    }
}