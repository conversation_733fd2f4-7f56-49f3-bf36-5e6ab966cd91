package com.techcubics.albarkahyper.ui.adapters.profile

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemLanguageBinding
import com.techcubics.albarkahyper.ui.adapters.holders.profile.SingleTextLineHolderItem


class SingleTextLineAdapter(
    private val progressbar: ProgressBar,
    private val context: Context,
    private val itemName: List<String>,
    private val popupMenuItemLiveData: MutableLiveData<String?>,
    private val dialog: Dialog
):
    RecyclerView.Adapter<SingleTextLineHolderItem>(){


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SingleTextLineHolderItem {

        val itemBinding = ItemLanguageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SingleTextLineHolderItem(itemBinding)
    }


    @SuppressLint("ResourceAsColor")
    override fun onBindViewHolder(holder: SingleTextLineHolderItem, position: Int) {
        holder.itemName.text = itemName.get(position)
        holder.itemView.setOnClickListener {
            popupMenuItemLiveData.value = itemName.get(position)
            Log.i("here","choosen lang is"+itemName.get(position))
            dialog.dismiss()
        }

    }

    override fun getItemCount() = itemName.size

    companion object{
        var mutableLiveData : MutableLiveData<Boolean> = MutableLiveData(false)
    }



}