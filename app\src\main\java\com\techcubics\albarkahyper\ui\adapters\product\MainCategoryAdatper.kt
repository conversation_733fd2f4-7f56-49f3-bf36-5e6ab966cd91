package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.Category
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemMainCategoryBinding
import com.techcubics.albarkahyper.databinding.ItemSliderBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.BannerHolderItem
import com.techcubics.albarkahyper.ui.adapters.holders.product.MainCategoryHolderItem


class MainCategoryAdatper (val onClick: IOnAdapterItemClickHandler): RecyclerView.Adapter<MainCategoryHolderItem>() {

    lateinit var items:List<Category>

    fun setItemsList(_items:List<Category>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MainCategoryHolderItem {
        val itemBinding = ItemMainCategoryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MainCategoryHolderItem(itemBinding,parent.context,onClick)
    }


    override fun onBindViewHolder(holder: MainCategoryHolderItem, position: Int) {
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return items.size
    }




}