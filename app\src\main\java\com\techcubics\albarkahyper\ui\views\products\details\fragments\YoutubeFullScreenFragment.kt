package com.techcubics.albarkahyper.ui.views.products.details.fragments

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.YouTubeHelper
import com.techcubics.albarkahyper.databinding.FragmentYoutubeFullScreenBinding
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants


class YoutubeFullScreenFragment : Fragment(), ViewTreeObserver.OnWindowFocusChangeListener {
    private var _binding: FragmentYoutubeFullScreenBinding? = null
    private val binding get() = _binding!!
    private var seconds = 0f
    private var vidId = ""
    private lateinit var youtubeHelper: YouTubeHelper

    private val viewModel: MainViewModel by activityViewModels()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        seconds = arguments?.getFloat(Constants.YOUTUBE_SECONDS, 0f) ?: 0f
        vidId = arguments?.getString(Constants.YOUTUBE_VID_ID) ?: ""
        _binding = FragmentYoutubeFullScreenBinding.inflate(inflater, container, false)
        Helper.hideSystemUI(requireActivity().window)
        return binding.root
    }
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUI(requireActivity().window)
        else Helper.showSystemUI(requireActivity().window)
    }
    @SuppressLint("SourceLockedOrientationActivity")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        youtubeHelper = YouTubeHelper(requireActivity(), vidId, binding.youtubePlayerView, seconds)
        youtubeHelper.initialize(true)
        binding.youtubePlayerView.enterFullScreen()
        youtubeHelper.setFullScreenListener(exitFullScreen = { seconds ->
            findNavController().popBackStack()
            viewModel.setSeconds(seconds)
            requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        })
        requireActivity().onBackPressedDispatcher.addCallback(
            requireActivity(),
            onBackPressedCallback
        )

    }

    private val onBackPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            binding.youtubePlayerView.exitFullScreen()
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        binding.root.viewTreeObserver?.removeOnWindowFocusChangeListener(this)
        onBackPressedCallback.remove()
    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}