<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/language_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="18dp"
            android:paddingEnd="18dp"
            android:paddingTop="16dp"
            android:paddingBottom="15dp"
            android:text="AR"
            android:textAppearance="@style/label_item_language"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="16dp"
            android:background="@color/color_gray_4" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
</layout>