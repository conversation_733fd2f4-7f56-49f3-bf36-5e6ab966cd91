package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.ItemProductBinding
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductHolderItem
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler


class ProductsPagingAdapter<T>(val margin:Int=0, val onFavClickListener: IFavClickListener?=null, val onClickHandler: IOnAdapterItemClickHandler): PagingAdapter() {

    lateinit var items:List<T>
    lateinit var itemBinding: ItemProductBinding

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductHolderItem<T> {
        itemBinding = ItemProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)

        return ProductHolderItem(itemBinding,parent.context,margin,onFavClickListener!!, onClickHandler )
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as ProductHolderItem<T>
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_product
    }

    override fun getPagingItemCount(): Int {
        return items.size
    }



}