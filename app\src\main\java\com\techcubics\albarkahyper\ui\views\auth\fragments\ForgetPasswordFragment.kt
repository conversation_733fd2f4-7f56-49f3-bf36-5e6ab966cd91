package com.techcubics.albarkahyper.ui.views.auth.fragments

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.firebase.ui.auth.util.data.PhoneNumberUtils.getCountryCode
import com.google.firebase.FirebaseException
import com.google.firebase.FirebaseTooManyRequestsException
import com.google.firebase.auth.*
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentForgetPasswordBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.requests.auth.ForgetPasswordConfirmRequest
import com.techcubics.data.model.requests.auth.ForgetPasswordRequest
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.utils.AuthUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.concurrent.TimeUnit


class ForgetPasswordFragment : Fragment() {

    private lateinit var forgetPasswordBinding: FragmentForgetPasswordBinding
    private lateinit var progressBtn: ProgressButton
    private val forgetPassViewModel by viewModel<AuthViewModel>()

    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private var appContext: Context? = null


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        forgetPasswordBinding = FragmentForgetPasswordBinding.inflate(inflater,container,false)

        progressBtn = ProgressButton(requireContext())
        progressBtn.init(forgetPasswordBinding.signBtnProgress)
        bottomSheetAlertDialog = BottomSheetAlertDialog()

        initViews()
        observeView()
        bottomSheetAlertDialog.init(requireContext())
        return forgetPasswordBinding.root
    }

    private fun observeView() {

        //phone
        forgetPassViewModel.forgetPasswordByPhoneMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!){
                    progressBtn.btnActivated()
                    Log.i("clothes","data reached")
                    sendOtp()
                    forgetPassViewModel.forgetPasswordConfirmCall(
                        ForgetPasswordConfirmRequest(forgetPasswordBinding.emailOrPhoneEdt.text.toString(),it.data!!.code,
                            sharedPreferencesManager.getCountryCode()))
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    appContext?.resources?.getString(com.techcubics.style.R.string.send)
                        ?.let { it1 -> progressBtn.btnFinishedFailed(it1,null) }
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else{
                    appContext?.resources?.getString(com.techcubics.style.R.string.send)
                        ?.let { it1 -> progressBtn.btnFinishedFailed(it1,null) }
                    bottomSheetAlertDialog.showDialog(it.message!!)

                }
                forgetPassViewModel.forgetPasswordByPhoneMutableLiveData.value = null
            }

        }

        //email
        forgetPassViewModel.forgetPasswordByEmailMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    appContext?.resources?.getString(com.techcubics.style.R.string.send)
                        ?.let { it1 -> progressBtn.btnFinishedSuccessfully(it1,null) }
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    appContext?.resources?.getString(com.techcubics.style.R.string.send)
                        ?.let { it1 -> progressBtn.btnFinishedFailed(it1,null) }
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else{
                    progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.send),null)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                forgetPassViewModel.forgetPasswordByEmailMutableLiveData.value = null
            }

        }

        //get token for reset password
        forgetPassViewModel.forgetPasswordConfirmMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if(it.status!!){
                    sharedPreferencesManager.saveToken(it.data!!.token)

                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
                forgetPassViewModel.forgetPasswordConfirmMutableLiveData.value = null
            }

        }
    }


    private fun initViews() {

        progressBtn.binding.textView.text = getString(com.techcubics.style.R.string.send)
        forgetPasswordBinding.countryPicker.setDefaultCountryUsingNameCode(sharedPreferencesManager.getCountryCode())
        forgetPasswordBinding.countryPicker.resetToDefaultCountry()
        forgetPasswordBinding.signBtnProgress.constraintsLayout.setOnClickListener {

            sharedPreferencesManager.convertCountryCode(forgetPasswordBinding.countryPicker.selectedCountryName)
            sharedPreferencesManager.saveMobileCode(forgetPasswordBinding.countryPicker.selectedCountryCodeWithPlus)
            //if phone number
            if (AuthUtils.isNumeric(forgetPasswordBinding.emailOrPhoneEdt.text.toString())) {
                if (!forgetPasswordBinding.emailOrPhoneEdt.text.isNullOrEmpty() && AuthUtils.validatePhone(
                        forgetPasswordBinding.emailOrPhoneEdt.text.toString(),
                        forgetPasswordBinding.countryPicker.selectedCountryCode
                    )
                ) {
                    Log.i("clothes", "clicked")
                    progressBtn.btnActivated()
                    forgetPassViewModel.forgetPasswordByPhone(
                        ForgetPasswordRequest(
                            forgetPasswordBinding.emailOrPhoneEdt.text.toString(),
                            forgetPasswordBinding.countryPicker.selectedCountryNameCode
                        )
                    )
                } else
                    bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.phone_not_correct))
            }
            //if email
            else if (AuthUtils.validateEmail(forgetPasswordBinding.emailOrPhoneEdt.text.toString()) && !forgetPasswordBinding.emailOrPhoneEdt.text.isNullOrEmpty()) {
                progressBtn.btnActivated()
                forgetPassViewModel.forgetPasswordByEmail(
                    ForgetPasswordRequest(
                        forgetPasswordBinding.emailOrPhoneEdt.text.toString(),
                        forgetPasswordBinding.countryPicker.selectedCountryNameCode
                    )
                )
            } else
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.check_your_email_and_try_again))

        }
        activity?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                findNavController().apply {
                    navigate(R.id.action_forgetPasswordFragment_to_loginFragment)
                }
            }
        })
        forgetPasswordBinding.navtoSigninBtn.setOnClickListener {
            findNavController().apply {
                navigate(R.id.action_forgetPasswordFragment_to_loginFragment)
//                backQueue.clear()
            }
        }
    }


    private fun sendOtp() {
        val callbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {

            override fun onVerificationCompleted(credential: PhoneAuthCredential) {
            }

            override fun onVerificationFailed(e: FirebaseException) {
                appContext?.resources?.getString(com.techcubics.style.R.string.send)
                    ?.let { progressBtn.btnFinishedFailed(it,null) }
                bottomSheetAlertDialog.showDialog(e.message.toString())
                Log.i("clothes",e.message.toString())

                if (e is FirebaseAuthInvalidCredentialsException) {
                    // Invalid request
                    Log.i("clothes",e.message.toString())
                } else if (e is FirebaseTooManyRequestsException) {
                    // The SMS quota for the project has been exceeded
                    Log.i("clothes",e.message.toString())

                }
            }

            override fun onCodeSent(verificationId: String, token: PhoneAuthProvider.ForceResendingToken) {
                appContext?.resources?.getString(com.techcubics.style.R.string.send)
                    ?.let { progressBtn.btnFinishedSuccessfully(it,null) }
                CoroutineScope(Dispatchers.Main).launch {
                    progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.send),null)
                    delay(1000)
                    Log.i("clothes", "from fpf : $verificationId")
                    val args = Bundle()
                    args.putString(Constants.VERFICATION_ID, verificationId)
                    args.putString(Constants.MOBILE,forgetPasswordBinding.emailOrPhoneEdt.text.toString())
                    findNavController().navigate(R.id.action_forgetPasswordFragment_to_verficationCodeFragment,args)
                }

            }
        }
        Log.i("clothes","send otp")


        PhoneAuthProvider.getInstance().verifyPhoneNumber(
            sharedPreferencesManager.getMobileCode()+ forgetPasswordBinding.emailOrPhoneEdt.text.toString(),
            60,
            TimeUnit.SECONDS,
            requireActivity(),
            callbacks
        )
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        Log.i("product", "onAttach")
        if (appContext == null) {
            appContext = context.applicationContext
        }
    }

}