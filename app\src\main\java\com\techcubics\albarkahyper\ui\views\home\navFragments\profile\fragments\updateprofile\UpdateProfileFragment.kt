package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.updateprofile

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.BitmapFactory
import android.location.Geocoder
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.widget.Autocomplete
import com.google.android.libraries.places.widget.AutocompleteActivity
import com.google.android.libraries.places.widget.model.AutocompleteActivityMode
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentUpdateProfileBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.pojo.User
import com.techcubics.data.model.requests.auth.UpdatePasswordRequest
import com.techcubics.data.model.requests.profile.UpdateProfileRequest
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.data.model.pojo.Governerate
import com.techcubics.data.model.pojo.Regoin
import com.techcubics.shared.constants.Constants
import com.techcubics.style.R
import kotlinx.coroutines.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.*
import java.net.URL
import java.text.DecimalFormat
import java.util.*


class UpdateProfileFragment : Fragment() , OnMapReadyCallback , OnItemsChangedListener {

    private lateinit var updateProfileBinding: FragmentUpdateProfileBinding
    private lateinit var progressBtn: ProgressButton
    private var updatePasswordResetMutableLiveData = MutableLiveData<UpdatePasswordRequest?>()
    private var uriFilePath: Uri? = null
    private var bitmap: Bitmap? = null
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private var mapFragment: SupportMapFragment? = null
    private var googleMap: GoogleMap? = null
    private lateinit var choosenLocation: Latlng
    private var location: Latlng? = null
    private var checkGovCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkRegionCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var checkCountryCLicked: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>()
    private var selectedCountryId = 0
    private var selectedGovernorateId = 0
    private var selectedRegionId = 0
    private var governorateList: MutableLiveData<ArrayList<Governerate>> =
        MutableLiveData<ArrayList<Governerate>>()
    private var regionList: MutableLiveData<ArrayList<Regoin>> =
        MutableLiveData<ArrayList<Regoin>>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        updateProfileBinding = FragmentUpdateProfileBinding.inflate(inflater, container, false)
        mapFragment =
            this.childFragmentManager.findFragmentById(com.techcubics.albarkahyper.R.id.map) as SupportMapFragment?

        mapFragment?.getMapAsync(this)
        val ai: ApplicationInfo = requireContext().packageManager
            .getApplicationInfo(requireContext().packageName, PackageManager.GET_META_DATA)
        val value = ai.metaData["com.google.android.geo.API_KEY"]
        val placekey = value.toString()
        Log.i("here",placekey)
        Places.initialize(requireContext(), placekey)
        progressBtn = ProgressButton(requireContext())
        progressBtn.init(updateProfileBinding.signBtnProgress)
        initViews()
        observeViews()
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())

        return updateProfileBinding.root
    }

    private fun observeViews() {
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {

            if (it != null) {
                if (it.message!!.contains(getString(R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        updateProfileBinding.updateprofileLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(R.string.server_error)
                    )
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }

        profileViewModel.countriesPopupMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                updateProfileBinding.country.setText(it.name)
                profileViewModel.getGovernerateByCountryId(it.country_id)
                selectedCountryId = it.country_id
                profileViewModel.countriesPopupMenuItemLiveData.value = null

            }
        }
        profileViewModel.listOfgovernerateMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    governorateList.postValue(it.data!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
                profileViewModel.listOfgovernerateMutableLiveData.value = null
            }
        }
        profileViewModel.governeratesMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                updateProfileBinding.governerate.setText(it.name)
                profileViewModel.getRegionBygovernorateId(
                    it.governorate_id,
                    sharedPreferencesManager.getCountryID().toInt()
                )
                selectedGovernorateId = it.governorate_id
                profileViewModel.governeratesMutableLiveData.value = null
            }
        }
        profileViewModel.listOfregionsMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                if (it.status!!) {
                    regionList.postValue(it.data!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else
                    bottomSheetAlertDialog.showDialog(it.message!!)
            }

        }
        profileViewModel.regionPopupMenuItemLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                updateProfileBinding.region.setText(it.name)
                selectedRegionId = it.regionID
                profileViewModel.regionPopupMenuItemLiveData.value = null
            }
        }
        governorateList.observe(viewLifecycleOwner) {
            if (it != null) {
                checkGovCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Log.i("clothes","clickedgov"+it)
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                updateProfileBinding.actionLoadingAnimation.root
                            )
                            popupDialog.showGovernerateDialog(
                                requireContext(),
                                com.techcubics.albarkahyper.R.layout.dialog_language,
                                governorateList.value!!,
                                profileViewModel.governeratesMutableLiveData
                            )
                        }
                        checkGovCLicked.value = null
                    }
                }

            }
        }
        regionList.observe(viewLifecycleOwner) {
            if (it != null) {
                checkRegionCLicked.observe(viewLifecycleOwner) {
                    if (it != null) {
                        if (it) {
                            Helper.loadingAnimationVisibility(
                                View.GONE,
                                updateProfileBinding.actionLoadingAnimation.root
                            )
                            popupDialog.showRegionDialog(
                                requireContext(), com.techcubics.albarkahyper.R.layout.dialog_language,
                                regionList.value!!, profileViewModel.regionPopupMenuItemLiveData
                            )
                        }
                        checkRegionCLicked.value = null
                    }
                }

            }
        }
        profileViewModel.countriesMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                val countryList = it.data
                checkCountryCLicked.observe(viewLifecycleOwner){
                    if(it != null){
                        if(it){
                            Helper.loadingAnimationVisibility(View.GONE, updateProfileBinding.actionLoadingAnimation.root)
                            popupDialog.showCounrtyDialog(
                                requireContext(),
                                com.techcubics.albarkahyper.R.layout.dialog_country,
                                countryList,
                                profileViewModel.countriesPopupMenuItemLiveData as MutableLiveData<Any?>,
                                Constants.JOIN
                            )
                        }
                        checkCountryCLicked.value = null
                    }
                }
            }
        }
        profileViewModel.updateProfileMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    CoroutineScope(Dispatchers.Main).launch {
                        if(uriFilePath != null){
                            sharedPreferencesManager.saveUserPhoto(it.data?.avatar.toString())
                        }
                        it.data.let { user ->
                            sharedPreferencesManager.saveObject(Constants.CHOOSEN_LATLNG,Latlng(user?.lat.toString(),user?.lng.toString()))
                            user?.facilityName?.let { it1 -> sharedPreferencesManager.saveShopName(it1) }
                            sharedPreferencesManager.saveObject(Constants.USER,user)
                            user?.name?.let {
                                sharedPreferencesManager.saveName(user.name)
                            }
                            user?.email?.let {
                                sharedPreferencesManager.saveEmail(user.email)
                            }

                        }

                        progressBtn.btnFinishedSuccessfully(getString(R.string.change), null)
                        bottomSheetAlertDialog.showDialog(it.message!!)
                        delay(1500)
                        findNavController().popBackStack()
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    progressBtn.btnFinishedFailed(getString(R.string.change), null)
                    Helper.ShowErrorDialog(requireContext(), getString(R.string.server_error))
                } else {
                    progressBtn.btnFinishedFailed(getString(R.string.change), null)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.updateProfileMutableLiveData.value = null
            }
        }

//        profileViewModel.branchTypeMutableLiveData.observe(viewLifecycleOwner){
//            if(it != null){
//                branchListController.initDropdownList(it.data?.types)
//                profileViewModel.branchTypeMutableLiveData.value = null
//            }
//        }

    }

    private fun initViews() {
        updateProfileBinding.governerate.isEnabled = true
        updateProfileBinding.governerate.isClickable = true
        updateProfileBinding.governerate.isLongClickable = true
        updateProfileBinding.region.isEnabled = true
        updateProfileBinding.region.isClickable = true
        updateProfileBinding.region.isLongClickable = true
        Helper.loadingAnimationVisibility(View.VISIBLE,updateProfileBinding.actionLoadingAnimation.root)
        checkAutherization()

        updateProfileBinding.region.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)
        updateProfileBinding.governerate.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)
        updateProfileBinding.country.setTextAppearance(com.techcubics.style.R.style.label_edittext_joinus)

        updateProfileBinding.signBtnProgress.textView.text = getString(R.string.change)
        updateProfileBinding.toolbar.tvTitle.text = getString(R.string.main_page_adjustement)
        updateProfileBinding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }
        profileViewModel.storeTypesCall()


        //address clicked to show search
        updateProfileBinding.address.keyListener = null

        updateProfileBinding.address.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                updateProfileBinding.address.callOnClick()
            }
        }

        updateProfileBinding.address.setOnClickListener {
            val fieldList =
                listOf(Place.Field.ADDRESS, Place.Field.LAT_LNG, Place.Field.NAME)
            val intent = Autocomplete.IntentBuilder(AutocompleteActivityMode.OVERLAY, fieldList)
                .build(requireActivity())
            startActivityForResult(intent, 100)
        }

        updateProfileBinding.country.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, updateProfileBinding.actionLoadingAnimation.root)
            checkCountryCLicked.postValue(true)
            profileViewModel.getCountries()

        }
        updateProfileBinding.governerate.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, updateProfileBinding.actionLoadingAnimation.root)
            checkGovCLicked.postValue(true)
            profileViewModel.getGovernerateByCountryId(selectedCountryId)

        }
        updateProfileBinding.region.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, updateProfileBinding.actionLoadingAnimation.root)
            checkRegionCLicked.postValue(true)
            profileViewModel.getRegionBygovernorateId(
                selectedGovernorateId,
                selectedCountryId
            )

        }
        //show user data
        val user: User = sharedPreferencesManager.getUser()
        val fullname = user.name.split("\\s".toRegex()).toTypedArray()
        Log.i("albarkahyper",fullname.toString())
        if (fullname.size >= 2) {
            updateProfileBinding.firstName.setText(fullname[0])
            updateProfileBinding.lastName.setText(fullname[1])
        } else
            updateProfileBinding.firstName.setText(user.name)

        updateProfileBinding.country.setText(user.country?.name)
        updateProfileBinding.governerate.setText(user.governorate?.name)
        updateProfileBinding.region.setText(user.region?.name)

        updateProfileBinding.phoneNumber.setText(user.phone)
        updateProfileBinding.facilityName.setText(user.facilityName)
        updateProfileBinding.facilityTypeEdt.setText(user.branchType)
        updateProfileBinding.address.setText(user.address)
        selectedCountryId = user.country?.countryID!!
        selectedGovernorateId = user.governorate?.governorate_id!!
        selectedRegionId = user.region?.regionID!!

        if (user.avatar != null) {
            Helper.loadImage(requireContext(), sharedPreferencesManager.getUserPhoto(), updateProfileBinding.profilePic)
        } else {
            updateProfileBinding.profilePic.setImageResource(R.drawable.circle_gray)
        }



        updateProfileBinding.ivCamera.setOnClickListener {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    android.Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                openGallery()
            } else {
                ActivityCompat.requestPermissions(
                    requireActivity(), arrayOf(android.Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    1
                )
            }

        }

        updateProfileBinding.signBtnProgress.constraintsLayout.setOnClickListener {
            CoroutineScope(Dispatchers.IO).launch {
                val file: File?
                if (bitmap == null) {
                    file = null
                } else {
                    file = bitmapToFile(bitmap, "userPhoto")
                }
                Log.i("here", "file value" + file)
                withContext(Dispatchers.Main) {
                    if (checkUiValidity()) {
                        progressBtn.btnActivated()

                        withContext(Dispatchers.IO) {
                            profileViewModel.updateProfileCall(
                                UpdateProfileRequest(
                                    updateProfileBinding.firstName.text.toString() + " " + updateProfileBinding.lastName.text.toString(),
                                    updateProfileBinding.phoneNumber.text.toString(),
                                    file,
                                    updateProfileBinding.address.text.toString(),
                                    sharedPreferencesManager.getCountryCode(),
                                    updateProfileBinding.facilityName.text.toString(),
                                    updateProfileBinding.facilityTypeEdt.text.toString(),
                                    choosenLocation.lat ?: sharedPreferencesManager.getCurrentLatlng().lat,
                                    choosenLocation.lng ?: sharedPreferencesManager.getCurrentLatlng().lng,
                                    selectedCountryId,
                                    selectedGovernorateId,
                                    selectedRegionId
                                )
                            )

                        }


                    }
                }
            }

        }

        updateProfileBinding.changePass.setOnClickListener {
            popupDialog.showDialog(
                updatePasswordResetMutableLiveData,
                viewLifecycleOwner,
                authViewModel
            )
        }


    }

    private fun getBitmapFromURL(avatar: String?): Bitmap? {
        return BitmapFactory.decodeStream(URL(avatar).openConnection().getInputStream())

    }

    private fun openGallery() {
        val intent = Intent()
        intent.action = Intent.ACTION_GET_CONTENT
        intent.type = "image/*"
        startActivityForResult(intent, 10)
    }

    private fun checkUiValidity(): Boolean {

        var check = true
        if (updateProfileBinding.phoneNumber.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.phone_not_correct))
            check = false
        } else if (updateProfileBinding.firstName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_check_first_name))
            check = false
        }else if (updateProfileBinding.lastName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_check_last_name))
            check = false
        }else if (updateProfileBinding.facilityName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_check_gallery_name))
            check = false
        }else if (updateProfileBinding.facilityTypeEdt.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_check_gallery_type))
            check = false
        }else if (updateProfileBinding.address.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_add_address))
            check = false
        }
        return check
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }


    private fun bitmapToFile(
        bitmap: Bitmap?,
        fileNameToSave: String
    ): File? { // File name like "image.png"
        //create a file to write bitmap data
        val file: File?

        file = File(requireContext().cacheDir, fileNameToSave)
        file.createNewFile()

        val bos = ByteArrayOutputStream()
        bitmap?.compress(CompressFormat.JPEG, 50 /*ignored for PNG*/, bos)

        val bitmapdata = bos.toByteArray()

        var fos: FileOutputStream? = null
        try {
            fos = FileOutputStream(file)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
        try {
            fos!!.write(bitmapdata)
            fos.flush()
            fos.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return file
    }

    override fun onMapReady(p0: GoogleMap) {
        Log.i("here", "onMapReady")
        googleMap = p0
        googleMap?.uiSettings?.isZoomControlsEnabled = true

        choosenLocation = sharedPreferencesManager.getChoosenLatlng()
        val currentLatLng =
            LatLng(roundToSevenDigits(choosenLocation.lat.toDouble()).toDouble(),roundToSevenDigits(choosenLocation.lng.toDouble()).toDouble())
        //updateProfileBinding.address.setText(getAddress(currentLatLng.latitude, currentLatLng.longitude))
        placeMarker(currentLatLng)


    }

    private fun addLocationMarker(currentLatLng: LatLng) {
        if (googleMap != null) {
            val markerOption = MarkerOptions().position(currentLatLng)
                .title(getString(com.techcubics.style.R.string.choosen_location))
                .snippet(getAddress(currentLatLng.latitude, currentLatLng.longitude))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLng(currentLatLng))
            googleMap?.animateCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0f))
            val currentMarker = googleMap?.addMarker(markerOption)
            currentMarker?.showInfoWindow()
        }
    }

    private fun placeMarker(currentLatLng: LatLng) {
        Helper.loadingAnimationVisibility(View.GONE,updateProfileBinding.actionLoadingAnimation.root)
        if (googleMap != null) {
            if (ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            googleMap?.isMyLocationEnabled = true
            googleMap?.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 13.0F))

        }

    }

    @SuppressLint("UseRequireInsteadOfGet")
    private fun getAddress(lat: Double, lng: Double): String {
        var returnAddress = ""
        runBlocking {
            if (<EMAIL> != null) {
                val geocoder =
                    Geocoder(<EMAIL>!!, Locale.getDefault())
                val address = geocoder.getFromLocation(lat, lng, 1)
                val addressJob = async { address?.get(0)?.getAddressLine(0).toString() }
                returnAddress = addressJob.await()

            }

        }
        return returnAddress
    }
    private fun roundToSevenDigits(value: Double): String {
        val df = DecimalFormat("##.#######").format(value)
        return convertArabic(df)
    }

    private fun convertArabic(arabicStr: String): String {
        val chArr = arabicStr.toCharArray()
        val sb = StringBuilder()
        for (ch in chArr) {
            if (Character.isDigit(ch)) {
                sb.append(Character.getNumericValue(ch))
            }else if (ch == '٫'){
                sb.append(".")
            }

            else {
                sb.append(ch)
            }
        }
        return sb.toString()
    }


    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 100 && resultCode == Activity.RESULT_OK) {

            //init place
            val place = data?.let { Autocomplete.getPlaceFromIntent(it) }

            //set Address on edittext
            place?.let {
                updateProfileBinding.address.setText(place.name)
                place.latLng?.let {
                        latlng -> addLocationMarker(latlng)
                    choosenLocation = Latlng(roundToSevenDigits(latlng.latitude),roundToSevenDigits(latlng.longitude))
                    Log.i("albarkahyper",latlng.toString()+choosenLocation)
                }
            }


        } else if (resultCode == AutocompleteActivity.RESULT_ERROR) {

            //init status
            val status = data?.let { Autocomplete.getStatusFromIntent(it) }
            Log.i("here", "status" + status?.statusMessage)
        }
        else if (requestCode == 10 && resultCode == Activity.RESULT_OK) {
            uriFilePath = data?.data
            if (uriFilePath != null) {
                Log.i("here", "uri path$uriFilePath")
//                updateProfileBinding.profilePic.setImageURI(uriFilePath)
                Helper.loadImage(
                    requireContext(),
                    uriFilePath.toString(),
                    updateProfileBinding.profilePic
                )
                try {
                    bitmap = MediaStore.Images.Media.getBitmap(
                        requireContext().contentResolver,
                        uriFilePath
                    )
                } catch (e: IOException) {
                    e.printStackTrace()
                }

            }

        }

    }

    override fun onPause() {
        super.onPause()
        googleMap?.clear()
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}