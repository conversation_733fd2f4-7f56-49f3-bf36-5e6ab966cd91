package com.techcubics.albarkahyper.ui.views.products.searchResult

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Regoin
import com.techcubics.data.model.requests.SearchAreaRequest
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.albarkahyper.common.DividerItemDecorator
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentProductAreasFilterBinding
import com.techcubics.albarkahyper.ui.adapters.product.AreaAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.style.R
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class ProductRegionFilterFragment : Fragment(), AreaSelectionListener {
    private var _binding: FragmentProductAreasFilterBinding? = null
    private val binding get() = _binding!!
    private val mainViewModel: MainViewModel by activityViewModels()
    private val viewModel: ProductsViewModel by viewModel()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var regionAdapter: AreaAdapter<Regoin>
    private var searchAreaRequest: SearchAreaRequest? = null
    private var regionsSearchList = arrayListOf<Regoin>()
    private var regionsList = arrayListOf<Regoin>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductAreasFilterBinding.inflate(inflater, container, false)
        initViews()
        listeners()
        setRegionRV(regionsList)
        setSearch()
        observers()
        return binding.root
    }

    private fun setSearch() {
        binding.searchBar.btnFilter.visibility = View.GONE
        binding.searchBar.txtSearchWord.hint = getString(R.string.search_region)
        binding.searchBar.txtSearchWord.doOnTextChanged { text, _, _, _ ->
            if (!text.isNullOrEmpty() && regionsList.isNotEmpty()) {
                regionsSearchList.clear()
                regionsSearchList.add(Regoin(-1, -1, Constants.DETECT_LOCATION))
                regionsSearchList.addAll(
                    regionsList.filter { r ->
                        r.name.contains(
                            text,
                            true
                        ) && r.name != Constants.DETECT_LOCATION
                    } as ArrayList<Regoin>)
                setRegionRV(regionsSearchList)
            } else if (text.isNullOrEmpty() && regionsList.isNotEmpty()) {
                setRegionRV(regionsList)
            }
        }
    }

    private fun listeners() {
        binding.toolbarFragment.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun initViews() {
        val bundle = arguments ?: return
        val args = ProductRegionFilterFragmentArgs.fromBundle(bundle)
        searchAreaRequest = args.searchAreaRequest
        binding.toolbarFragment.tvTitle.text = getString(R.string.select_area)
        searchAreaRequest?.govId?.let {
            viewModel.getRegions(
                sharedPreferencesManager.getCountryID().toInt(), it
            )
        }
    }

    private fun setRegionRV(regionsList: ArrayList<Regoin>) {
        regionAdapter = AreaAdapter(requireContext(), regionsList, this)
        binding.recyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.recyclerView.adapter = regionAdapter
        val drawable = ContextCompat.getDrawable(requireContext(), R.drawable.divider)
        drawable?.let {
            val dividerItemDecoration = DividerItemDecorator(it)
            binding.recyclerView.addItemDecoration(dividerItemDecoration)
        }
    }

    private fun observers() {
        viewModel.regionsLiveData.observe(viewLifecycleOwner) { regionResponse ->
            if (regionResponse?.status == true) {

                val areaList = regionResponse.data
                if (areaList != null && areaList.size > 0) {
                    regionsList.add(Regoin(-1, -1, Constants.DETECT_LOCATION))
                    regionsList.add(Regoin(-1, -1, Constants.ALL_AREAS))
                    regionsList.addAll(areaList)
                    regionAdapter.updateAreaList(regionsList)
                }
            }
        }
    }

    override fun onAreaSelected(id: Int, type: String) {
        when (type) {
            Constants.ALL_AREAS -> {
                searchAreaRequest?.let { mainViewModel.setGovRegion(it) }
                findNavController().popBackStack()
            }
            Constants.DETECT_LOCATION -> {
                val latLng = sharedPreferencesManager.getCurrentLatlng()
                mainViewModel.setLatLng(latLng)
                findNavController().popBackStack()
            }
            else -> {
                searchAreaRequest?.regionId = id
                searchAreaRequest?.areaName = type
                searchAreaRequest?.let { mainViewModel.setGovRegion(it) }
                findNavController().popBackStack()
//            govId
//            findNavController().navigate(R.id.view_areaFilter,bundle)
            }
        }

    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}