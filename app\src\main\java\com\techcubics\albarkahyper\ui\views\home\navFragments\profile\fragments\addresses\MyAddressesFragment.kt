package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.addresses

import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentMyAddressesBinding
import com.techcubics.albarkahyper.ui.adapters.profile.LocationsAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Locations
import com.techcubics.shared.constants.Constants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class MyAddressesFragment : Fragment(), IRefreshListListener {


    private lateinit var myAddressesFragmentBinding: FragmentMyAddressesBinding
    private lateinit var progressBtn: ProgressButton
    private var position = 0
    private var locationList: MutableList<Locations> = mutableListOf()
    private var selectedLocation: Locations? = null
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val cartFragmentViewModel by viewModel<CartFragmentViewModel>()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private lateinit var popupDialog: PopupDialog


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        myAddressesFragmentBinding = FragmentMyAddressesBinding.inflate(inflater, container, false)
        progressBtn = ProgressButton(requireContext())
        Log.i("here", "maFrag")
        initViews()
        observeViews()
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        // Inflate the layout for this fragment
        return myAddressesFragmentBinding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){

            if(it != null) {
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        myAddressesFragmentBinding.myaddressesLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                }
                else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    myAddressesFragmentBinding.addressRV.visibility = View.GONE
                    myAddressesFragmentBinding.serverErrorLayout.layout.visibility = View.VISIBLE
                    myAddressesFragmentBinding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    myAddressesFragmentBinding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }

        profileViewModel.selectedEditLocationMutableLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                val args = Bundle()
                Log.i("edit", "observer" + it.locationId)
                args.putString(Constants.LOCATION_ID, it.locationId.toString())
                args.putString(Constants.FRAGMENT_TAG, Constants.UPDATE)
                profileViewModel.selectedEditLocationMutableLiveData.value = null
                findNavController().navigate(
                    R.id.action_myAddressesFragment_to_addAddressFragment,
                    args
                )
            }

        }

        cartFragmentViewModel.listLocationResponseMutableLiveData.observe(
            viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE,myAddressesFragmentBinding.actionLoadingAnimation.root)

            if(it != null) {
                if (it.status!!) {
                    if (it.data.isNullOrEmpty()) {
                        myAddressesFragmentBinding.addressRV.visibility = View.GONE
                        myAddressesFragmentBinding.placeholderLayout.layout.visibility = View.VISIBLE
                        myAddressesFragmentBinding.placeholderLayout.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                        myAddressesFragmentBinding.placeholderLayout.tvMessage.text = getString(com.techcubics.style.R.string.message_empty_list_general)
                    } else {
                        myAddressesFragmentBinding.addressRV.visibility = View.VISIBLE
                        myAddressesFragmentBinding.placeholderLayout.layout.visibility = View.GONE
                        locationList = it.data ?: mutableListOf()
                        myAddressesFragmentBinding.addressRV.adapter =
                            LocationsAdapter(
                                requireContext(),
                                it.data ?: mutableListOf(),
                                profileViewModel.selectedEditLocationMutableLiveData,
                                profileViewModel.selectedDeleteLocationMutableLiveData,
                                profileViewModel.itemPositionLiveData,
                                null,
                                this,
                                false,
                                selectedLocation
                            )
//                        cartFragmentViewModel.getCart()
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    myAddressesFragmentBinding.addressRV.visibility = View.GONE
                    myAddressesFragmentBinding.serverErrorLayout.layout.visibility =
                        View.VISIBLE
                    myAddressesFragmentBinding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    myAddressesFragmentBinding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                } else {
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
            }
        }

        profileViewModel.selectedDeleteLocationMutableLiveData.observe(
            viewLifecycleOwner) {
            if (it != null) {
                showConfirmDeleteDialog(it.locationId!!, it.country?.countryID!!)
                profileViewModel.selectedDeleteLocationMutableLiveData.value = null
            }
        }

        profileViewModel.deleteLocationLocationMutableLiveData.observe(
            viewLifecycleOwner) {
                if (it != null) {
                    if(it.status!!) {
                        Helper.loadingAnimationVisibility(View.GONE,myAddressesFragmentBinding.actionLoadingAnimation.root)
                        bottomSheetAlertDialog.showDialog(it.message!!)
                        myAddressesFragmentBinding.addressRV.adapter?.notifyItemRemoved(position)
                        locationList.removeAt(position)
//                    if (locationList.size > 0) {
//                        locationList[0].id?.let { id -> saveLocationID(id) }
//                    }
                    myAddressesFragmentBinding.addressRV.adapter?.notifyItemRangeChanged(
                        0,
                        locationList.size
                    )
                }else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    myAddressesFragmentBinding.addressRV.visibility = View.GONE
                    myAddressesFragmentBinding.serverErrorLayout.layout.visibility = View.VISIBLE
                    myAddressesFragmentBinding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    myAddressesFragmentBinding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                }

                profileViewModel.deleteLocationLocationMutableLiveData.value = null
            }
        }

        profileViewModel.itemPositionLiveData.observe(viewLifecycleOwner){
            if (it != null) {
                position = it
            }
        }

//        cartFragmentViewModel.cartResponse.observe(viewLifecycleOwner) { cart ->
//            if (cart != null && cart.data?.size!! > 0) {
//                selectedLocation = cart.data?.get(0)?.location
//                selectedLocation?.locationId?.let { sharedPreferencesManager.saveLocationID(it) }
//                cartFragmentViewModel.cartResponse.value = null
//                (myAddressesFragmentBinding.addressRV.adapter as LocationsAdapter)
//                    .updateSelectedLocation(selectedLocation)
//            }
//        }
    }

    private fun showConfirmDeleteDialog(locationId: Int, countryId: Int) {
        val builder = AlertDialog.Builder(requireContext())
            .create()
        val view = layoutInflater.inflate(R.layout.dialog_confirm_action, null)
        val noButton = view.findViewById<Button>(R.id.no)
        val yesButton = view.findViewById<Button>(R.id.yes)

        builder.setView(view)
        noButton.setOnClickListener {
            builder.dismiss()
        }
        yesButton.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,myAddressesFragmentBinding.actionLoadingAnimation.root)
            profileViewModel.deleteLocation(locationId, countryId, sharedPreferencesManager.getCountryCode())
            builder.dismiss()
        }
        builder.setCanceledOnTouchOutside(false)
        builder.show()
    }

    private fun initViews() {
        checkAutherization()

        cartFragmentViewModel.listLocations(sharedPreferencesManager.getCountryID().toInt(), sharedPreferencesManager.getCountryCode())
        Helper.loadingAnimationVisibility(View.VISIBLE,myAddressesFragmentBinding.actionLoadingAnimation.root)
        myAddressesFragmentBinding.toolbar.tvTitle.text =
            getString(com.techcubics.style.R.string.my_addresses)
        myAddressesFragmentBinding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onRefreshList() {
        cartFragmentViewModel.listLocations(sharedPreferencesManager.getCountryID().toInt(), sharedPreferencesManager.getCountryCode())
    }

    override var location: Locations?=null


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}