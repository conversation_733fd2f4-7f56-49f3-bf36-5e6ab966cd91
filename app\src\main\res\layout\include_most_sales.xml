<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingBottom="16dp"
    >

    <ImageView
        android:id="@+id/imageView3"
        android:layout_width="@dimen/icons_size_height_weight_9"
        android:layout_height="@dimen/icons_size_height_weight_9"
        android:layout_marginTop="16dp"
        android:src="@drawable/ic_fire_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView"
        style="@style/label_section_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:layout_marginStart="16dp"
        android:text="@string/section_most_sales"
        app:layout_constraintBottom_toBottomOf="@+id/imageView3"
        app:layout_constraintStart_toEndOf="@+id/imageView3"
        app:layout_constraintTop_toTopOf="@+id/imageView3"
        app:layout_constraintVertical_bias="0.26" />




<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    android:layout_marginTop="16dp"
    app:layout_constraintTop_toBottomOf="@+id/textView">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMostWantedProducts"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:itemCount="1"
        android:layout_weight="1"
        tools:listitem="@layout/item_store_product"

        />

    <ProgressBar
        android:id="@+id/horizontal_loading"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_gravity="center_vertical"
        android:indeterminateTint="@color/app_color"
        android:visibility="gone" />
</LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>