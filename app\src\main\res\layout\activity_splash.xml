<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.FullScreen"
    tools:context=".ui.views.SplashActivity">

        <ImageView
            android:id="@+id/logo"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:src="@drawable/logo2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"

            />



</androidx.constraintlayout.widget.ConstraintLayout>