<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/callus_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.views.home.navFragments.profile.fragments.addresses.AddAddressFragment">


    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_marginTop="16dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp">
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/how_can_we_help_you"
                        android:textAppearance="@style/header1_callus"/>
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/customer_service"
                        android:singleLine="true"
                        android:textAppearance="@style/header2_callus"/>
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/all_day"
                        android:textAppearance="@style/header2_callus"/>
                </LinearLayout>
                <ImageView
                    android:layout_marginTop="29dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/portrait_callus"
                    android:layout_gravity="center_horizontal"/>


                <LinearLayout
                    android:layout_marginTop="42dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/first_name"
                        android:textAppearance="@style/label_callus" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="@string/last_name"
                        android:textAppearance="@style/label_callus" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <EditText
                        android:id="@+id/first_name"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_callus"
                         />

                    <EditText
                        android:id="@+id/last_name"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_callus"
                         />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/email"
                    android:textAppearance="@style/label_callus" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:gravity="right">

                    <EditText
                        android:padding="3dp"
                        android:id="@+id/email"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/et_style_with_borders"
                        android:textAppearance="@style/label_edittext_callus"
                        />
                </LinearLayout>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/phone_number"
                    android:textAppearance="@style/label_callus" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style_with_borders"
                    android:gravity="right"
                    android:orientation="horizontal"
                    android:layoutDirection="ltr">

                    <com.hbb20.CountryCodePicker
                        app:ccp_defaultNameCode="EG"
                        android:id="@+id/country_picker"
                        android:layout_width="wrap_content"
                        app:ccp_showNameCode="false"
                        android:layout_height="wrap_content" />

                    <EditText
                        android:id="@+id/phone_edt"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_gravity="center_vertical"
                        android:background="@null"
                        android:inputType="number"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_callus"
                        />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/subject"
                    android:textAppearance="@style/label_callus" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:gravity="right">

                    <EditText
                        android:padding="3dp"
                        android:id="@+id/subject"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/et_style_with_borders"
                        android:textAppearance="@style/label_edittext_callus"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/notes"
                        android:textAppearance="@style/label_callus" />

                    <EditText
                        android:id="@+id/additional_notes"
                        android:layout_width="match_parent"
                        android:layout_height="98dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_callus"
                        />
                </LinearLayout>

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="23dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="55dp" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>




    </androidx.appcompat.widget.LinearLayoutCompat>