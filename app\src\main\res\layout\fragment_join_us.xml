<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:id="@+id/joinus_layout"
    tools:context=".ui.views.home.navFragments.profile.fragments.addresses.AddAddressFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintBottom_toTopOf="@id/sc_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
            android:id="@+id/sc_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/gallery_name_"
                        android:textAppearance="@style/label_joinus" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="30dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/gallery_branches_count"
                        android:textAppearance="@style/label_joinus" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <EditText
                        android:id="@+id/gallery_name"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus" />

                    <EditText
                        android:id="@+id/b_count"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="number"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/gallery_type"
                        android:textAppearance="@style/label_joinus" />

                    <EditText
                        android:id="@+id/gellery_type"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:cursorVisible="false"
                        android:drawableEnd="@drawable/ic_arrow_down"
                        android:drawablePadding="12dp"
                        android:focusableInTouchMode="false"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="@string/country_"
                        android:textAppearance="@style/label_joinus" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="25dp"
                        android:layout_marginEnd="25dp"
                        android:layout_weight="1"
                        android:text="@string/governorate"
                        android:textAppearance="@style/label_joinus" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <EditText
                        android:id="@+id/country"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:cursorVisible="false"
                        android:drawableEnd="@drawable/ic_arrow_down"
                        android:drawablePadding="12dp"
                        android:focusableInTouchMode="false"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                        />

                    <EditText
                        android:id="@+id/governerate"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:clickable="false"
                        android:cursorVisible="false"
                        android:drawableEnd="@drawable/ic_arrow_down"
                        android:drawablePadding="12dp"
                        android:enabled="false"
                        android:focusableInTouchMode="false"
                        android:inputType="textNoSuggestions"
                        android:longClickable="false"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                        />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/region"
                    android:textAppearance="@style/label_joinus" />

                <EditText
                    android:id="@+id/region"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:clickable="false"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/ic_arrow_down"
                    android:drawablePadding="12dp"
                    android:enabled="false"
                    android:focusableInTouchMode="false"
                    android:inputType="textNoSuggestions"
                    android:longClickable="false"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/address_"
                        android:textAppearance="@style/label_joinus" />

                    <EditText
                        android:id="@+id/address"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                        />
                </LinearLayout>

                <androidx.fragment.app.FragmentContainerView
                    android:id="@+id/map"
                    android:name="com.google.android.gms.maps.SupportMapFragment"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style_with_borders" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/owner"
                        android:textAppearance="@style/label_joinus" />

                    <EditText
                        android:id="@+id/owner_name"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                        />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/phone_number_"
                    android:textAppearance="@style/label_joinus" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style_with_borders"
                    android:gravity="right"
                    android:orientation="horizontal"
                    android:layoutDirection="ltr">

                    <com.hbb20.CountryCodePicker
                        android:id="@+id/country_picker"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:ccp_showNameCode="false"
                        app:ccp_defaultNameCode="EG" />

                    <EditText
                        android:id="@+id/phone_edt"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_gravity="center_vertical"
                        android:background="@null"
                        android:inputType="number"
                        android:textAppearance="@style/label_edittext_joinus" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/email"
                        android:textAppearance="@style/label_joinus" />

                    <EditText
                        android:id="@+id/email"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                         />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/social_media"
                        android:textAppearance="@style/label_joinus" />

                    <EditText
                        android:id="@+id/social_account"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                         />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/notes"
                        android:textAppearance="@style/label_joinus" />

                    <EditText
                        android:id="@+id/additional_notes"
                        android:layout_width="match_parent"
                        android:layout_height="98dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/et_style_with_borders"
                        android:inputType="textNoSuggestions"
                        android:padding="3dp"
                        android:textAppearance="@style/label_edittext_joinus"
                         />
                </LinearLayout>

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="23dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="110dp" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/action_loading_animation"
            layout="@layout/include_action_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
