package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import android.content.Intent
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.StoreDetailsMenuData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.StoreCategoryOnItemClickListener
import com.techcubics.albarkahyper.databinding.ItemCategoryBinding


class CategoryHolderItem<T>(
    val binding: ItemCategoryBinding,
    val context: Context,
    val onCategoryListener: StoreCategoryOnItemClickListener? = null
) : RecyclerView.ViewHolder(binding.root) {

    private val TAG = "CategoryHolderItem"
    private lateinit var intent: Intent

    fun bind(data: T) {
        when (data) {
            is Category -> {
                setData(data.name.toString(), data.image.toString())
            }
            is StoreDetailsMenuData -> {
                setData(data.name, data.image)
            }
        }
        binding.root.setOnClickListener {
            onCategoryListener?.onCategoryClick(data)
        }
    }

    private fun setData(name: String, img: String) {
        binding.tvTitle.text = name
        Helper.loadImage(context, img, binding.icon)
    }
}