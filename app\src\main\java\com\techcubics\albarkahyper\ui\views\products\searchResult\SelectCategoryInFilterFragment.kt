package com.techcubics.albarkahyper.ui.views.products.searchResult

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.techcubics.data.model.pojo.Category
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.ICategoryClickListener
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentSelectCategoryInFilterBinding
import com.techcubics.albarkahyper.ui.adapters.home.FilterCategoryAdapter
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.ArrayList

class SelectCategoryInFilterFragment : Fragment() , ICategoryClickListener {

    private lateinit var binding : FragmentSelectCategoryInFilterBinding
    private lateinit var categoryAdapter: FilterCategoryAdapter
    private  val categoryViewModel: SectionsViewModel by viewModel<SectionsViewModel>()
    private lateinit var progressButton: ProgressButton
    private var selectedCategories: ArrayList<Category> = arrayListOf()
    private val mainViewModel: MainViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentSelectCategoryInFilterBinding.inflate(inflater, container, false)
        initView()
        observeViews()
        return binding.root
    }

    private fun observeViews() {
        categoryViewModel.categoriesResponse.observe(viewLifecycleOwner)
        {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            binding.containerLayout.visibility = View.VISIBLE
            binding.btnSearchCategory.constraintsLayout.visibility = View.VISIBLE

            if (it.status!!) {

                if (it.data != null) {
                    showCategoryData(it.data!!)
                } else {
                    //empty


                }

            } else {


            }
        }
    }

    private fun initView() {
        binding.toolbarFilter.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.btnSearchCategory)
        binding.btnSearchCategory.textView.text = getString(com.techcubics.style.R.string.done)
        binding.toolbarFilter.tvTitle.text = getString(com.techcubics.style.R.string.available_categories)
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        binding.containerLayout.visibility = View.GONE
        categoryAdapter = FilterCategoryAdapter(handler = this)
        categoryViewModel.getCategories()
        progressButton.binding.constraintsLayout.setOnClickListener {
            Log.i("pharma","from categ frag"+selectedCategories)
            mainViewModel.setCategoryList(selectedCategories)
            CoroutineScope(Dispatchers.Main).launch {
                progressButton.btnActivated()
                delay(1000)
                progressButton.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.done),null)
                delay(300)
                findNavController().popBackStack()
            }

        }
    }

    fun showCategoryData(data: MutableList<Category>) {
        categoryAdapter.setItemsList(data)
        binding.includeFilterCategorySection.rvCategories.adapter = categoryAdapter
    }

    override fun onAddCategoryClick(category: Category) {
        Log.i("pharma","added : "+category)
         selectedCategories.add(category)
    }

    override fun onRemoveCategory(category: Category) {
        Log.i("clinic","deleted : "+category)
        selectedCategories.remove(category)
    }


}