package com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.NotificationRequest
import com.techcubics.data.model.requests.home.DiscountRequest
import com.techcubics.data.model.requests.home.OffersRequest
import com.techcubics.data.model.requests.home.SavesRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.HomeRepo
import com.techcubics.data.repos.home.HomeRepoImpl
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.data.repos.product.ProductsRepoImpl
import com.techcubics.shared.enums.SortTypesEnum
import kotlinx.coroutines.launch

class SectionsViewModel(private val homeRepository: HomeRepo,private val productsRepo: ProductsRepo,private val sharedPreferencesManager: SharedPreferencesManager):ViewModel() {

    val categoriesResponse= MutableLiveData<BaseResponse<MutableList<Category>>>()
    val offersResponse= MutableLiveData<BaseResponse<MutableList<Offer>>>()
    val savesResponse= MutableLiveData<BaseResponse<MutableList<Save>>>()
    val discountsResponse= MutableLiveData<BaseResponse<MutableList<Discount>>>()
    val notificationsResponse= MutableLiveData<BaseResponse<MutableList<Notification>>>()
    val readNotificationsResponse= MutableLiveData<BaseResponse<Any>>()

    val addCartResponse : MutableLiveData<BaseResponse<CartData>> by lazy {
        MutableLiveData()
    }



    fun getCategories(){

        viewModelScope.launch {
           // val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

            val rs=homeRepository.getCategories()
            categoriesResponse.postValue(rs!!)
        }

    }

    fun getOffers(page:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request=OffersRequest(SortTypesEnum.TopDown,countryID,page)
            val rs=homeRepository.getOffers(request)
            offersResponse.postValue(rs!!)
        }

    }

    fun getSaves(page:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= SavesRequest(SortTypesEnum.TopDown,countryID,page)
            val rs=homeRepository.getSaves(request)
            savesResponse.postValue(rs!!)
        }

    }

    /*fun getDiscounts(page:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= DiscountRequest(SortTypesEnum.TopDown,countryID,page)
            val rs=homeRepository.getDiscounts(request)
            discountsResponse.postValue(rs!!)
        }

    }*/
    fun addToCart(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId,modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint,request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }


    fun getNotifications(page:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= NotificationRequest(countryID,page)
            val rs=homeRepository.getNotifications(request)
            notificationsResponse.postValue(rs!!)
        }

    }

    fun readNotifications(){


        viewModelScope.launch {
            val rs=homeRepository.readNotifications()
            readNotificationsResponse.postValue(rs!!)
        }

    }


}