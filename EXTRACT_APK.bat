@echo off
echo ==========================================
echo   AlBarkaHyper APK Extractor
echo ==========================================
echo.

echo Attempting to build APK files...
echo This may take several minutes, please wait...
echo.

REM Try the simplest approach first
echo Step 1: Building Debug APK...
gradlew assembleDebug
if %ERRORLEVEL% equ 0 (
    echo ✓ Debug APK build completed!
) else (
    echo Debug build failed, error code: %ERRORLEVEL%
)

echo.
echo Step 2: Building Release APK...
gradlew assembleRelease
if %ERRORLEVEL% equ 0 (
    echo ✓ Release APK build completed!
) else (
    echo Release build failed, error code: %ERRORLEVEL%
)

echo.
echo Step 3: Building AAB...
gradlew bundleRelease
if %ERRORLEVEL% equ 0 (
    echo ✓ AAB build completed!
) else (
    echo AAB build failed, error code: %ERRORLEVEL%
)

echo.
echo ==========================================
echo   Checking Results
echo ==========================================
echo.

REM Check for Debug APK
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ DEBUG APK FOUND!
    echo Location: %CD%\app\build\outputs\apk\debug\app-debug.apk
    for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do echo Size: %%~zI bytes
    echo.
) else (
    echo ❌ Debug APK not found
)

REM Check for Release APK
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✅ RELEASE APK FOUND!
    echo Location: %CD%\app\build\outputs\apk\release\app-release.apk
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo Size: %%~zI bytes
    echo.
    echo 🎯 This file is ready for Google Play Store!
) else (
    echo ❌ Release APK not found
)

REM Check for AAB
if exist "app\build\outputs\bundle\release\app-release.aab" (
    echo ✅ AAB FOUND!
    echo Location: %CD%\app\build\outputs\bundle\release\app-release.aab
    for %%I in ("app\build\outputs\bundle\release\app-release.aab") do echo Size: %%~zI bytes
    echo.
    echo 🎯 This file is PREFERRED for Google Play Store!
) else (
    echo ❌ AAB not found
)

echo.
echo ==========================================
echo   Summary
echo ==========================================
echo.

REM Count successful builds
set "SUCCESS_COUNT=0"
if exist "app\build\outputs\apk\debug\app-debug.apk" set /a SUCCESS_COUNT+=1
if exist "app\build\outputs\apk\release\app-release.apk" set /a SUCCESS_COUNT+=1
if exist "app\build\outputs\bundle\release\app-release.aab" set /a SUCCESS_COUNT+=1

if %SUCCESS_COUNT% gtr 0 (
    echo ✅ SUCCESS: %SUCCESS_COUNT% file(s) created!
    echo.
    echo 📁 Full paths:
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo DEBUG APK: %CD%\app\build\outputs\apk\debug\app-debug.apk
    )
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo RELEASE APK: %CD%\app\build\outputs\apk\release\app-release.apk
    )
    if exist "app\build\outputs\bundle\release\app-release.aab" (
        echo RELEASE AAB: %CD%\app\build\outputs\bundle\release\app-release.aab
    )
    echo.
    echo 🎉 Your files are ready!
    echo.
    echo 📋 For Google Play Store:
    echo - Use AAB file if available (preferred)
    echo - Use Release APK if AAB not available
    echo - Check google-play-assets folder for store materials
) else (
    echo ❌ FAILED: No APK/AAB files were created!
    echo.
    echo 🔧 Possible solutions:
    echo 1. Check internet connection
    echo 2. Run Android Studio and sync project
    echo 3. Install missing Android SDK components
    echo 4. Check antivirus settings
    echo 5. Try running as Administrator
)

echo.
pause
