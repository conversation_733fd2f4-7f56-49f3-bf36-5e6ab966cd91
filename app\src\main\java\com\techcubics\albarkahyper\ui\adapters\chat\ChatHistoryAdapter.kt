package com.techcubics.albarkahyper.ui.adapters.chat

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.ui.adapters.holders.chat.SendChatHolderItem
import com.techcubics.albarkahyper.databinding.ItemChatSendBinding

class ChatHistoryAdapter <T>(): RecyclerView.Adapter<SendChatHolderItem<T>>() {


    lateinit var items:List<T>

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SendChatHolderItem<T> {

        val itemBinding=ItemChatSendBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SendChatHolderItem(itemBinding!!,parent.context)

    }

    override fun onBindViewHolder(holder: Send<PERSON><PERSON>HolderItem<T>, position: Int) {

        holder.bind(items.get(position))

    }


    override fun getItemCount(): Int {
        return items.size
    }
}