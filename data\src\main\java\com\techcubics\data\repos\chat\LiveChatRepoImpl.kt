package com.techcubics.data.repos.chat

import com.techcubics.data.model.pojo.LiveChatRoomData
import com.techcubics.data.model.pojo.LiveMessageData
import com.techcubics.data.model.pojo.SendSupportData
import com.techcubics.data.model.pojo.SupportTicketData
import com.techcubics.data.model.requests.TicketSendCallRequest
import com.techcubics.data.model.requests.chat.*
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

class LiveChatRepoImpl(private val retrofitBuilder: RetrofitBuilder): RepositoryResponse ,LiveChatRepo{


    override suspend fun getHistory(request: LiveMessagesRequest): BaseResponse<MutableList<LiveMessageData>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.liveChatMessagesCall(country_id = request.country_id,storeID = request.storeID)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun send(request: SendLiveRequest): BaseResponse<Any>? {
        return try {

            val result = retrofitBuilder.getBindObject()?.liveSendCall(country_id = request.country_id, request = request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun liveGetAllChats(): BaseResponse<MutableList<LiveChatRoomData>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.liveGetAllChats()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

}