package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.NotificationClickListener
import com.techcubics.data.model.pojo.Notification
import com.techcubics.albarkahyper.databinding.ItemNotificationBinding
import java.text.SimpleDateFormat
import java.util.*

class NotificationHolderItem (val binding: ItemNotificationBinding, val context: Context , val onClickListener: NotificationClickListener): RecyclerView.ViewHolder(binding.root) {

    fun bind(data: Notification){

        // iso 8601 date
        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        val l: Date = format.parse(data.created_at)
        var dateFormatter = SimpleDateFormat("dd/MM/yyyy").format(l)
        var timeFormatter = SimpleDateFormat("hh:mm aa").format(l)

        binding.tvDateTime.text="${timeFormatter} ${dateFormatter}"
        binding.tvTitle.text=data.data.title
        binding.tvMessage.text=data.data.description

        binding.root.setOnClickListener {
            onClickListener.onClick(data.notifyType,data.data.shop_id)
        }
    }
}