package com.techcubics.albarkahyper.ui.views.home.navFragments.home.fragments


import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageButton
import androidx.activity.OnBackPressedCallback
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.SnapHelper
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.albarkahyper.MainActivity
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.common.Helper.loadingAnimationVisibility
import com.techcubics.albarkahyper.databinding.FragmentHomeBinding
import com.techcubics.albarkahyper.ui.adapters.home.*
import com.techcubics.albarkahyper.ui.adapters.product.BannersAdaper
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.home.viewmodels.HomeFragmentViewModel
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.Constants.INTENT_ID
import com.techcubics.shared.constants.Constants.INTENT_NAME
import com.techcubics.shared.enums.LoginStateEnum
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.style.R
import kotlinx.coroutines.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class HomeFragment : Fragment(), IPageDetails<NearStoreTypes>, IOnAdapterItemClickHandler {

    private val TAG = "HomeFragment"
    private var _homeBinding: FragmentHomeBinding? = null
    private val homeBinding: FragmentHomeBinding get() = _homeBinding!!
    lateinit var branchTypesAdapter: BranchesTypesAdaper
    private lateinit var popupDialog: PopupDialog
    private lateinit var bannersAdapter: BannersAdaper
    private var bannersSlider: Slider? = null
    private lateinit var onCategoryListener: StoreCategoryOnItemClickListener
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val homeViewModel by viewModel<HomeFragmentViewModel>()
    private val authViewModel by viewModel<AuthViewModel>()
     val notificationsFragmentViewModel: SectionsViewModel by viewModel<SectionsViewModel>()
   private lateinit var refreshClickListener: IItemClickListener
    private  val productsViewModel: ProductsViewModel by viewModel<ProductsViewModel>()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        if (_homeBinding == null || homeViewModel.oldCountryId != sharedPreferencesManager.getCountryID() || !NetworkChangeReceiver.isOnline(requireContext()) || homeBinding.includeNetwork.networkView.isVisible) {
            homeViewModel.oldCountryId = sharedPreferencesManager.getCountryID()
            _homeBinding = FragmentHomeBinding.inflate(inflater, container, false)
            checkNetwork()
        }
        listeners()
        observers()

        return homeBinding.root

    }

    private fun listeners() {
        homeBinding.includeNetwork.refresh.setOnClickListener {
            checkNetwork()
        }
        onCategoryListener = object : StoreCategoryOnItemClickListener {
            override fun <T> onCategoryClick(item: T) {
                val category = item as Category
                val bundle = Bundle()
                bundle.putInt(INTENT_ID, category.id!!)
                bundle.putString(INTENT_NAME, category.name)
                findNavController().navigate(
                    com.techcubics.albarkahyper.R.id.display_products_by_category,
                    bundle
                )
            }

        }

        refreshClickListener=object:IItemClickListener{
            override fun perform() {
               notificationsFragmentViewModel.readNotifications()
            }
        }
    }

    private fun checkNetwork() {
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            loadingAnimationVisibility(View.VISIBLE, homeBinding.loadingAnimation.root)
            homeBinding.includeNetwork.networkView.visibility = View.GONE
            init()
            events()
        } else {
            homeBinding.includeHomeToolbar.tvUnReadNo.visibility = View.GONE
            loadingAnimationVisibility(View.GONE, homeBinding.loadingAnimation.root)
            homeBinding.includeNetwork.networkView.visibility = View.VISIBLE
        }
    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
        Helper.exitFullScreen(requireContext())
    }
    override fun init() {

        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        } else {
            findNavController().navigate(com.techcubics.albarkahyper.R.id.hometologin)
        }

        productsViewModel.getBanners()
        showHidePlaceHolder(show = false, type = null, message = null)
        Log.i("here", "callhome")
        loadingAnimationVisibility(View.VISIBLE,homeBinding.loadingAnimation.root)
        homeBinding.includeHomeBranchTypesSection.root.visibility = View.GONE
        homeBinding.includeHomeToolbar.tvUnReadNo.visibility = View.GONE

        homeViewModel.getNearStoreTypes("it.lat" ,"it.lng")

        /*
        sharedPreferencesManager.let { sh ->

            if (sh != null) {

                try {

                    sh.getCurrentLatlng().let {
                        // Log.d(TAG, "showData: ${it.lat},${it.lng}")

                        if (!it.equals(null)) {

                            if (!it.lat.equals(null)) {


                                homeViewModel.getNearStoreTypes(it.lat ,it.lng)

                            }


                        }
                    }
                } catch (ex: Exception) {

                    Log.d(TAG, "showData: ${ex.message}")
                }


            }
        }
*/

        homeBinding.rvBanners.visibility=View.GONE
        homeBinding.indicator.visibility=View.GONE
    }
    override fun observers() {

        homeViewModel.nearStoreTypesResponse.observe(viewLifecycleOwner, Observer { it ->
            try {
                if (it != null) {
                    loadingAnimationVisibility(View.GONE, homeBinding.loadingAnimation.root)
                    if (it.status!!) {

                        Log.i("clothes", sharedPreferencesManager.getCountryID())
                        if (it.data != null) {
                            homeBinding.mainView.visibility = View.VISIBLE
                            showData(it.data!!)
                            if (it.data?.types!!.isNotEmpty()) {

                                homeBinding.includeHomeBranchTypesSection.root.visibility = View.VISIBLE
                            }
                            if(sharedPreferencesManager.isLoggedIn() == "true"){
                                if(it.data!!.total_of_unread_notifications!=null){

                                    if(it.data!!.total_of_unread_notifications!!>0){

                                        homeBinding.includeHomeToolbar.tvUnReadNo.visibility=View.VISIBLE
                                        //homeBinding.includeHomeToolbar.btnNotifications.visibility=View.VISIBLE
                                    }
                                }
                            }



                        } else {
                            //empty
                           showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                            Log.d(TAG, "observers: 1")

                        }
                    } else {

                        //error
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Error,
                            message = it.message
                        )
                        Log.d(TAG, "observers: 2")
                    }

                    homeViewModel.homeResponse.value = null
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
                Log.d(TAG, "observers: 3")
            }

        })

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {
            loadingAnimationVisibility(View.GONE,homeBinding.loadingAnimation.root)
            if(it != null){

                if (it.message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!) {
                    CoroutineScope(Dispatchers.Main).launch {
//                        binding.cartLayout.visibility = View.GONE
                        sharedPreferencesManager.setLoginState(LoginStateEnum.SessionExpired.value)
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }else{
                    if (openNotification){
                        var action=HomeFragmentDirections.actionHomeFragmentToNotificationsFragment(refreshClickListener)

                        findNavController().navigate(action)
                        openNotification = false
                    }
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }

        notificationsFragmentViewModel.readNotificationsResponse.observe(viewLifecycleOwner, Observer {

            try {
                homeBinding.includeHomeToolbar.tvUnReadNo.visibility=View.GONE

            }catch (ex:Exception){

            }

        })

        productsViewModel.bannersResponse.observe(viewLifecycleOwner, Observer {


            try {
                if (it.status!!) {

                    if(it.data!=null){

                        if(it.data!!.size>0) {
                            homeBinding.rvBanners.visibility=View.VISIBLE
                            showBanners(it.data!!)

                        }else{
                            homeBinding.rvBanners.visibility=View.GONE
                        }
                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })

    }
    override fun events() {

        activity?.onBackPressedDispatcher?.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {

                activity?.finishAffinity()
            }
        })

//        homeBinding.includeHomeToolbar.btnNotifications.setOnClickListener{
//                    activity?.finishAffinity()
//                }

        homeBinding.includeHomeToolbar.btnNotifications.setOnClickListener {


            if (sharedPreferencesManager.isLoggedIn() == "true") {
                loadingAnimationVisibility(View.VISIBLE,homeBinding.loadingAnimation.root)
                openNotification = true
                authViewModel.checkAuthorization()
            } else {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
            }


        }


    }
    private var openNotification = false
    override fun showData(items: NearStoreTypes) {
        homeBinding.includeHomeToolbar.tvUnReadNo.text=items.total_of_unread_notifications.toString()
        showBranchTypes(items.types!!)

    }
    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {


        if (show) {
            homeBinding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    homeBinding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    homeBinding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    homeBinding.includeHomeToolbar.tvUnReadNo.visibility = View.GONE
                    homeBinding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    if (message == Constants.SERVER_ERROR) {
                        homeBinding.placeholder.tvMessage.text =
                            getString(com.techcubics.style.R.string.server_error)

                    } else {
                        homeBinding.placeholder.tvMessage.text = message

                    }

                }
                else -> throw IllegalStateException("error")
            }
        } else {

            homeBinding.placeholder.root.visibility = View.GONE

        }
    }
    private fun showBranchTypes(items: List<StoreTypes>) {

        branchTypesAdapter = BranchesTypesAdaper(this)
        branchTypesAdapter.setItemsList(items)
        homeBinding.includeHomeBranchTypesSection.rvBranchesTypes.adapter = branchTypesAdapter
        homeBinding.includeHomeBranchTypesSection.rvBranchesTypes.layoutManager =
            GridLayoutManager(context,2)
        // StaggeredGridLayoutManager (4,LinearLayoutManager.VERTICAL)

    }
    override fun onDestroy() {
        super.onDestroy()

    }

    private fun showBanners(data:MutableList<BannerData>){
        bannersAdapter = BannersAdaper()
        bannersAdapter.items = data
        homeBinding.rvBanners.adapter = bannersAdapter
        //////////////////
        var manager= LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

        val snapHelper: SnapHelper = PagerSnapHelper()
        homeBinding.rvBanners.setLayoutManager(manager)
        homeBinding.rvBanners.setOnFlingListener(null)
        snapHelper.attachToRecyclerView(homeBinding.rvBanners)
        homeBinding.indicator.visibility=View.VISIBLE
        //////////////
        homeBinding.indicator.attachToRecyclerView( homeBinding.rvBanners)

        if (data.size > 1) {
            bannersSlider = Slider(homeBinding.rvBanners, 3000)
            bannersSlider?.setAdapter(bannersAdapter)
            bannersSlider?.start()
            bannersSlider?.setSnapHelper()
        }
    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle=Bundle()
        bundle.putInt(Constants.INTENT_ID,itemId!!)
        bundle.putString(Constants.INTENT_NAME, type)
        findNavController().navigate(com.techcubics.albarkahyper.R.id.action_homeFragment_to_store,bundle)

    }



}
