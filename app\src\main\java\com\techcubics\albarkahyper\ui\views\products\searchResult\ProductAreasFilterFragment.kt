package com.techcubics.albarkahyper.ui.views.products.searchResult

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Governerate
import com.techcubics.data.model.requests.SearchAreaRequest
import com.techcubics.albarkahyper.common.DividerItemDecorator
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentProductAreasFilterBinding
import com.techcubics.albarkahyper.ui.adapters.product.AreaAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.style.R.drawable
import com.techcubics.style.R.string
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class ProductAreasFilterFragment : Fragment(), AreaSelectionListener {
    private var _binding: FragmentProductAreasFilterBinding? = null
    private val binding get() = _binding!!
    private val mainViewModel: MainViewModel by activityViewModels()
    private val viewModel: ProductsViewModel by viewModel()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var govAdapter: AreaAdapter<Governerate>
    private var govId = -1
    private var lat: Double? = null
    private var lng: Double? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductAreasFilterBinding.inflate(inflater, container, false)
        initViews()
        listeners()
        setGovRV()
        observers()
        return binding.root
    }

    private fun listeners() {
        binding.toolbarFragment.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun initViews() {
        binding.toolbarFragment.tvTitle.text = getString(string.select_city)
        binding.searchCont.visibility = View.GONE
        viewModel.getGovernorates(sharedPreferencesManager.getCountryID().toInt())
    }

    private fun setGovRV() {
        govAdapter = AreaAdapter(requireContext(), arrayListOf(), this)
        binding.recyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.recyclerView.adapter = govAdapter
        val drawable = ContextCompat.getDrawable(requireContext(), drawable.divider)
        drawable?.let {
            val dividerItemDecoration = DividerItemDecorator(it)
            binding.recyclerView.addItemDecoration(dividerItemDecoration)
        }
    }

    private fun observers() {
        viewModel.governoratesLiveData.observe(viewLifecycleOwner) { govResponse ->
            if (govResponse?.status == true) {
                val areaList = govResponse.data
                if (areaList != null && areaList.size > 0) {
                    val newAreaList = arrayListOf<Governerate>()
                    newAreaList.add(Governerate(-1, -1, Constants.DETECT_LOCATION))
                    newAreaList.add(Governerate(-1, -1, Constants.ALL_CITIES))
                    newAreaList.addAll(areaList)
                    govAdapter.updateAreaList(newAreaList)
                }
            }
        }
    }

    override fun onAreaSelected(id: Int, type: String) {
        when (type) {
            Constants.ALL_CITIES -> {
                mainViewModel.setAllCities(getString(string.all_cities))
                findNavController().popBackStack()
            }
            Constants.DETECT_LOCATION -> {
                val latLng =  sharedPreferencesManager.getCurrentLatlng()
                mainViewModel.setLatLng(latLng)
                findNavController().popBackStack()
            }
            else -> {
                val searchAreaRequest = SearchAreaRequest(id,null,type)
                val action = ProductAreasFilterFragmentDirections.viewRegionFilter(searchAreaRequest)
                findNavController().navigate(action)
//            govId
//            findNavController().navigate(R.id.view_areaFilter,bundle)
            }
        }

    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}

interface AreaSelectionListener {
    fun onAreaSelected(id: Int, type: String)
}

