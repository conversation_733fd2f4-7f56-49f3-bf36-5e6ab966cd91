package com.techcubics.albarkahyper.ui.adapters.holders.product


import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Size
import com.techcubics.albarkahyper.common.ISizesClickListener
import com.techcubics.albarkahyper.databinding.ItemSizeFilterBinding


class ProductSizeFilterHolderItem(
    val binding: ItemSizeFilterBinding,
    val context: Context,
    val handler: ISizesClickListener? = null
) : RecyclerView.ViewHolder(binding.root) {

    private val TAG = "ProductSizeFilterHolderItem"
    private var isSelected: Boolean = false
    fun bind(size: Size) {

        binding.size.setText(size.name)

        binding.root.setOnClickListener {

            if (isSelected == false) {

                isSelected = true
                binding.size.isChecked = true
                handler?.onAddSizeClick(size)
            } else {

                isSelected = false
                binding.size.isChecked = false
                handler?.onRemoveSize(size)
            }

        }

        binding.size.setOnClickListener {

            if (isSelected == false) {

                isSelected = true

                handler?.onAddSizeClick(size)
            } else {

                isSelected = false

                handler?.onRemoveSize(size)
            }

        }


    }


}