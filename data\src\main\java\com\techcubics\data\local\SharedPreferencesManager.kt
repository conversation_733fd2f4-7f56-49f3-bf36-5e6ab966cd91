package com.techcubics.data.local

import android.annotation.TargetApi
import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.techcubics.data.model.pojo.Countries
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.pojo.SocialUser
import com.techcubics.data.model.pojo.User
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LoginStateEnum
import java.lang.reflect.Type
import java.util.*


class SharedPreferencesManager(private val prefs: SharedPreferences) {
    private val COUNTRYID = "COUNTRYID"
    private val MOBILECODE = "MOBILECODE"

    //    private lateinit var prefs: SharedPreferences
//    private const val PREFS_NAME = "params"
    private val TOKEN = "TOKEN"
    private val FIREBASE_TOKEN = "FIREBASE_TOKEN"
    private val LANGUAGE = "language"
    private val LOGGED = "user"
    private val COUNTRYCODE = "COUNTRYCODE"
    private val NAME = "NAME"
    private val PASSWORD = "PASSWORD"
    private val ADDRESS = "ADDRESS"
    private val USER_ID = "USER_ID"
    private val ID = "ID"
    private val EMAIL = "EMAIL"
    private val SHOP = "SHOP"


//    fun init(context: Context) {
//        prefs = context.getSharedPreferences(PREFS_NAME, MODE_PRIVATE)
//    }

    fun getToken(): String? = prefs.getString(TOKEN, " ")

    fun saveToken(value: String?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(TOKEN, value)
        prefsEditor.apply()

    }

    fun getFireBaseToken(): String = prefs.getString(FIREBASE_TOKEN, "")!!

    fun saveFireBaseToken(value: String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(FIREBASE_TOKEN, value)
        prefsEditor.apply()


    }

    fun updateBaseContextLocale(context: Context): Context {
        val languageCode = getLanguage()
        return updateLocale(context, languageCode)
    }


    fun updateLocale(context: Context, languageCode: String): Context {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        saveLanguage(languageCode)
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            updateResourcesLocale(context, locale)
        } else {
            updateResourcesLocaleLegacy(context, locale)
        }
    }

    private fun updateResourcesLocaleLegacy(context: Context, locale: Locale): Context {
        val resources = context.resources
        val configuration = resources.configuration
        configuration.locale = locale
        configuration.setLayoutDirection(locale)
        resources.updateConfiguration(configuration, resources.displayMetrics)
        return context
    }

    @TargetApi(Build.VERSION_CODES.N)
    private fun updateResourcesLocale(context: Context, locale: Locale): Context {
        val configuration = context.resources.configuration
        configuration.setLocale(locale)
        configuration.setLayoutDirection(locale)
        return context.createConfigurationContext(configuration)
    }

    fun saveLanguage(value: String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(LANGUAGE, value)
        prefsEditor.apply()
    }

//    fun setLocale(context: Context, language: String?): Context? {
//        Log.i("here",language + "setLocale")
//
//        saveLanguage(language!!)
//        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            updateResources(context, language)
//        } else updateResourcesLegacy(context, language)
//    }
//
//
//    @TargetApi(Build.VERSION_CODES.N)
//    private fun updateResources(context: Context, language: String?): Context? {
//        val locale = Locale(language)
//        Locale.setDefault(locale)
//        val configuration: Configuration = context.getResources().getConfiguration()
//        configuration.setLocale(locale)
//        configuration.setLayoutDirection(locale)
//        return context.createConfigurationContext(configuration)
//    }
//
//    private fun updateResourcesLegacy(context: Context, language: String?): Context? {
//        val locale = Locale(language)
//        Locale.setDefault(locale)
//        val resources: Resources = context.getResources()
//        val configuration: Configuration = resources.getConfiguration()
//        configuration.locale = locale
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
//            configuration.setLayoutDirection(locale)
//        }
//        resources.updateConfiguration(configuration, resources.getDisplayMetrics())
//        return context
//    }

    fun loggedIn(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(LOGGED, value)
        prefsEditor.apply()
    }

    fun isLoggedIn(): String = prefs.getString(LOGGED, "false")!!


    fun getLanguage(): String = prefs.getString(LANGUAGE, Locale.getDefault().language)!!


    fun saveMobileCode(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(MOBILECODE, value)
        prefsEditor.apply()
    }

    fun getMobileCode(): String = prefs.getString(MOBILECODE, "+20")!!

    fun saveCountryCode(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(COUNTRYCODE, value)
        prefsEditor.apply()
    }

    fun convertCountryCode(value : String){
        var country_name = value
        when(value) {
            "Egypt" -> {
                country_name = "EG"
                saveCountryID("1")
            }
            "Turkey" -> {
                country_name = "TR"
                saveCountryID("2")
            }
            "Germany" -> {
                country_name = "DE"
                saveCountryID("4")
            }
            "Saudi Arabia" -> {
                country_name = "SA"
                saveCountryID("3")

            }
        }
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(COUNTRYCODE, country_name)
        prefsEditor.apply()
    }

    fun getCountryCode(): String = prefs.getString(COUNTRYCODE, "EG")!!

    fun saveCountryID(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(COUNTRYID, value)
        prefsEditor.apply()
    }

    fun getCountryID(): String = prefs.getString(COUNTRYID, "1")!!

    fun saveRegionID(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString("REGIONID", value)
        prefsEditor.apply()
    }

    fun getRegionID(): String = prefs.getString("REGIONID", "1")!!
    fun saveMobileCodeWithoutPlus(value: String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString("MobileCodeWithoutPlus", value)
        prefsEditor.apply()
    }
    fun getLocationID(): Int = prefs.getInt("location_id"+ getUserID(), -1)
    fun saveLocationID(value: Int?){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        if (value != null) {
            prefsEditor.putInt("location_id"+ getUserID(), value)
        }else{
            prefsEditor.remove("location_id"+ getUserID())
        }
        prefsEditor.apply()
    }
    fun removeLocationID(){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.remove("location_id"+ getUserID())
        prefsEditor.apply()
    }

    fun getMobileCodeWithoutPlus() : String = prefs.getString("MobileCodeWithoutPlus","20")!!

    fun saveGovernerateID(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString("GOVERNERATE", value)
        prefsEditor.apply()
    }

    fun getGovernerateID(): String = prefs.getString("GOVERNERATE", "1")!!

    fun saveAddress(value : String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(ADDRESS, value)
        prefsEditor.apply()
    }

    fun getAddress(): String = prefs.getString(ADDRESS, "NOT Assigned")!!

    fun getName() : String = prefs.getString(NAME,"")!!

    fun saveName(value: String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(NAME, value)
        prefsEditor.apply()
    }

    fun getShopName() : String = prefs.getString(SHOP,"")!!

    fun saveShopName(value: String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(SHOP, value)
        prefsEditor.apply()
    }

    fun saveEmail(email: String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(EMAIL, email)
        prefsEditor.apply()
    }

    fun getEmail() : String = prefs.getString(EMAIL,"")!!

    fun getUserPhoto(): String = prefs.getString(Constants.PHOTO,"")!!

    fun saveUserPhoto(value : String) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(Constants.PHOTO, value)
        prefsEditor.apply()
    }

    fun getPassword() : String = prefs.getString(NAME,"")!!

    fun savePassword(value: String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(PASSWORD, value)
        prefsEditor.apply()
    }

    fun <T> saveList(key: String?, list: List<T>?) {
        val gson = Gson()
        val json = gson.toJson(list)
        set(key, json)
    }

    operator fun set(key: String?, value: String?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(key, value)
        prefsEditor.apply()
    }

    fun <T> saveObject(key: String?, obj: T?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        val gson : Gson = Gson()
        val json = gson.toJson(obj)
        prefsEditor.putString(key, json ?: "EMPTYOBJECT")
        prefsEditor.apply()
    }

    fun getCurrentLatlng() : Latlng{
        val gson = Gson()
        val json: String? = prefs.getString(Constants.LATLNG,"")
        val obj: Latlng
        if(json.isNullOrEmpty()){
            val json = gson.toJson(Latlng("31.111111","31.222222"))
            obj = gson.fromJson(json, Latlng::class.java)
        }else{
            obj = gson.fromJson(json, Latlng::class.java)
        }
        return obj
    }

    fun getChoosenLatlng() : Latlng{
        val gson = Gson()
        val json: String? = prefs.getString(Constants.CHOOSEN_LATLNG,"")
        val obj: Latlng
        if(json.isNullOrEmpty()){
            val json = gson.toJson(Latlng("31.111111","31.222222"))
            obj = gson.fromJson(json, Latlng::class.java)
        }else{
            obj = gson.fromJson(json, Latlng::class.java)
        }
        return obj
    }

    fun getUser(): User {
        val gson = Gson()
        val json: String = prefs.getString("USER", "")!!
        val obj: User = gson.fromJson(json, User::class.java)
        return obj
    }

    fun getListOfCountries(): List<Countries>? {
        val arrayItems: List<Countries>?
        val serializedObject: String = prefs.getString("LISTCOUNTRIES", "")!!
        val gson = Gson()
        val type: Type = object : TypeToken<List<Countries>>() {}.getType()
        arrayItems = gson.fromJson(serializedObject, type)
        return arrayItems
    }

    fun getListOfLanguages(): List<String>? {
        val arrayItems: List<String>?
        val serializedObject: String = prefs.getString(Constants.langaueList, "")!!
        val gson = Gson()
        val type: Type = object : TypeToken<List<String>>() {}.getType()
        arrayItems = gson.fromJson<List<String>>(serializedObject, type)
        return arrayItems
    }


    fun getUserID(): Int? = prefs.getInt(USER_ID, -1)

    fun saveUserID(value: Int?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putInt(USER_ID, value!!)
        prefsEditor.apply()

    }

    fun getID(): Int? = prefs.getInt(ID, -1)

    fun saveID(value: Int?) {
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putInt(ID, value!!)
        prefsEditor.apply()

    }

    fun isTermsAccepted() : Boolean = prefs.getBoolean("terms", false)

    fun setTerms(value: Boolean){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putBoolean("terms", value)
        prefsEditor.apply()
    }
    fun getLoginState():String=prefs.getString(Constants.LOGIN_STATE, LoginStateEnum.Other.value)!!
    fun setLoginState(state:String){
        val prefsEditor: SharedPreferences.Editor = prefs.edit()
        prefsEditor.putString(Constants.LOGIN_STATE, state)
        prefsEditor.apply()
    }
}