package com.techcubics.albarkahyper.ui.adapters.order

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.denzcoskun.imageslider.constants.ScaleTypes
import com.denzcoskun.imageslider.models.SlideModel
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemOrderBinding
import com.techcubics.albarkahyper.ui.adapters.holders.order.OrdersHolderItem
import com.techcubics.albarkahyper.ui.adapters.profile.LocationsAdapter
import com.techcubics.data.model.pojo.Order
import com.techcubics.style.R



class OrdersAdapter(
    private val context: Context,
    private val listOfOrders: List<Order>,
    private val itemClickedLiveData: MutableLiveData<Order?>
):
    RecyclerView.Adapter<OrdersHolderItem>(){

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrdersHolderItem {

        val itemBinding = ItemOrderBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrdersHolderItem(itemBinding)
    }


    @SuppressLint("ResourceAsColor")
    override fun onBindViewHolder(holder: OrdersHolderItem, position: Int) {

        var productsname = ""
        for (names in listOfOrders.get(position).orderDetails){
            productsname += names.productName + "-"
        }
        productsname = productsname.substring(0,productsname.length - 1)
        holder.productName.text = productsname

        holder.orderno.text = listOfOrders.get(position).orderCode
        holder.total.text = listOfOrders.get(position).amount.toString()
        holder.status.text = listOfOrders.get(position).orderStatus
        holder.currencyName.text = context.getString(R.string.currency_name)

        when(listOfOrders.get(position).orderStatus){
            "pending" -> holder.status.setTextColor(context.resources.getColor(R.color.salmon))
            "onway" -> holder.status.setTextColor(context.resources.getColor(R.color.pyellow))
            "cancelled" -> holder.status.setTextColor(context.resources.getColor(R.color.pred))
            "delivered" -> holder.status.setTextColor(context.resources.getColor(R.color.pgreen))
            "completed" -> holder.status.setTextColor(context.resources.getColor(R.color.pgreen))

        }

        val imagelist =  ArrayList<SlideModel>()
        for (imgs in listOfOrders.get(position).orderDetails){
            imagelist.add(SlideModel(imgs.productIcon))
        }

        if(imagelist.size == 1){
            holder.ordersliderimg.visibility = View.GONE
            holder.imageView.visibility = View.VISIBLE
            imagelist.get(0).imageUrl?.let { Helper.loadImage(context, it,holder.imageView) }
        }else{
            holder.ordersliderimg.visibility = View.VISIBLE
            holder.imageView.visibility = View.GONE
            holder.ordersliderimg.setImageList(imagelist,ScaleTypes.FIT)
        }
        holder.itemView.setOnClickListener {
            Log.i("clothes","clicked"+listOfOrders.get(position))
            itemClickedLiveData.postValue(listOfOrders.get(position))
        }


    }

    override fun getItemCount() = listOfOrders.size

}