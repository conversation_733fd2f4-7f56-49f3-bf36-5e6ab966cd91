<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.views.home.navFragments.profile.fragments.updateprofile.UpdateProfileFragment">

        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_fragment"
            app:layout_constraintTop_toTopOf="parent"
            />
        <androidx.core.widget.NestedScrollView
            android:id="@+id/sc_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RadioGroup
                    android:id="@+id/group"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/toolbar"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    tools:ignore="NotSibling">

                    <RadioButton
                        android:id="@+id/pay_cash"
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:text="@string/pay_cash"
                        android:layout_marginTop="24dp"
                        android:theme="@style/label_paymentincart"
                        android:background="@drawable/et_style_with_borders"/>
                    <RadioButton
                        android:id="@+id/credit"
                        android:layout_marginTop="16dp"
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:text="@string/pay_credit"
                        android:clickable="false"
                        android:theme="@style/label_paymentincart"
                        android:drawableStart="@drawable/ic_coming_soon"
                        android:button="@color/transparent"
                        android:drawablePadding="10dp"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:background="@drawable/et_style_with_borders"/>
                    <RadioButton
                        android:id="@+id/vodafon"
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:theme="@style/label_paymentincart"
                        android:text="@string/pay_vodafon"
                        android:layout_marginTop="16dp"
                        android:clickable="false"
                        android:drawablePadding="10dp"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:drawableStart="@drawable/ic_coming_soon"
                        android:button="@color/transparent"
                        android:background="@drawable/et_style_with_borders"/>

                </RadioGroup>

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="100dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="55dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/group"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>




        </androidx.core.widget.NestedScrollView>




    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>