package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName


data class Order(

    @SerializedName("order_id") var orderId: Int? = null,
    @SerializedName("order_code") var orderCode: String? = null,
    @SerializedName("shop") var shop: ProductDetailsStore? = ProductDetailsStore(),
    @SerializedName("delivery_area") var deliveryArea: DeliveryArea? = DeliveryArea(),
    @SerializedName("location") var location: Location? = Location(),
    @SerializedName("delivery_price") var deliveryPrice: Float? = null,
    @SerializedName("order_status") var orderStatus: String? = null,
    @SerializedName("note") var note: String? = null,
    @SerializedName("status_pay") var statusPay: String? = null,
    @SerializedName("payment_method") var paymentMethod: String? = null,
    @SerializedName("coupon_code") var couponCode: String? = null,
    @SerializedName("coupon_price") var couponPrice: Float? = null,
    @SerializedName("total_price") var totalPrice: Float? = null,
    @SerializedName("amount") var amount: Float? = null,
    @SerializedName("date") var date: String? = null,
    @SerializedName("time") var time: String? = null,
    @SerializedName("day") var day: String? = null,
    @SerializedName("range_discount") var rangeDiscount: Float? = null,
    @SerializedName("range_discount_amount") var rangeDiscountAmount: Float? = null,
    @SerializedName("range_price_from") var rangePriceFrom: Float? = null,
    @SerializedName("range_price_to") var rangePriceTo: Float? = null,
    @SerializedName("format_date") var formatDate: String? = null,
    @SerializedName("special_order") var specialOrder: String? = null,
    @SerializedName("order_details") var orderDetails: ArrayList<OrderDetails> = arrayListOf()

)

data class OrderDetails(

    @SerializedName("order_detail_id") var orderDetailId: Int? = null,
    @SerializedName("model_id") var modelId: Int? = null,
    @SerializedName("model_type") var modelType: String? = null,
    @SerializedName("qty") var qty: Int? = null,
    @SerializedName("price") var price: Float? = null,
    @SerializedName("product_id") var productId: Int? = null,
    @SerializedName("product_name") var productName: String? = null,
    @SerializedName("product_icon") var productIcon: String? = null,
    @SerializedName("product_images") var productImages: ArrayList<ProductImages> = arrayListOf(),
    @SerializedName("products") var products: ArrayList<Products> = arrayListOf()

)

data class Products(

    @SerializedName("product_name") var productName: String? = null,
    @SerializedName("product_slug"   ) var productSlug   : String?                  = null,
    @SerializedName("product_qty") var productQty: String? = null,
    @SerializedName("product_icon") var productIcon: String? = null,
    @SerializedName("product_images") var productImages: ArrayList<ProductImages> = arrayListOf()

)

data class ProductItem(
     var productName: String? = null,
     var productIcon: String? = null,
     var productPrice : String? = null,
     var productQty: String? = null
)
data class ProductImages(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("path_id") var pathId: Int? = null,
    @SerializedName("path") var path: String? = null

)

data class DeliveryArea(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("delivery_area_id") var deliveryAreaId: Int? = null,
    @SerializedName("area_id") var areaId: Int? = null,
    @SerializedName("governorate") var governorate: Governerate? = null,
    @SerializedName("region") var region: ArrayList<Regoin>? = null,
    @SerializedName("average_time") var averageTime: String? = null,
    @SerializedName("min_time") var minTime: Int? = null,
    @SerializedName("max_time") var maxTime: Int? = null,
    @SerializedName("delivery_price") var deliveryPrice: Float? = null,
    @SerializedName("min_order_price") var minOrderPrice: Float? = null

)

data class Location(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("customer_id") var customerId: Int? = null,
    @SerializedName("first_name") var firstName: String? = null,
    @SerializedName("last_name") var lastName: String? = null,
    @SerializedName("phone") var phone: String? = null,
    @SerializedName("phone_verified") var phoneVerified: String? = null,
    @SerializedName("lat") var lat: Double? = null,
    @SerializedName("lng") var lng: Double? = null,
    @SerializedName("address") var address: String? = null,
    @SerializedName("country_id") var countryId: Int? = null,
    @SerializedName("governorate_id") var governorateId: Int? = null,
    @SerializedName("region_id") var regionId: Int? = null,
    @SerializedName("building_type") var buildingType: String? = null,
    @SerializedName("street") var street: String? = null,
    @SerializedName("building_number") var buildingNumber: String? = null,
    @SerializedName("floor_no") var floorNo: String? = null,
    @SerializedName("apartment_number") var apartmentNumber: String? = null,
    @SerializedName("notes") var notes: String? = null,
    @SerializedName("created_at") var createdAt: String? = null,
    @SerializedName("updated_at") var updatedAt: String? = null

)

