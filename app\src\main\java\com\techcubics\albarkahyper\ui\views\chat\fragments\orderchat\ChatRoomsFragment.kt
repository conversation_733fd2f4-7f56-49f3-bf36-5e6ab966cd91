package com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.model.pojo.OrderRoomsData
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentChatRoomsBinding
import com.techcubics.albarkahyper.ui.adapters.chat.ChatRoomsHistoryAdapter
import com.techcubics.albarkahyper.ui.views.chat.fragments.ChatTabsFragment
import com.techcubics.albarkahyper.ui.views.chat.fragments.ChatTabsFragmentDirections
import com.techcubics.albarkahyper.ui.views.chat.viewmodels.ChatViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

class ChatRoomsFragment : Fragment(), IPageRowset<OrderRoomsData>,
    IItemClickListener {

    private lateinit var binding: FragmentChatRoomsBinding
    private lateinit var chatRoomsHistoryAdapter: ChatRoomsHistoryAdapter<OrderRoomsData>
    private val chatViewModel by viewModel<ChatViewModel>()
    private val TAG = "ChatRoomsFragment"
    private lateinit var popupDialog: PopupDialog
    private lateinit var refreshClickListener: IItemClickListener

    companion object {
        val instance: ChatRoomsFragment = ChatRoomsFragment()
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentChatRoomsBinding.inflate(inflater, container, false)


        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {

        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        //
        chatViewModel.getOrderRoomsHistory()
        //
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.rvHistory.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
    }

    override fun observers() {

        chatViewModel.orderRoomsResponse.observe(viewLifecycleOwner, Observer {

//            binding.shimmerID.stopShimmer()
//            binding.shimmerID.visibility = View.GONE
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            try {
                if (it.status!!) {

                    if (it.data != null) {

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvHistory.visibility = View.VISIBLE

                        } else {

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })


    }

    override fun events() {

        refreshClickListener=object:IItemClickListener{
            override fun perform() {
                chatViewModel.getOrderRoomsHistory()
            }
        }
    }

    override fun showData(items: List<OrderRoomsData>) {
        Log.i("here", items.toString())


        chatRoomsHistoryAdapter = ChatRoomsHistoryAdapter<OrderRoomsData>(onItemClicked = this )
        chatRoomsHistoryAdapter.setItemsList(items)
        binding.rvHistory.adapter = chatRoomsHistoryAdapter
        binding.rvHistory.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)


    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    if(message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!){
                        CoroutineScope(Dispatchers.Main).launch {
                            binding.chatroomLayout.visibility = View.GONE
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
                        binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                        binding.placeholder.tvMessage.text = message
                    }
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onItemClick(id: Int, name: String,logo : String) {

        var action=ChatTabsFragmentDirections.chatTabsFragmentToOrderChatHistoryFragment(refreshClickListener,id,name)
        Navigation.findNavController(requireParentFragment().requireView()).navigate(action)

        /*var bundle = Bundle()
        bundle.putInt(Constants.INTENT_ID, id)
        bundle.putString(Constants.INTENT_NAME, name)
        findNavController().navigate(
            R.id.action_chatRoomsFragment_to_orderChatHistoryFragment,
            bundle
        )*/
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}