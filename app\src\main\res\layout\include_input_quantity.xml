<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/product_qty_cart_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/btn_delete"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/rect_quantity_controllers_disabled"
        android:contentDescription="@string/content_desc_decrease_button"
        android:paddingHorizontal="11dp"
        android:src="@drawable/ic_delete"
        android:visibility="invisible"
        app:tint="@color/app_color"
        android:scaleType="center"
        app:layout_constraintBottom_toBottomOf="@id/btn_decrease"
        app:layout_constraintEnd_toEndOf="@id/btn_decrease"
        app:layout_constraintStart_toStartOf="@id/btn_decrease"
        app:layout_constraintTop_toTopOf="@id/btn_decrease" />
    <ImageView
        android:id="@+id/btn_decrease"
        android:layout_width="0dp"
        android:layout_height="35dp"
        android:background="@drawable/rect_quantity_controllers"
        android:contentDescription="@string/content_desc_decrease_button"
        android:scaleType="centerInside"
        android:src="@drawable/ic_item_remove"
        app:layout_constraintBottom_toBottomOf="@id/product_quantity"
        app:layout_constraintEnd_toStartOf="@id/product_quantity"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/product_quantity"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/product_quantity"
        style="@style/input_value_quantity_style"
        android:layout_width="wrap_content"
        android:layout_height="35dp"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:text="1"
        android:background="@color/white"
        android:textAlignment="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_increase"
        app:layout_constraintStart_toEndOf="@id/btn_decrease"
        app:layout_constraintTop_toTopOf="parent" />
    <ImageView
        android:id="@+id/btn_increase"
        android:layout_width="0dp"
        android:layout_height="35dp"
        android:background="@drawable/btn_ripple_white"
        android:contentDescription="@string/content_desc_increase_button"
        android:scaleType="centerInside"
        android:src="@drawable/ic_item_add"
        app:layout_constraintBottom_toBottomOf="@id/product_quantity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/product_quantity"
        app:layout_constraintTop_toTopOf="@id/product_quantity"
        app:tint="@color/white" />

</androidx.constraintlayout.widget.ConstraintLayout>
