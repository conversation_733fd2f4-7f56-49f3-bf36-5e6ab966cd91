package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.ItemRateBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.RateHolderItem


class RatingsAdapter<T>(val vm: ViewModel): PagingAdapter() {

    lateinit var items:List<T>
    lateinit var itemBinding: ItemRateBinding

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RateHolderItem<T> {
        itemBinding = ItemRateBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return RateHolderItem(itemBinding,parent.context,vm)
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as RateHolderItem<T>
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_rate
    }

    override fun getPagingItemCount(): Int {
        return items.size
    }





}