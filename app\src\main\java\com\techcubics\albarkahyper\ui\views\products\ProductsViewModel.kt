package com.techcubics.albarkahyper.ui.views.products

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.ItemRemovingRequest
import com.techcubics.data.model.requests.SearchAreaRequest
import com.techcubics.data.model.requests.home.AddRemoveFavoriteRequest
import com.techcubics.data.model.requests.home.ProductByCategoryRequest
import com.techcubics.data.model.requests.home.ProductColorsSizesRequest
import com.techcubics.data.model.requests.home.ProductSearchRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.favourite.FavoritesRepo
import com.techcubics.data.repos.home.profile.ProfileRepo
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.shared.enums.FavoriteTypesEnum
import com.techcubics.shared.enums.SortTypesEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ProductsViewModel(
    private val productsRepo: ProductsRepo,
    private val favoritesRepo: FavoritesRepo,
    private val sharedPreferencesManager: SharedPreferencesManager,
    private val profileRepo: ProfileRepo
) : ViewModel() {
    val governoratesLiveData = MutableLiveData<BaseResponse<ArrayList<Governerate>>?>()
    val regionsLiveData = MutableLiveData<BaseResponse<ArrayList<Regoin>>?>()

    val productsCategoriesResponse = MutableLiveData<BaseResponse<MutableList<ProductDetailsDto>>?>()
    val productSearchResponse = MutableLiveData<BaseResponse<List<ProductSearchData>>>()
    val addRemoveFavoriteResponse = MutableLiveData<BaseResponse<String>>()
    val productColorsResponse = MutableLiveData<BaseResponse<MutableList<ColorObject>>>()
    val productColorsSizesResponse = MutableLiveData<BaseResponse<ProductColorsSizesData>>()
    val addCartResponse: MutableLiveData<BaseResponse<CartData>> by lazy {
        MutableLiveData()
    }
    val getCartResponse: MutableLiveData<BaseResponse<ArrayList<CartData>>?> by lazy {
        MutableLiveData()
    }
    val bannersResponse = MutableLiveData<BaseResponse<MutableList<BannerData>>>()
    val mainCategoriesResponse = MutableLiveData<BaseResponse<MutableList<Category>>>()
    val subCategoriesResponse = MutableLiveData<BaseResponse<MutableList<SubCategory>>>()
    val mostWantedProductsResponse = MutableLiveData<BaseResponse<MutableList<ProductDetailsDto>>?>()
    val storeDiscountsResponse = MutableLiveData<BaseResponse<MutableList<ProductDetailsDto>>?>()

    private  val TAG = "ProductsViewModel"

    private val products= mutableListOf<ProductDetailsDto>()
    private fun appendOnProducts(request: BaseResponse<MutableList<ProductDetailsDto>>){
        if (!products.containsAll(request.data!!)) {
            products.addAll(request.data!!)
            request.data=products
            productsCategoriesResponse.postValue(request)
        }
    }
    fun updateProduct(product: ProductDetailsDto){
        products.updateItem({ p -> p.id == product.id }) { product }
        productsCategoriesResponse.value?.data = products
        productsCategoriesResponse.postValue(productsCategoriesResponse.value)
    }
    fun getProductsByCategory(id: Int, branch_type_id: Int, storeID: Int, page: Int) {
        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()
        if (page==1){

            products.clear()
        }
        viewModelScope.launch {
            val request = ProductByCategoryRequest(SortTypesEnum.TopDown, countryID, id,branch_type_id,storeID,page)
            val rs = productsRepo.getProductsByCategory(request)
            if(rs!=null&&!rs.data.isNullOrEmpty()) {
                appendOnProducts(rs)
            }else {
                productsCategoriesResponse.postValue(rs)
            }
        }

    }

    fun getProductsBySubCategory(id: Int, branch_type_id: Int, storeID: Int, page: Int) {
        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()
        if (page==1){
            products.clear()
        }
        viewModelScope.launch {
            val request = ProductByCategoryRequest(SortTypesEnum.TopDown, countryID, id,branch_type_id,storeID,page)
            val rs = productsRepo.getProductsBySubCategory(request)
            if(rs!=null&&!rs.data.isNullOrEmpty()) {
                appendOnProducts(rs)
            }else {
                productsCategoriesResponse.postValue(rs)
            }
        }
    }

    fun addRemoveFav(id: Int) {
        viewModelScope.launch {
            val request = AddRemoveFavoriteRequest(type = FavoriteTypesEnum.Product.value, id = id)
            val rs = favoritesRepo.addRemoveFav(request)
            addRemoveFavoriteResponse.postValue(rs!!)
        }

    }

    fun getProductsByWordFilter(
        word: String?,
        colors: ArrayList<String>? = null,
        sizes: ArrayList<String>? = null,
        categories: ArrayList<Int>? = null,
        start: Int? = null,
        to: Int? = null,
        page : Int,
        latlng: Latlng?=null,
        searchAreaRequest: SearchAreaRequest?=null,
        price : String? = null
    ) {
        Log.i("search", "viewModel before")

        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request = ProductSearchRequest(
                country_id = countryID,
                key_search = word,
                colors = colors?.toTypedArray(),
                sizes = sizes?.toTypedArray(),
                category_id = categories?.toTypedArray(),
                price_start = start,
                price_end = to,
                page = page,
                governorateId = searchAreaRequest?.govId,
                regionId = searchAreaRequest?.regionId,
                lat = latlng?.lat,
                lng = latlng?.lng,
                price = price
            )
            val rs = productsRepo.getProductsFilter(request)
            productSearchResponse.postValue(rs!!)
        }

    }


    fun getProductsByStore(
        word: String? = null,
        storeID: Int,
        catIDList:Array<Int>?=null,
        page: Int
    ) {


        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()

        CoroutineScope(Dispatchers.IO).launch {
            val request = ProductSearchRequest(
                country_id = countryID,
                page = page,
                key_search = word?.ifEmpty{null},
                shop_id = storeID,
                category_id = catIDList?.ifEmpty { null }
            )
            val rs = productsRepo.getProductsFilter(request)
            productSearchResponse.postValue(rs!!)
        }

    }

    fun getColors() {
        viewModelScope.launch {
            val rs = productsRepo.getColors()
            productColorsResponse.postValue(rs!!)
        }

    }

    fun getColorsSizes() {
        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request = ProductColorsSizesRequest(countryID = countryID)
            val rs = productsRepo.getColorsSizes(request)
            productColorsSizesResponse.postValue(rs!!)
        }

    }

    fun addToCart(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId, modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }
    fun addToCartForSectionProducts(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    )
            : MutableLiveData<BaseResponse<CartData>> {
        val addCartResponse = MutableLiveData<BaseResponse<CartData>>()
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId, modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
        return addCartResponse
    }

    fun getBanners() {

        viewModelScope.launch {
            val rs = productsRepo.getBanners()
            bannersResponse.postValue(rs!!)
        }

    }

    fun getHomeBanner() {
        viewModelScope.launch {
            val rs = productsRepo.getHomeBanner()
            bannersResponse.postValue(rs!!)
        }

    }

    fun getMainCategories(branchTypeID: Int) {

        viewModelScope.launch {
            val rs = productsRepo.getBranchTypesCategories(branchTypeID = branchTypeID)
            mainCategoriesResponse.postValue(rs!!)
        }

    }
    fun getMainCategoriesByShopId(shopId: Int) {

        viewModelScope.launch {
            val rs = productsRepo.getCategoriesByShopId(shopId)
            mainCategoriesResponse.postValue(rs!!)
        }

    }
    fun getSubCategoriesByCatId(catId: Int) {

        viewModelScope.launch {
            val rs = productsRepo.getSubCategoriesByCatId(catId)
            subCategoriesResponse.postValue(rs!!)
        }

    }

    fun getGovernorates(countryID: Int) {
        viewModelScope.launch {
            val request = profileRepo.getGovernerateByCountryId(countryID)
            governoratesLiveData.postValue(request)
        }
    }

    fun getRegions(countryID: Int, govId: Int) {
        viewModelScope.launch {
            val request = profileRepo.getRegionBygovernorateId(govId, countryID)
            regionsLiveData.postValue(request)
        }
    }
    val cartItemRemovedResponse = MutableLiveData<BaseResponse<CartData>>()

    fun removeCartItem(modelType: String?, modelId: Int?) {
        viewModelScope.launch {
            val request = ItemRemovingRequest(modelId, modelType)
            val rs = productsRepo.removeCartItem( request)
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
    }
    fun removeCartItemForSectionProducts(modelType: String?, modelId: Int?)
            : MutableLiveData<BaseResponse<CartData>> {
        val cartItemRemovedResponse = MutableLiveData<BaseResponse<CartData>>()
        viewModelScope.launch {
            val request = ItemRemovingRequest(modelId, modelType)
            val rs = productsRepo.removeCartItem(request)
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
        return cartItemRemovedResponse
    }

    fun getCart() {
        viewModelScope.launch {
            val rs = productsRepo.getCart()
            rs?.let { getCartResponse.postValue(it) }
        }
    }
    private val mostWantedList= mutableListOf<ProductDetailsDto>()

    fun getMostWantedProducts(store: Int, category_id: Int, page: Int) {
        if (page==1){
            mostWantedList.clear()
        }
        viewModelScope.launch {
            val request = productsRepo.getMostWantedProducts(store,category_id,page)
            if(request!=null&&!request.data.isNullOrEmpty()) {
                appendOnMostWanted(request)
            }else {
                mostWantedProductsResponse.postValue(request)
            }
        }
    }
    private fun appendOnMostWanted(request: BaseResponse<MutableList<ProductDetailsDto>>){
        if (!mostWantedList.containsAll(request.data!!)) {
            mostWantedList.addAll(request.data!!)
            request.data=mostWantedList
            mostWantedProductsResponse.postValue(request)
        }
    }

private val discounts= mutableListOf<ProductDetailsDto>()
    fun getStoreDiscounts(store: Int, page: Int) {
        if (page==1){
            discounts.clear()
        }
        viewModelScope.launch {
            val request = productsRepo.getStoreDiscounts(store,page)
            if(request!=null&&!request.data.isNullOrEmpty()) {
                appendOnDiscounts(request)
            }else {
                storeDiscountsResponse.postValue(request)
            }
        }
    }
    private fun appendOnDiscounts(request: BaseResponse<MutableList<ProductDetailsDto>>){
        if (!discounts.containsAll(request.data!!)) {
            discounts.addAll(request.data!!)
            request.data=discounts
            storeDiscountsResponse.postValue(request)
        }
    }

}
fun <T> MutableList<T>.updateItem(condition: (T) -> Boolean,update: (T) -> T) {
    val index = indexOfFirst(condition)
    if (index != -1) {
        val updatedItem = update(get(index))
        set(index, updatedItem)
    }
}