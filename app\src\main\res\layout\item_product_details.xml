<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ad_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="visible"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TableRow
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:weightSum="3">

            <TextView
                android:id="@+id/product_name"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:textSize="10sp"
                android:singleLine="true"
                android:background="@drawable/et_style_with_borders"
                android:padding="5dp"
                android:gravity="center"
                android:text="fffffffffffffffffffffffffffffffffffffffffff"
                android:textAppearance="@style/label_value_orderdetails" />

            <TextView
                android:id="@+id/product_price"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.5"
                android:singleLine="true"
                android:background="@drawable/et_style_with_borders"
                android:gravity="center"
                android:textSize="10sp"
                android:padding="5dp"
                android:text="20"
                android:textAppearance="@style/label_value_orderdetails" />

            <TextView
                android:id="@+id/product_qty"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.5"
                android:singleLine="true"
                android:background="@drawable/et_style_with_borders"
                android:gravity="center"
                android:textSize="10sp"
                android:padding="5dp"
                android:text="20"
                android:textAppearance="@style/label_value_orderdetails" />


                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:gravity="center"
                    android:id="@+id/product_image"
                    android:layout_gravity="center"
                    android:padding="10dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/portrait_placeholder" />

        </TableRow>
    </TableLayout>
</LinearLayout>
