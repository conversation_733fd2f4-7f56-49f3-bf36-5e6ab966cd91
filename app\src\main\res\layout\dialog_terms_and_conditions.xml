<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_popup_dialog"
    android:minWidth="300dp">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">
        <FrameLayout
            android:id="@+id/webview"
            android:paddingBottom="70dp"
            android:paddingHorizontal="12dp"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <WebView
                android:id="@+id/terms_conditions_webview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </FrameLayout>
        <include
            android:id="@+id/sign_btn_progress"
            layout="@layout/btn_progress"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"/>
    </FrameLayout>

</LinearLayout>