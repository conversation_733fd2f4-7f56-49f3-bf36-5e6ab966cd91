<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/product_details_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingTop="10dp">

    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
        android:id="@+id/dots_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:dotsColor="@color/color_gray_22"
        app:dotsCornerRadius="8dp"
        app:dotsSize="16dp"
        app:dotsSpacing="4dp"
        app:dotsWidthFactor="2.5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progressMode="false"
        app:selectedDotColor="@color/color_gray_2" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="5dp"
        android:textAlignment="viewStart"
        android:theme="@style/product_details_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dots_indicator"
        tools:text="لاب توب" />


    <LinearLayout
        android:id="@+id/min_qty_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/max_qty_cont"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <TextView
            style="@style/label_item_product_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/min_num" />

        <TextView
            android:id="@+id/min_qty"
            style="@style/label_item_product_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/app_color"
            tools:text="5" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/max_qty_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/priceLayout"
        app:layout_constraintTop_toBottomOf="@+id/min_qty_cont">

        <TextView
            style="@style/label_item_product_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/max_num_title" />

        <TextView
            android:id="@+id/max_qty"
            style="@style/label_item_product_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:textColor="@color/app_color"
            tools:text="20" />
    </LinearLayout>


    <FrameLayout
        android:id="@+id/priceLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/max_qty_cont">

        <TextView
            android:id="@+id/tv_price"
            style="@style/product_details_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            tools:text="41.99 جنيه" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/priceDiscountLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/tvBeforeDiscountPrice"
                style="@style/label_item_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/line_diagonal"
                app:layout_constraintBottom_toBottomOf="@+id/tvAfterDiscountPrice"
                app:layout_constraintStart_toEndOf="@+id/tvAfterDiscountPrice"
                app:layout_constraintTop_toTopOf="@+id/tvAfterDiscountPrice"
                tools:text="50.99 جنيه" />

            <TextView
                android:id="@+id/tvAfterDiscountPrice"
                style="@style/product_details_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="41.99 جنيه" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>


    <RatingBar
        android:id="@+id/rating"
        style="@style/Widget.AppCompat.RatingBar.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:numStars="5"
        android:stepSize="0.1"
        android:theme="@style/ratingbar"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/priceLayout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rating_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/rating"
        app:layout_constraintStart_toEndOf="@id/rating"
        app:layout_constraintTop_toTopOf="@id/rating">

        <TextView
            android:id="@+id/rate_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:text="@string/rate_title"
            android:textAppearance="@style/product_details_rating_title"
            app:layout_constraintEnd_toStartOf="@id/tv_rating"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/product_details_rating_title"
            app:layout_constraintBottom_toBottomOf="@id/rate_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/rate_title"
            tools:text="4" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/branch_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rating">

        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/gallery_img"
            android:layout_width="@dimen/icons_size_height_weight_6"
            android:layout_height="@dimen/icons_size_height_weight_6"
            android:layout_gravity="center_vertical"
            android:src="@drawable/portrait_welcome_img"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/gallery_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAlignment="viewStart"
            android:theme="@style/product_details_store_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/gallery_img"
            app:layout_constraintTop_toTopOf="@id/gallery_img"
            tools:text="Branch name" />

        <TextView
            android:id="@+id/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:theme="@style/product_details_store_desc"
            app:layout_constraintBottom_toBottomOf="@id/gallery_img"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/gallery_name"
            app:layout_constraintTop_toBottomOf="@id/gallery_name"
            tools:text="desc of shop" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/category"
        layout="@layout/item_sub_category"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/branch_container" />

    <View
        android:id="@+id/product_details_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/category" />

    <TextView
        android:id="@+id/product_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/product_details"
        android:theme="@style/product_details_colors_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/product_details_divider" />

    <TextView
        android:id="@+id/product_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:theme="@style/product_details_description"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/product_title"
        tools:text="لاب توب لينوفو V14 82C6006GED AMD 3020E - ذاكرة 4 رام جيجابايت - هارد 1 تيرابايت اتش دي دي - كارت شاشة ايه ام دي راديون - شاشة 14 بوصة HD عالية الدقة 220 مضادة للتوهج دي اوه اس - لون رمادي ايرون" />

    <View
        android:id="@+id/cart_order_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/product_details" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quantity_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_cart_quantity"
        android:padding="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cart_order_divider">

        <TextView
            android:id="@+id/price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            android:theme="@style/product_details_price"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="66 EGP" />

        <androidx.cardview.widget.CardView
            android:id="@+id/qty_cont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            app:cardCornerRadius="6dp"
            app:layout_constraintBottom_toBottomOf="@id/price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/price"
            app:layout_constraintTop_toTopOf="@id/price">

            <include
                android:id="@+id/product_qty_cart_container"
                layout="@layout/include_input_quantity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/min_order_note"
        style="@style/label_item_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/min_order_number"
        android:textColor="@color/color_red_3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/quantity_cont" />

    <TextView
        android:id="@+id/colors_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/available_colors"
        android:theme="@style/product_details_colors_title"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/min_order_note" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/colors_recycler_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/colors_title"
        app:layout_constraintTop_toBottomOf="@id/colors_title"
        tools:itemCount="5"
        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_product_color"
        tools:orientation="horizontal" />

    <include
        android:id="@+id/include_size_section"
        layout="@layout/include_size_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/sizes"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/colors_recycler_view" />


    <include
        android:id="@+id/btn_add_to_cart"
        layout="@layout/btn_progress"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_size_section" />

    <View
        android:id="@+id/ratings_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/btn_add_to_cart" />

    <TextView
        android:id="@+id/rate_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:theme="@style/product_details_rating_count"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ratings_divider"
        tools:text="1234 rates" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/review_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rate_count">

        <TextView
            android:id="@+id/your_rate_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/your_rating_title"
            android:theme="@style/product_details_my_rating_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RatingBar
            android:id="@+id/your_rating"
            style="@style/Widget.AppCompat.RatingBar.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:isIndicator="false"
            android:numStars="5"
            android:stepSize="1"
            android:theme="@style/ratingbar"
            app:layout_constraintBottom_toBottomOf="@id/your_rate_title"
            app:layout_constraintStart_toEndOf="@id/your_rate_title"
            app:layout_constraintTop_toTopOf="@id/your_rate_title" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:drawablePadding="4dp"
            android:text="@string/rate_title"
            android:theme="@style/product_details_my_rating_title"
            app:layout_constraintBottom_toBottomOf="@id/your_rating"
            app:layout_constraintStart_toEndOf="@id/your_rating"
            app:layout_constraintTop_toTopOf="@id/your_rating" />

        <EditText
            android:id="@+id/comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_round_light_stroke"
            android:gravity="top|start"
            android:hint="@string/share_your_rate_hint"
            android:inputType="textMultiLine"
            android:minLines="5"
            android:padding="8dp"
            android:theme="@style/product_details_share_rate_hint"
            app:layout_constraintTop_toBottomOf="@id/your_rating" />

        <include
            android:id="@+id/btn_review"
            layout="@layout/btn_progress"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="10dp"
            app:layout_constraintTop_toBottomOf="@id/comment" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/reviews_title"
        style="@style/product_details_rating_list_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/ratings"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/review_container" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/reviews_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/reviews_title"
        tools:itemCount="2"
        tools:listitem="@layout/item_rate" />

    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/reviews_recycler_view" />


</androidx.constraintlayout.widget.ConstraintLayout>
