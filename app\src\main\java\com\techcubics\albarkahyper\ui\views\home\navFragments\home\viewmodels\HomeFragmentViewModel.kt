package com.techcubics.albarkahyper.ui.views.home.navFragments.home.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.NearbyStoresCallRequest
import com.techcubics.data.model.requests.UpdateFireBaseTokenCallRequest
import com.techcubics.data.model.requests.home.HomeRequest
import com.techcubics.data.model.requests.home.NearBranchTypeRequest
import com.techcubics.data.model.requests.home.NearByStoresRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.HomeRepo
import com.techcubics.data.repos.home.HomeRepoImpl
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.data.repos.product.ProductsRepoImpl
import com.techcubics.data.repos.store.StoresRepo
import com.techcubics.data.repos.store.StoresRepoImpl
import kotlinx.coroutines.launch


class HomeFragmentViewModel(private val homeRepository : HomeRepo,
                            private val storesRepository: StoresRepo,
                            private val productsRepo: ProductsRepo,
                            private val sharedPreferencesManager: SharedPreferencesManager ): ViewModel() {

    val homeResponse=MutableLiveData<BaseResponse<HomeData>>()
    val nearByResponse=MutableLiveData<BaseResponse<MutableList<StoresNearbyData>>>()
    val updateFirebaseTokenResponse=MutableLiveData<BaseResponse<String>>()
    val nearStoreTypesResponse=MutableLiveData<BaseResponse<NearStoreTypes>>()
    val addCartResponse : MutableLiveData<BaseResponse<CartData>> by lazy {
        MutableLiveData()
    }
    var oldCountryId = sharedPreferencesManager.getCountryID()
    //val homeRepository= HomeRepoImpl()
    //val storesRepository= StoresRepoImpl()
    //val productsRepo = ProductsRepoImpl()
    fun getHome(){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= HomeRequest(countryID =  countryID)
            val rs=homeRepository.getHome(request)
            homeResponse.postValue(rs!!)


        }

    }

    fun getNearBy(latitude:String,longitude:String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= NearbyStoresCallRequest(latitude=latitude,longitude=longitude, country_id = countryID)
            val rs=storesRepository.getNearby(request)
            if(rs!=null) {
                nearByResponse.postValue(rs!!)
            }
        }

    }

    fun getNearStoreTypes(latitude: String,longitude: String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= NearBranchTypeRequest(latitude,longitude)
            val rs=homeRepository.getNearStoreTypes(request)
            nearStoreTypesResponse.postValue(rs!!)


        }

    }

    fun updateFCMToken(token:String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= UpdateFireBaseTokenCallRequest(country_id =countryID,token=token,device="android")
            val rs=homeRepository.updateFCMToken(request)
            updateFirebaseTokenResponse.postValue(rs!!)

        }

    }
    fun addToCart(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId,modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint,request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }
}