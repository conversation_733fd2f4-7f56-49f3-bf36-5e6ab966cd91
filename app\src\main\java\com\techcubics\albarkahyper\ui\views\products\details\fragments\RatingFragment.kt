package com.techcubics.albarkahyper.ui.views.products.details.fragments

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.model.pojo.Rates
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.FragmentRatingBinding
import com.techcubics.albarkahyper.ui.adapters.product.RatesAdapter

class RatingFragment : Fragment() {
    private var _binding: FragmentRatingBinding? = null
    private val binding get() = _binding!!
    private lateinit var ratesAdapter: RatesAdapter
    private val rates:ArrayList<Rates> = arrayListOf()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRatingBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val bundle = arguments ?: return
        val args = RatingFragmentArgs.fromBundle(bundle)
        setRatesRecyclerView()
        Helper.exitFullScreen(requireContext())
        rates.addAll( args.rates.toList())
        setRates(rates)
        setToolbar()
    }

    private fun setToolbar() {
        binding.toolbarName.tvTitle.text = getString(com.techcubics.style.R.string.ratings)
        binding.toolbarName.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun setRatesRecyclerView() {
        ratesAdapter = RatesAdapter(requireContext(), listOf())
        binding.reviewsRecyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.reviewsRecyclerView.adapter = ratesAdapter
    }
    private fun setRates(rates: ArrayList<Rates>?) {
        if ((rates?.size ?: 0) > 0) {
            binding.reviewsRecyclerView.visibility = View.VISIBLE
            binding.placeholder.root.visibility = View.GONE
            ratesAdapter.updateRates(rates ?: listOf())
        } else {
            binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty_no_comments)
            binding.placeholder.tvMessage.text =
                getString(com.techcubics.style.R.string.message_empty_list_nocomments)
            binding.reviewsRecyclerView.visibility = View.GONE
            binding.placeholder.root.visibility = View.VISIBLE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Helper.hideSystemUIWithNavigation(requireActivity().window)
        _binding = null
    }
}