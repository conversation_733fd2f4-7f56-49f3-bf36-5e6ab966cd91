<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="20dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:background="?attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout5"
        android:layout_width="match_parent"
        android:layout_height="320dp"
        android:background="@drawable/bg_round_gray"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgThumb"
            android:layout_width="match_parent"
            android:layout_height="170dp"
            android:background="@null"
            android:scaleType="centerCrop"
            tools:src="@drawable/portrait_placeholder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>


        <TextView
            android:id="@+id/tvTitle"
            style="@style/label_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_round_title"
            android:maxLines="2"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/imgThumb"
            tools:text="TitleTitleTitleTitleTitleTitleTitle" />



        <TextView
            android:id="@+id/tvDescription"
            style="@style/label_item_product_description"
            android:layout_width="0dp"
            android:layout_height="70dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imgThumb"
            android:maxLines="3"
            android:ellipsize="end"
            tools:text="testtesttesttetesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/price_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_price"
            android:padding="8dp"
            app:layout_constraintBottom_toBottomOf="@+id/imgThumb"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">

            <TextView
                android:id="@+id/tvOldPrice"
                style="@style/label_item_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:background="@drawable/line_diagonal"
                app:layout_constraintStart_toEndOf="@+id/tvCurrentPrice"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="50.99 جنيه" />

            <TextView
                android:id="@+id/tvCurrentPrice"

                style="@style/label_item_price_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"


                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/tvPercentage"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvOldPrice"
                app:layout_constraintVertical_bias="0.77"
                tools:text="41.99 جنيه" />

            <ImageView
                android:id="@+id/imageView5"
                android:layout_width="@dimen/icons_size_height_weight_10"
                android:layout_height="@dimen/icons_size_height_weight_10"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_red_label"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/tvPercentage"
                app:layout_constraintStart_toStartOf="@+id/tvCurrentPrice"
                app:layout_constraintTop_toTopOf="@+id/tvPercentage" />

            <TextView
                android:id="@+id/tvPercentage"
                style="@style/label_item_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/tvOldPrice"
                app:layout_constraintStart_toStartOf="@+id/tvOldPrice"
                app:layout_constraintTop_toBottomOf="@+id/tvOldPrice"
                tools:text="30 %" />

        </androidx.constraintlayout.widget.ConstraintLayout>



        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDescription">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/branch_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <com.mikhaellopez.circularimageview.CircularImageView
                    android:id="@+id/logo"
                    android:layout_width="@dimen/icons_size_height_weight_6"
                    android:layout_height="@dimen/icons_size_height_weight_6"
                    android:scaleType="centerInside"
                    app:civ_border_color="@color/gray"
                    app:civ_border_width="1dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvBranchName"
                    style="@style/label_item_store_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:ellipsize="end"
                    android:maxLines="1"

                    app:layout_constraintBottom_toBottomOf="@+id/logo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/logo"
                    app:layout_constraintTop_toTopOf="@+id/logo"
                    tools:text="branch name" />

                <include
                    android:id="@+id/btnOrder"
                    layout="@layout/btn_progress_circle"
                    android:layout_width="wrap_content"
                    android:layout_height="45dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.appcompat.widget.LinearLayoutCompat>




    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>