<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_1"
    tools:context=".ui.views.products.searchResult.ProductsFilterFragment">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbarFilter"
            layout="@layout/toolbar_fragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include
                    android:id="@+id/loading_animation"
                    layout="@layout/include_page_loading_animation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone" />


                <include
                    android:id="@+id/include_category_section"
                    layout="@layout/include_category_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp" />

                <include
                    android:id="@+id/include_color_section"
                    layout="@layout/include_color_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:visibility="gone"
                    android:layout_marginBottom="10dp" />

                <include
                    android:id="@+id/include_size_section"
                    layout="@layout/include_size_choosed_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:visibility="gone"
                    android:layout_marginBottom="10dp" />
                <include
                    android:id="@+id/include_places_search_section"
                    layout="@layout/include_places_search_section"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp" />


                <include
                    android:id="@+id/include_filter_price_section"
                    layout="@layout/include_filter_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="horizontal">

                    <!--                    <Button-->
                    <!--                        android:id="@+id/btnSearch"-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        -->
                    <!--                        android:background="@drawable/btn_ripple_normal_gray"-->
                    <!--                        android:text="@string/accept"-->
                    <!--                        android:theme="@style/button_accept"-->
                    <!--                         />-->
                    <include
                        android:id="@+id/btn_filter"
                        layout="@layout/btn_progress"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginVertical="20dp"
                        android:layout_marginEnd="8dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@+id/guideline3"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <Button
                        android:id="@+id/btnClear"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginVertical="20dp"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/bg_btn_notfilled_ripple"
                        android:text="@string/re_enter"
                        android:theme="@style/button_clear"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/guideline3"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.5" />


                </androidx.constraintlayout.widget.ConstraintLayout>


            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.appcompat.widget.LinearLayoutCompat>


</RelativeLayout>