package com.techcubics.albarkahyper.ui.adapters.holders.chat

import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.LiveChatRoomData
import com.techcubics.data.model.pojo.OrderRoomsData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IItemClickListener
import com.techcubics.albarkahyper.databinding.ItemChatRoomBinding
import org.koin.java.KoinJavaComponent
import org.koin.java.KoinJavaComponent.inject


class ChatRoomHolderItem <T>(val binding: ItemChatRoomBinding, val context: Context,val onItemClicked: IItemClickListener?): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "ChatRoomHolderItem"
    private lateinit var intent: Intent
    private val sharedPreferencesManager: SharedPreferencesManager by inject(SharedPreferencesManager::class.java)


    fun bind(data:T){

        when(data){
            is OrderRoomsData -> {
                fillByOrder(data)
            }
            is LiveChatRoomData -> {
                fillByLiveChatRoom(data)
            }

        }
    }

    private  fun fillByOrder(room: OrderRoomsData){



        binding.tvTimeDate.text=room.lastMessageTime

        binding.tvOrderId.text = room.order_code

        binding.tvShortMessage.text=room.last_message

        Log.d(TAG, "fillByOrder: ${room.count_unread}")

        if(room.count_unread > 0){
            val messagesCount = room.count_unread
            binding.tvUnReadNo.text= messagesCount.toString()
            binding.tvUnReadNo.visibility= View.VISIBLE
        }else{
            binding.tvUnReadNo.visibility= View.GONE
        }
        if(sharedPreferencesManager.getID()==room.sender_id){

            binding.tvName.text=room.furniture.name
            Helper.loadImage(context,room.furniture.logo,binding.photo)

        }else{
            binding.tvName.text=room.furniture.name
            Helper.loadImage(context,room.furniture.logo,binding.photo)
        }
        binding.root.setOnClickListener {
            onItemClicked?.onItemClick(room.order_id,room.furniture.name,"")

        }
    }

    private fun fillByLiveChatRoom(data: LiveChatRoomData) {

        binding.tvName.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            setMargins(0, 50, 0, 0)
        }

        binding.tvTimeDate.text=data.lastMessageTime

        binding.tvShortMessage.text=data.lastMessage

        if(data.chatsUnSeen!! > 0){
            val messagesCount = data.chatsUnSeen
            binding.tvUnReadNo.text= messagesCount.toString()
        }else{
            binding.tvUnReadNo.visibility= View.GONE
        }
        if(sharedPreferencesManager.getID()==data.senderInfo?.userId){

            binding.tvName.text=data.receiverInfo?.name
            Helper.loadImage(context,data.receiverInfo?.logo!!,binding.photo)

        }else{
            binding.tvName.text=data.receiverInfo?.name
            Helper.loadImage(context,data.receiverInfo?.logo!!,binding.photo)
        }
        binding.root.setOnClickListener {
            onItemClicked?.onItemClick(data.receiverInfo?.clinicId!!,data.receiverInfo?.name!!,data.receiverInfo?.logo!!)

        }
    }


}