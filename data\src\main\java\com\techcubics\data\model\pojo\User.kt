package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class User(
    @SerializedName("name")
    val name: String,
    @SerializedName("email")
    val email: String,
    @SerializedName("phone")
    val phone: String,
    @SerializedName("avatar")
    val avatar: String?,
    @SerializedName("address")
    val address: String,
    @SerializedName("gender")
    val gender: String,
    @SerializedName("birthday")
    val birthday: String,
    @SerializedName("age")
    val age: String,
    @SerializedName("id")
    val id: Int,
    @SerializedName("user_id")
    val userID: Int,
    @SerializedName("token")
    val token: String,
    @SerializedName("country") val country: Country? = null,
    @SerializedName("governorate") val governorate: Governerate? = null,
    @SerializedName("region") val region: Regoin? = null,
    @SerializedName("total_of_unread_orders_chats")  val unreadOrderChats : Int? = null,
    @SerializedName("total_of_unread_support_chats") val unreadSupportChats : Int? = null,
    @SerializedName("chats_un_seen") val unseenChats : Int? = null,
    @SerializedName("facility_name") val facilityName : String? = null,
    @SerializedName("branch_type") val branchType : String? = null,
    @SerializedName("lat") val lat : String? = null,
    @SerializedName("lng") val lng : String? = null,
    )

data class SocialUser(
    val name: String?,
    val email: String?,
    val image : String?
)

