package com.techcubics.data.repos.home

import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.NotificationRequest
import com.techcubics.data.model.requests.UpdateFireBaseTokenCallRequest
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.remote.BaseResponse

interface HomeRepo {

    suspend fun getHome(request: HomeRequest): BaseResponse<HomeData>?

    suspend fun getCategories():BaseResponse<MutableList<Category>>?

    suspend fun getOffers(offerRequest: OffersRequest): BaseResponse<MutableList<Offer>>?

    suspend fun getSaves(saveRequest: SavesRequest): BaseResponse<MutableList<Save>>?

    //suspend fun getDiscounts(discountsRequest: DiscountRequest): BaseResponse<MutableList<Discount>>?

    suspend fun updateFCMToken(request: UpdateFireBaseTokenCallRequest): BaseResponse<String>?

    suspend fun getNotifications(request: NotificationRequest):BaseResponse<MutableList<Notification>>?

    suspend fun getNearStoreTypes(request:NearBranchTypeRequest): BaseResponse<NearStoreTypes>?

    suspend fun readNotifications():BaseResponse<Any>?

}