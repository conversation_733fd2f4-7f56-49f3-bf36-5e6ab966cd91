<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="20dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:background="?attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_round_gray"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgThumb"
            android:layout_width="135dp"
            android:layout_height="0dp"
            android:background="@null"
            android:scaleType="centerInside"
            android:layout_marginVertical="18dp"
            android:layout_marginHorizontal="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/portrait_placeholder" />



        <TextView
            android:id="@+id/tvTitle"
            style="@style/label_item_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="10dp"
            android:textAlignment="viewStart"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="TitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitleTitle"
            android:layout_marginHorizontal="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imgThumb" />

        <TextView
            android:id="@+id/tvDescription"
            style="@style/label_item_product_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_marginTop="4dp"
            android:textAlignment="viewStart"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            tools:text="testtesttesttetesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/price_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@+id/tvDescription">

            <TextView
                android:id="@+id/tvOldPrice"
                style="@style/label_item_old_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:textColor="?label_discount_details_price_before_text_color"
                android:background="@drawable/line_diagonal2"
                app:layout_constraintStart_toEndOf="@+id/tvCurrentPrice"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="50.99 جنيه" />

            <TextView
                android:id="@+id/tvCurrentPrice"

                style="@style/label_item_price_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"


                android:textColor="@color/color_59"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/tvPercentage"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvOldPrice"
                tools:text="41.99 جنيه" />

            <ImageView
                android:id="@+id/imageView5"
                android:layout_width="@dimen/icons_size_height_weight_10"
                android:layout_height="@dimen/icons_size_height_weight_10"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_red_label"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/tvPercentage"
                app:layout_constraintStart_toStartOf="@+id/tvCurrentPrice"
                app:layout_constraintTop_toTopOf="@+id/tvPercentage" />

            <TextView
                android:id="@+id/tvPercentage"
                style="@style/label_item_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/coral"
                android:layout_gravity="center_vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/tvOldPrice"
                app:layout_constraintStart_toStartOf="@+id/tvOldPrice"
                app:layout_constraintTop_toBottomOf="@+id/tvOldPrice"
                tools:text="30 %" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <include
            android:id="@+id/btnOrder"
            layout="@layout/btn_progress_circle"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="10dp"
            android:visibility="visible"
            android:layout_marginStart="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/imgThumb"
            app:layout_constraintTop_toBottomOf="@id/price_container" />

        <LinearLayout
            android:id="@+id/quantity_cont"
            android:layout_width="wrap_content"
            android:background="@drawable/bg_cart_quantity"
            android:padding="2dp"
            android:visibility="invisible"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/btnOrder"
            app:layout_constraintStart_toStartOf="@id/btnOrder"
            app:layout_constraintTop_toTopOf="@id/btnOrder">
            <include
                android:id="@+id/quantity_controller"
                layout="@layout/include_input_quantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>