<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_shadow"
    xmlns:app="http://schemas.android.com/apk/res-auto"

    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"


        android:layout_marginStart="4dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bg_round_dark_background"
        android:padding="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvCartItems"
            style="@style/cart_box_number"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/rect_quantity_controllers"
            android:ellipsize="end"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textColor="@color/white"
            tools:text="1" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/cart_box_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/cart_items"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvCartItems"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView

            style="@style/cart_box_total_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/cart_total"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@+id/tvTotal"
            app:layout_constraintEnd_toStartOf="@+id/tvTotal" />

        <TextView
            android:id="@+id/tvTotal"
            style="@style/cart_box_total_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textStyle="bold"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="41.99" />


    </androidx.constraintlayout.widget.ConstraintLayout>




</androidx.constraintlayout.widget.ConstraintLayout>