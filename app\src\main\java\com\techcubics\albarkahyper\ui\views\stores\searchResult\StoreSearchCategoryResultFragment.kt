package com.techcubics.albarkahyper.ui.views.stores.searchResult

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageButton
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.SnapHelper
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.albarkahyper.common.*
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.StoreByCategoryData
import com.techcubics.albarkahyper.databinding.FragmentStoreSearchCategoryResultBinding
import com.techcubics.albarkahyper.ui.adapters.home.StoresAdapter
import com.techcubics.albarkahyper.ui.adapters.product.BannersAdaper
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.albarkahyper.ui.views.stores.StoresViewModel
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.style.R
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class StoreSearchCategoryResultFragment : Fragment(),
    IPagePagedRowset<StoreByCategoryData>, IFavClickListener {

    private lateinit var binding: FragmentStoreSearchCategoryResultBinding
    private lateinit var storesAdapter: StoresAdapter<StoreByCategoryData>
    private  val storesFragmentViewModel: StoresViewModel by viewModel<StoresViewModel>()
    private lateinit var resultList:ArrayList<StoreByCategoryData>
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private var isLoading:Boolean=false
    private  var id:Int?=null
    private lateinit var name:String
    private lateinit var  etSearch:EditText
    private  var isGeneral:Boolean=true
    private var word:String=""
    private lateinit var bannersAdapter: BannersAdaper
    private var bannersSlider: Slider? = null
    private var _operation:Int=-1
    private var _position: Int?=null
    private  val productsViewModel: ProductsViewModel by viewModel<ProductsViewModel>()
    private  val TAG = "StoreSearchResultFrag"


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        binding= FragmentStoreSearchCategoryResultBinding.inflate(inflater, container, false)

        init()
        observers()
        events()

        return binding.root
    }


    override fun init() {

        resultList= arrayListOf()

        productsViewModel.getBanners()
        arguments?.let {
            id = it.getInt(Constants.INTENT_ID, -1)
            name = it.getString(Constants.INTENT_NAME, "")

            binding.toolbarSearchByCategory.tvTitle.text = name
            storesFragmentViewModel.getStoresByCategory(category = id!!, page = 1)
            //
            showHidePlaceHolder(show = false, type = null, message = null)
            binding.pagingLoadingImg.visibility = View.GONE
            binding.rvStores.visibility = View.GONE
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.pageLoadingAnimation.root)
            etSearch=  binding.searchToolbar.txtSearchWord
            etSearch.hint=getString(R.string.hint_write_store_name)
            var x:ImageButton= binding.searchToolbar.btnFilter
            x.visibility=View.GONE

        }

        binding.rvBanners.visibility=View.GONE
        binding.indicator.visibility=View.GONE
    }
    override  fun observers(){

        storesFragmentViewModel.storesByCategoryResponse.observe(viewLifecycleOwner, Observer { it->


            Helper.loadingAnimationVisibility(View.GONE,binding.pageLoadingAnimation.root)

            try {
                if (it.status!!) {

                    if(it.data!=null){

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvStores.visibility = View.VISIBLE

                        }else{

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }
        })

        storesFragmentViewModel.addRemoveFavoriteForStoreResponse.observe(viewLifecycleOwner,Observer{

            try {

                if(it.status!!){

                    when(_operation){
                        1->  storesFragmentViewModel.storesByCategoryResponse.value?.data!![_position!!].isFav=false
                        2->  storesFragmentViewModel.storesByCategoryResponse.value?.data!![_position!!].isFav=true
                    }

                    //storesAdapter.notifyDataSetChanged()

                }else{

                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvStores.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            }catch (ex:Exception){


                binding.pagingLoadingImg.visibility = View.GONE
                binding.rvStores.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }

        })

        productsViewModel.bannersResponse.observe(viewLifecycleOwner, Observer {


            try {
                if (it.status!!) {

                    if(it.data!=null){

                        if(it.data!!.size>0) {
                            binding.rvBanners.visibility=View.VISIBLE
                            showBanners(it.data!!)

                        }else{
                            binding.rvBanners.visibility=View.GONE
                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })


    }
    override fun events(){

        binding.toolbarSearchByCategory.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
        etSearch.addTextChangedListener(object:TextWatcher{
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                word=  p0.toString()
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        }
        )

        etSearch.setOnEditorActionListener { v, actionId, event ->
            when(actionId){
                EditorInfo.IME_ACTION_SEARCH -> {
                    //word= ""+v.text
                   // var e:String= word
                    Log.d(TAG, "events: ${word}")
                    v.clearFocus()
                    v.hideKeyboard()
                    resultList.clear()
                    binding.rvStores.adapter=null
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvStores.visibility = View.GONE
                    Helper.loadingAnimationVisibility(View.VISIBLE,binding.pageLoadingAnimation.root)
                    showHidePlaceHolder(show = false, type = null, message = null)
                    isGeneral=false
                    storesFragmentViewModel.getStoresByCategory(category=id!!,word=this.word,page=1)
                    true
                }

                else -> false
            }
        }
    }

    private fun showBanners(data:MutableList<BannerData>){

        bannersAdapter = BannersAdaper()
        bannersAdapter.items = data
        binding.rvBanners.adapter = bannersAdapter
        //////////////////
        var manager= LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

        val snapHelper: SnapHelper = PagerSnapHelper()
        binding.rvBanners.setLayoutManager(manager)
        binding.rvBanners.setOnFlingListener(null)
        snapHelper.attachToRecyclerView(binding.rvBanners)
        //////////////
        binding.indicator.attachToRecyclerView( binding.rvBanners)
        binding.indicator.visibility=View.VISIBLE

        if (data.size > 1) {
            bannersSlider = Slider(binding.rvBanners, 3000)
            bannersSlider?.setAdapter(bannersAdapter)
            bannersSlider?.start()
            bannersSlider?.setSnapHelper()
        }
    }

    fun View.hideKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)
    }

    override fun showData( items:List<StoreByCategoryData>){

        if(storesFragmentViewModel.storesByCategoryResponse.value?.pagingator?.currentPage==1) {

            resultList= items as ArrayList<StoreByCategoryData>
            storesAdapter = StoresAdapter<StoreByCategoryData>(this)
            storesAdapter.setItemsList(resultList)
            binding.rvStores.adapter = storesAdapter
            binding.rvStores.layoutManager =
                GridLayoutManager(context, 2)
            //
            var pageDelegate =
                PagingDelegate.Builder(storesAdapter).attachTo(binding.rvStores)
                    .listenWith(this).build()

        }else{

            items.forEach {
                resultList.add(it)
            }
            storesAdapter.notifyDataSetChanged()
            onDonePaging()
        }

        Log.d(TAG, "showDiscounts: ${resultList.size}")

    }
    override fun onPage(p0: Int) {


        if(!isLoading){
            Log.d(TAG, "onPage: ${p0}")
            if(storesFragmentViewModel.storesByCategoryResponse.value?.pagingator?.hasMorePages!!) {
                isLoading=true
                binding.pagingLoadingImg.visibility=View.VISIBLE
                when(isGeneral){
                    true->storesFragmentViewModel.getStoresByCategory(category = id!!,page=storesFragmentViewModel.storesByCategoryResponse.value?.pagingator?.currentPage!!+1)
                    false->storesFragmentViewModel.getStoresByCategory(category = id!!,page=storesFragmentViewModel.storesByCategoryResponse.value?.pagingator?.currentPage!!+1,word=word)
                }

            }
        }
    }
    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility=View.GONE
        isLoading=false
    }
    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                } else -> throw IllegalStateException("error")
            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }

    override fun onFavClick(parent:Int,position: Int, operation: Int) {
        _operation=operation
        _position=position
        storesFragmentViewModel.storesByCategoryResponse.value?.data!![position].id?.let {
            storesFragmentViewModel.addRemoveFavStore(
                it
            )
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}