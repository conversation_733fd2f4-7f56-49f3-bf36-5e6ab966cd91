package com.techcubics.albarkahyper.ui.views.auth.fragments

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.view.GravityCompat
import androidx.core.widget.addTextChangedListener
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.data.model.requests.auth.ForgetPasswordResetRequest
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentNewPasswordBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.utils.AuthUtils
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class NewPasswordFragment : Fragment() {

    private lateinit var binding : FragmentNewPasswordBinding
    private lateinit var progressBtn : ProgressButton
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val newPasswordViewModel by viewModel<AuthViewModel>()

    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentNewPasswordBinding.inflate(inflater,container,false)

        progressBtn = ProgressButton(requireContext())
        progressBtn.init(binding.signBtnProgress)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        initViews()
        observeViews()
        bottomSheetAlertDialog.init(requireContext())
        return binding.root
    }

    private fun observeViews() {
        newPasswordViewModel.forgetPasswordResetMutableLiveData.observe(viewLifecycleOwner ){

            if (!it?.status!!){
                progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.change_pass),null)
                bottomSheetAlertDialog.showDialog(it.message!!)
            }
            else if(it.message!!.contains(Constants.SERVER_ERROR)){
                progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.change_pass),null)
                Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
            }
            else{
                progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.change_pass),null)
                bottomSheetAlertDialog.showDialog(it.message!!)
                findNavController().navigate(R.id.action_newPasswordFragment_to_loginFragment)
            }
        }
    }

    private fun initViews() {

        activity?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {

                findNavController().navigate(R.id.action_newPasswordFragment_to_loginFragment)
            }
        })

        if(sharedPreferencesManager.getLanguage().equals("ar")){
            binding.passwordEdt.gravity = GravityCompat.START
            binding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            binding.confirmPasswordEdt.gravity = GravityCompat.START
            binding.confirmPasswordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
        }
        binding.passwordEdt.addTextChangedListener{
            if(sharedPreferencesManager.getLanguage().equals("ar")) {
                binding.passwordEdt.gravity = GravityCompat.END
                binding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_END
                binding.confirmPasswordEdt.gravity = GravityCompat.END
                binding.confirmPasswordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_END
            }else{
                binding.passwordEdt.gravity = GravityCompat.START
                binding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
                binding.confirmPasswordEdt.gravity = GravityCompat.START
                binding.confirmPasswordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            }
        }

        binding.passwordEdt.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus){
                binding.validatePassGroup.group.visibility = View.VISIBLE
            }
            else {
                binding.validatePassGroup.group.visibility = View.GONE
            }
        }
        binding.passwordEdt.addTextChangedListener{
            AuthUtils.validatePassword(binding.passwordEdt.text.toString(),binding.validatePassGroup.specialchar
                ,binding.validatePassGroup.AtoZ,binding.validatePassGroup.num,binding.validatePassGroup.charcount)
        }
        progressBtn.binding.textView.text = getString(com.techcubics.style.R.string.change_pass)
        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            if (!(AuthUtils.validatePassword(binding.passwordEdt.text.toString(),binding.validatePassGroup.specialchar
                    ,binding.validatePassGroup.AtoZ,binding.validatePassGroup.num,binding.validatePassGroup.charcount)) || binding.passwordEdt.text.isNullOrEmpty())
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.check_your_password_and_try_again))
            else if(binding.passwordEdt.text.toString() != binding.confirmPasswordEdt.text.toString() || binding.confirmPasswordEdt.text.isNullOrEmpty())
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.confirm_password_isincorrect))
            else{
                progressBtn.btnActivated()
                newPasswordViewModel.forgetPasswordResetCall(ForgetPasswordResetRequest( sharedPreferencesManager.getToken()!!,binding.passwordEdt.text.toString(),binding.confirmPasswordEdt.text.toString()))
            }

        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}