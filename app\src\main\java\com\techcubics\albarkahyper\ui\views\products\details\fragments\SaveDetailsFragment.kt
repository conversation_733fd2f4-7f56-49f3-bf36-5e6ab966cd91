package com.techcubics.albarkahyper.ui.views.products.details.fragments

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Save
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentSaveDetailsBinding
import com.techcubics.albarkahyper.ui.adapters.product.OfferProductsAdapter
import com.techcubics.albarkahyper.ui.adapters.product.ProductsImagesAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.ProductDetailsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

class SaveDetailsFragment : Fragment() ,OnItemsChangedListener , ViewTreeObserver.OnWindowFocusChangeListener{
    private var _binding: FragmentSaveDetailsBinding? = null
    private val binding get() = _binding!!
    private var saveId = -1
    private var slug: String? = null
    private var qty = 1
    private lateinit var imagesAdapter: ProductsImagesAdapter
    private var save: Save? = null
    private lateinit var addToCartProgressButton: ProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val productsAdapter: OfferProductsAdapter = OfferProductsAdapter(true)
    private lateinit var popupDialog: PopupDialog
    private val authViewModel by viewModel<AuthViewModel>()

    private val viewModel by viewModel<ProductDetailsViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var quantityButtonsController: QuantityButtonsController
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        saveId = arguments?.getInt(Constants.INTENT_ID, -1) ?: -1
        slug = arguments?.getString(Constants.INTENT_SLUG)
        _binding = FragmentSaveDetailsBinding.inflate(inflater, container, false)
        Helper.hideSystemUIWithNavigation(requireActivity().window)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        addToCartProgressButton = ProgressButton(requireContext())
        addToCartProgressButton.init(binding.saveDetailsContainer.btnAddToCart)
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            viewModel.getSaveDetails(slug ?: saveId.toString())

            quantityButtonsController = QuantityButtonsController(
                binding.saveDetailsContainer.productQtyCartContainer.root,
                binding.saveDetailsContainer.minOrderNote,
                requireContext(),
                this,
                Int.MAX_VALUE,
                1
            )
            quantityButtonsController.setQuantity()

            checkAuthorization()
            setBtnListeners()
            setImages()
            setObservers()
        } else {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            binding.scrollView.visibility = View.GONE
            binding.nonetworkView.visibility = View.VISIBLE
        }


    }


    private fun setBtnListeners() {
        binding.saveDetailsContainer.btnAddToCart.textView.text = getString(R.string.add_to_cart)
        binding.saveImg.navigateBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.saveDetailsContainer.btnAddToCart.constraintsLayout.setOnClickListener {
            if (sharedPreferencesManager.isLoggedIn() == "true") {
                addToCart(save)
            } else {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.save_details_to_login)
            }
        }
        binding.saveImg.btnShare.setOnClickListener {
            val shareUrl = Constants.BASE_SAVE_SHARE_URL + slug
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            viewModel.getShareLink(RateTypesEnum.Save.value, url = shareUrl, id = slug!!)
        }

        binding.saveDetailsContainer.branchContainer.setOnClickListener {
            save?.let { d ->
                val bundle = Bundle()
                bundle.putInt(Constants.INTENT_ID, d.shop.id)

            }
        }
//        quantityButtonsController.setListeners()
    }

    private fun setObservers() {
        viewModel.shareLinkResponse.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            if (it != null) {
                if (it.status!!) {
                    Helper.shareLink(requireContext(), it.data!!)

                } else {
                    Helper.ShowErrorDialog(requireContext(), it.message!!)
                }
                viewModel.shareLinkResponse.value = null
            }
        }
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.message!!.contains(getString(R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.savedetailsLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(R.string.server_error)
                    )
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }
        viewModel.saveDetailsResponse.observe(viewLifecycleOwner) { saveDetails ->
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            save = saveDetails.data
            if (saveDetails.message.toString() != Constants.SERVER_ERROR) {
                binding.tvMessage.visibility = View.GONE
                if (save != null) {
                    saveId = save?.saveID!!
                    slug = save?.slug
                    binding.animation.visibility = View.GONE
                    binding.scrollView.visibility = View.VISIBLE
                    setData()
                } else {
                    binding.animation.visibility = View.VISIBLE
                    binding.animation.setAnimation(R.raw.empty_box_lottie)
                    binding.scrollView.visibility = View.GONE
                }
            } else {
                binding.tvMessage.visibility = View.VISIBLE
                binding.tvMessage.text = getString(R.string.server_error)
                binding.animation.visibility = View.VISIBLE
                binding.animation.setAnimation(R.raw.lottie_error)
                binding.scrollView.visibility = View.GONE
            }


        }
        viewModel.addCartResponse.observe(viewLifecycleOwner)
        {
            if (it != null && it.message.toString() != Constants.SERVER_ERROR) {
                if (it.status == true) {
                    addToCartProgressButton.btnFinishedSuccessfully(
                        getString(R.string.add_to_cart),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(getString(R.string.added_to_cart))
                } else {
                    if(it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))){
                        CoroutineScope(Dispatchers.Main).launch {
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
                        addToCartProgressButton.btnFinishedFailed(getString(R.string.add_to_cart), null)
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }

                }
                viewModel.addCartResponse.value = null
            } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                addToCartProgressButton.btnFinishedFailed(getString(R.string.add_to_cart), null)
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }
        }
    }

    private fun setData() {
        val minQty=if ((save?.quantityCart ?:1)>0){
            save?.quantityCart ?:1
        }else{
            1
        }
        quantityButtonsController.updateQuantity(minQty)
        quantityButtonsController.isBtnDecreaseEnabled()
        val size = save?.Images?.size ?: 0
        if (size <= 1) {
            binding.saveDetailsContainer.dotsIndicator.visibility = View.GONE
        }
        imagesAdapter.updateImagesAndVideo(
            save!!.Images,
            null
        )

        Helper.loadImage(
            requireContext(),
            save!!.shop.logo,
            binding.saveDetailsContainer.galleryImg
        )
        save?.price?.let { setPrice(it,binding.saveDetailsContainer.tvPrice) }
        binding.saveDetailsContainer.galleryName.text = save!!.shop.name
        calcPrice()
        binding.saveDetailsContainer.tvTitle.text = save!!.name
        setProductList()
        setTimer()
    }

    override fun calcPrice() {
        val price = save?.price?:0f
        val priceCents = (price * 100f).toInt()
        val totalPriceCents = priceCents * quantityButtonsController.getQuantity()
        totalPriceCents.toFloat() / 100f
        setPrice((totalPriceCents.toFloat() / 100f), binding.saveDetailsContainer.price)
    }

    private fun setPrice(price: Float,priceTextView: TextView) {
        "${java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(price)} ${getString(R.string.currency_name)}".also {
            priceTextView.text = it
        }
    }

    private fun setProductList() {
        productsAdapter.items = save?.products!!
        binding.saveDetailsContainer.productRecyclerView.adapter = productsAdapter
        binding.saveDetailsContainer.productRecyclerView.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
    }

    private fun setTimer() {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

        val start = save?.startDate + " " + save?.fromTime
        val end = save?.endDate + " " + save?.toTime

        val from = LocalDateTime.parse(start, formatter)
        val to = LocalDateTime.parse(end, formatter)

        val fromPeriod = Date.from(from.atZone(ZoneId.systemDefault()).toInstant())
        val toPeriod = Date.from(to.atZone(ZoneId.systemDefault()).toInstant())
        val diff: Long = toPeriod.time - fromPeriod.time

        save?.timer?.cancel()

        save?.timer = object : CountDownTimer(diff, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val x = Helper.formatToDigitalClock(millisUntilFinished).split(':')
                if (_binding != null) {
                    binding.saveDetailsContainer.includeTimerSection.tvHours.text = x[0]
                    binding.saveDetailsContainer.includeTimerSection.tvMinutes.text = x[1]
                    binding.saveDetailsContainer.includeTimerSection.tvSeconds.text = x[2]
                }
            }

            override fun onFinish() {
            }
        }.start()
    }

    override fun onPause() {
        super.onPause()
        save?.timer?.cancel()
    }

    private fun setImages() {
        imagesAdapter = ProductsImagesAdapter(
            requireContext(),
            listOf()
        )
        println(saveId)
        binding.saveImg.viewPager2.adapter = imagesAdapter
        binding.saveDetailsContainer.dotsIndicator.attachTo(binding.saveImg.viewPager2)
    }

    private fun addToCart(save: Save?) {
        save.let { d ->
            addToCartProgressButton.btnActivated()
            viewModel.addToCart(
                EndPointConstants.add_save,
                shopId = d?.shop!!.id,
                modelType = d.model_type,
                modelId = d.saveID,
                qty = quantityButtonsController.getQuantity()
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.root.viewTreeObserver?.removeOnWindowFocusChangeListener(this)
        _binding = null
        save?.timer?.cancel()
        Helper.exitFullScreen(requireContext())
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUIWithNavigation(requireActivity().window)
        else Helper.showSystemUIWithNavigation(requireActivity().window)
    }

    private fun checkAuthorization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}