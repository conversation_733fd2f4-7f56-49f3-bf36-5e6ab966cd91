package com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.model.pojo.CartData
import com.techcubics.data.model.pojo.Coupon
import com.techcubics.data.model.pojo.Locations
import com.techcubics.data.model.pojo.SelectedLocationData
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.ItemRemovingRequest
import com.techcubics.data.model.requests.SelectLocationRequest
import com.techcubics.data.model.requests.SubmitCouponRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.profile.ProfileRepo
import com.techcubics.data.repos.product.ProductsRepoImpl
import com.techcubics.data.repos.home.profile.ProfileRepoImpl
import com.techcubics.data.repos.product.ProductsRepo
import kotlinx.coroutines.launch

class CartFragmentViewModel(
    private val productsRepo: ProductsRepo,
    private val profileRepo: ProfileRepo
) : ViewModel() {

    val cartResponse = MutableLiveData<BaseResponse<ArrayList<CartData>>?>()
    val cartItemRemovedResponse = MutableLiveData<BaseResponse<CartData>>()
    val couponResponse = MutableLiveData<BaseResponse<Coupon>>()
    val addCartResponse = MutableLiveData<BaseResponse<CartData>>()
    val selectedLocationResponse = MutableLiveData<BaseResponse<SelectedLocationData>>()
    val listLocationResponseMutableLiveData = MutableLiveData<BaseResponse<ArrayList<Locations>>?>()

    fun updateCartProduct(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        sizeId: Int?=null,
        colorId: Int?=null
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId, modelType, modelId, qty, sizeId, colorId)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }

    fun updateCartProduct(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId, modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }

    fun getCart() {
        viewModelScope.launch {
            val rs = productsRepo.getCart()
            rs?.let { cartResponse.postValue(it) }
        }
    }

    fun removeCartItem(modelType: String?, modelId: Int?) {
        viewModelScope.launch {
            val request = ItemRemovingRequest(modelId, modelType)
            val rs = productsRepo.removeCartItem( request)
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
    }

    fun removeCartItem(modelType: String?, modelId: Int?, colorId: Int?) {
        viewModelScope.launch {
            val request = ItemRemovingRequest(colorId, modelId, modelType)
            val rs = productsRepo.removeCartItem( request)
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
    }

    fun submitCoupon( code: String) {
        viewModelScope.launch {
            val request=SubmitCouponRequest(code)
            val rs = productsRepo.submitCoupon(request)
            rs?.let { couponResponse.postValue(it) }
        }
    }

    fun selectLocation(locationId: Int?) {
        viewModelScope.launch {
            val request=SelectLocationRequest(locationIdRequest=locationId)
            val rs = productsRepo.selectLocation(request)
            rs?.let { selectedLocationResponse.postValue(it) }
        }
    }

    fun listLocations(country_id: Int, countryCode: String) {
        viewModelScope.launch {
            val listLocationResponse = profileRepo.listLocation(country_id, countryCode)

            if (listLocationResponse != null) {
                listLocationResponseMutableLiveData.postValue(listLocationResponse)
            }

        }
    }

    fun deleteCart() {
        viewModelScope.launch {
            val rs = productsRepo.removeAllCart()
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
    }

}
