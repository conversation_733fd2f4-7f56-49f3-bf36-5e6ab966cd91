<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
  >

    <ImageView
        android:id="@+id/imgProduct"
        android:layout_width="@dimen/icons_size_height_weight_9"
        android:layout_height="@dimen/icons_size_height_weight_9"
        android:layout_marginTop="4dp"
        android:background="@null"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@tools:sample/avatars" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/label_item_collection_item"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textAlignment="viewStart"
        app:layout_constraintStart_toEndOf="@+id/imgProduct"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/imgProduct"
        app:layout_constraintBottom_toBottomOf="@id/imgProduct"
        tools:text="test" />
</androidx.constraintlayout.widget.ConstraintLayout>