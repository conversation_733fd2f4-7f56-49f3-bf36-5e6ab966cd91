package com.techcubics.albarkahyper.ui.adapters.store

import android.content.Context
import android.graphics.Point
import android.graphics.Rect
import android.view.*
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemStoreProductBinding
import com.techcubics.albarkahyper.ui.adapters.holders.store.ProductHolderItem


class ProductsAdapter<T>(
    val margin: Int = 0,
    val onFavClickListener: IFavClickListener? = null,
    val onClickHandler: IOnAdapterItemClickHandler,
    val isHorizontal: Boolean,
    val context: Context
) : PagingAdapter() {

    lateinit var items:List<T>
    lateinit var itemBinding: ItemStoreProductBinding
    var minOrder: Float = 0f
    fun setItemsList(_items: List<T>, minOrder: Float) {
        items = _items
        this.minOrder=minOrder
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductHolderItem<T> {
        itemBinding = ItemStoreProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)

        return ProductHolderItem(itemBinding,parent.context,margin,onFavClickListener!!, onClickHandler )
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as ProductHolderItem<T>
        holder.bind(items.get(position),minOrder)
    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_product
    }

    override fun getPagingItemCount(): Int {
        return items.size
    }
    private fun getScreenWidth(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val size = Point()
        display.getSize(size)
        return size.x
    }
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        if (isHorizontal) {
            recyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val layoutParams = view.layoutParams as RecyclerView.LayoutParams
                    layoutParams.width = getScreenWidth(context) / 2
                    layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
                    outRect.set(0, 0, 0, 0)
                }
            })
        }
    }


}