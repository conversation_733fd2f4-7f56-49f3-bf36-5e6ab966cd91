<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_fragment"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <ImageButton
            android:id="@+id/btnFilter"
            android:layout_width="@dimen/icons_size_height_weight_9"
            android:layout_height="@dimen/icons_size_height_weight_9"
            android:layout_marginEnd="16dp"
            android:background="@null"
            android:scaleType="centerInside"
            android:src="@drawable/ic_filter"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            app:layout_constraintBottom_toTopOf="@id/sc_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

<!--        <com.harrywhewell.scrolldatepicker.DayScrollDatePicker-->
<!--            android:id="@+id/day_scroll_calender"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:showFullDate="false"-->
<!--            android:visibility="gone"-->
<!--            app:selectedTextColor="@color/black"-->
<!--            app:selectedColor="@color/app_color"-->
<!--            app:baseTextColor="@color/black"-->
<!--            android:layout_marginStart="15dp"-->
<!--            android:layout_marginEnd="16dp"-->
<!--            android:layout_marginTop="27dp"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/toolbar"-->
<!--            />-->

        <androidx.core.widget.NestedScrollView
            android:id="@+id/sc_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnFilter">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="90dp"
                android:layout_marginTop="20dp">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/mypurchasesRV"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
                    tools:itemCount="15"
                    tools:listitem="@layout/item_mypurchases"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />
                <include
                    android:id="@+id/server_error_layout"
                    layout="@layout/include_placeholder"
                    android:gravity="center"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>
        <include
            android:id="@+id/placeholder_layout"
            layout="@layout/include_placeholder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

        <include
            android:id="@+id/action_loading_animation"
            layout="@layout/include_action_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="32dp"
            android:backgroundTint="@color/app_color"
            android:clickable="true"
            android:background="?attr/selectableItemBackground"
            android:src="@drawable/ic_item_add_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>