package com.techcubics.albarkahyper.common

import android.app.AlertDialog
import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.DialogFragment
import com.techcubics.albarkahyper.databinding.DialogFeedbackBinding
import com.techcubics.style.R

class DialogFeedback(var email: String) : DialogFragment() {
    private var _binding: DialogFeedbackBinding? = null
    private val binding get() = _binding!!
    private var rate = 0f
    val intent = Intent(Intent.ACTION_SENDTO)
    private var deviceInfo: String? = null
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(activity)
        val inflater = requireActivity().layoutInflater
        _binding = DialogFeedbackBinding.inflate(inflater)
        builder.setView(binding.root)
        if (savedInstanceState != null) {
            rate = savedInstanceState.getFloat(RATING_KEY)
        }
        binding.btMaybeLater.setOnClickListener { dismiss() }
        deviceInfo = "Device Info:"
        deviceInfo += """OS Version: ${System.getProperty("os.version")}(${Build.VERSION.INCREMENTAL})"""
        deviceInfo += """OS API Level: ${Build.VERSION.SDK_INT}"""
        deviceInfo += """Device: ${Build.DEVICE}"""
        deviceInfo += """Model (and Product): ${Build.MODEL} (${Build.PRODUCT})"""

        val feedback = binding.etFeedback.text
        binding.btFeedbackSend.setOnClickListener {
            if (!feedback.isNullOrEmpty()&&feedback.length>10) {

                intent.data = Uri.parse("mailto:")
                intent.putExtra(Intent.EXTRA_EMAIL, arrayOf(email))
                intent.putExtra(
                    Intent.EXTRA_SUBJECT,
                    getString(R.string.app_name) + " App Rating...!"
                )
                intent.putExtra(
                    Intent.EXTRA_TEXT,
                    "Stars: $rate\n\nFeedback: $feedback\n\n$deviceInfo"
                )
                try {
                    startActivity(Intent.createChooser(intent, "Choose Email Client..."))
                }
                catch (e: Exception){
                    Toast.makeText(requireContext(),e.message,Toast.LENGTH_LONG).show()
                    Log.d(RATING_KEY, "onCreateDialog: $e.message")
                }
                dismiss()

            }
        }
        binding.etFeedback.doOnTextChanged { _, _, _, _ ->
            if ( binding.etFeedback.text.isNullOrEmpty()){
                binding.textInput.error = "Please enter at least 10 characters."
            }else{
                binding.textInput.isErrorEnabled = false
            }
        }
        builder.setCancelable(false)
        return builder.create()
    }

    fun setRating(rating: Float) {
        this.rate = rating
    }

    companion object {
        private const val RATING_KEY = "rating"
    }
}