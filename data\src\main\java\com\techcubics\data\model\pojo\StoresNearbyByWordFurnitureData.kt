package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class NearByStoresResponse<T>(

    @SerializedName("data")
    val data: T?=null,
    @SerializedName("message")
    val message: String,
    @SerializedName("status")
    val status: <PERSON><PERSON>an,
    @SerializedName("paginator")
    val pagingator: Paginator?=null
)

data class StoresNearbyByWordFurnitureData(

    @SerializedName("id")
    val id:Int,
    @SerializedName("shop_id")
    val furnitureID:Int,
    @SerializedName("branch_type_id")
    val branchTypeID:Int,
    @SerializedName("branch_type_name")
    val branchTypeName:String,
    @SerializedName("name")
    val name:String,
    @SerializedName("description")
    val description:String,
    @SerializedName("logo")
    val logo:String,
    @SerializedName("country")
    val country: Country,
    @SerializedName("governorate")
    val governorate: Governerate,
    @SerializedName("region")
    val region: Regoin,
    @SerializedName("address")
    val address:String,
    @SerializedName("is_fav")
    var isFav:Boolean,
    @SerializedName("rate")
    val rate:Float,
    @SerializedName("rate_count")
    val rate_count:Int,
    @SerializedName("open")
    val isOpened:Boolean,
    @SerializedName("link_google_play")
    val linkGooglePlay:String,
    @SerializedName("link_apple_store")
    val linkAppleStore:String,
    @SerializedName("qr_image")
    val qrImage:String,
    @SerializedName("lat")
    val latitute:String,
    @SerializedName("lng")
    val longitude:String,
)