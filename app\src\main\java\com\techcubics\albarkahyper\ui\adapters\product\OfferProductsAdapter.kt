package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Product
import com.techcubics.albarkahyper.databinding.ItemOfferProductBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.OfferProductHolderItem


class OfferProductsAdapter(private val isDetails:Boolean=false): RecyclerView.Adapter<OfferProductHolderItem>() {

    lateinit var items:List<Product>

    fun setItemsList(_items:List<Product>) {
        items = _items
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OfferProductHolderItem {
        val itemBinding = ItemOfferProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OfferProductHolderItem(itemBinding,parent.context,isDetails)
    }

    override fun onBindViewHolder(holder: OfferProductHolderItem, position: Int) {
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return items.size
    }


}