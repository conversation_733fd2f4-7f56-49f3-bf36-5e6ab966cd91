package com.techcubics.albarkahyper.ui.views.products.details.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Discount
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentDiscountDetailsBinding
import com.techcubics.albarkahyper.ui.adapters.product.ProductsImagesAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.ProductDetailsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.style.R
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class DiscountDetailsFragment : Fragment(),OnItemsChangedListener, ViewTreeObserver.OnWindowFocusChangeListener {
    private var _binding: FragmentDiscountDetailsBinding? = null
    private val binding get() = _binding!!
    private var discountId = -1
    private lateinit var imagesAdapter: ProductsImagesAdapter
    private var discount: Discount? = null
    private lateinit var addToCartProgressButton: ProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog

    private val authViewModel by viewModel<AuthViewModel>()

    private val viewModel by viewModel<ProductDetailsViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var quantityButtonsController: QuantityButtonsController


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        discountId = arguments?.getInt(Constants.INTENT_ID, -1) ?: -1
        _binding = FragmentDiscountDetailsBinding.inflate(inflater, container, false)
        Helper.hideSystemUIWithNavigation(requireActivity().window)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        addToCartProgressButton = ProgressButton(requireContext())
        addToCartProgressButton.init(binding.discountDetailsContainer.btnAddToCart)
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
            viewModel.getDiscountDetails(discountId)



            checkAuthorization()
            setBtnListeners()
            setImages()
            setObservers()
        } else {
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            binding.scrollView.visibility = View.GONE
            binding.nonetworkView.visibility = View.VISIBLE
        }


    }


    private fun setBtnListeners() {
        binding.discountDetailsContainer.btnAddToCart.textView.text = getString(R.string.add_to_cart)
        binding.discountImg.navigateBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.discountDetailsContainer.btnAddToCart.constraintsLayout.setOnClickListener {
            if (sharedPreferencesManager.isLoggedIn() == "true") {
                addToCart(discount)
            } else {
                findNavController().navigate(com.techcubics.albarkahyper.R.id.discount_details_to_login)
            }
        }
        binding.discountImg.btnShare.setOnClickListener {
            val shareUrl = Constants.BASE_DISCOUNT_SHARE_URL + discountId
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
            viewModel.getShareLink(RateTypesEnum.Discount.value, url = shareUrl, id = discountId.toString())
        }

        binding.discountDetailsContainer.branchContainer.setOnClickListener {
            discount?.let { d ->
                val bundle= Bundle()
                d.shop?.id?.let { it1 -> bundle.putInt(Constants.INTENT_ID, it1) }

            }
        }
//        quantityButtonsController.setListeners()

    }

    private fun setObservers() {
        viewModel.shareLinkResponse.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            if (it!=null) {
                if (it.status!!) {
                    Helper.shareLink(requireContext(), it.data!!)

                } else {
                    Helper.ShowErrorDialog(requireContext(), it.message!!)
                }
                viewModel.shareLinkResponse.value = null
            }
        }
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {

            if (it?.message?.contains("Unauthenticated")!!) {
                 findNavController().navigate(com.techcubics.albarkahyper.R.id.discount_details_to_login)
            } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }

        }
        viewModel.discountDetailsResponse.observe(viewLifecycleOwner) { discountDetails ->
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            discount = discountDetails.data
            if (discountDetails.message.toString() != Constants.SERVER_ERROR) {
                binding.tvMessage.visibility = View.GONE
                if (discount != null) {
                    binding.animation.visibility = View.GONE
                    binding.scrollView.visibility = View.VISIBLE
                    setData()
                } else {
                    binding.animation.visibility = View.VISIBLE
                    binding.animation.setAnimation(R.raw.empty_box_lottie)
                    binding.scrollView.visibility = View.GONE
                }
            } else {
                binding.tvMessage.visibility = View.VISIBLE
                binding.tvMessage.text = getString(R.string.server_error)
                binding.animation.visibility = View.VISIBLE
                binding.animation.setAnimation(R.raw.lottie_error)
                binding.scrollView.visibility = View.GONE
            }


        }
        viewModel.addCartResponse.observe(viewLifecycleOwner)
        {
            if (it != null&&it.message.toString()!= Constants.SERVER_ERROR) {
                if (it.status==true) {
                    addToCartProgressButton.btnFinishedSuccessfully(getString(R.string.add_to_cart),null)
                    bottomSheetAlertDialog.showDialog(getString(R.string.added_to_cart))
                }else{
                    addToCartProgressButton.btnFinishedFailed(getString(R.string.add_to_cart),null)
                    bottomSheetAlertDialog.showDialog(it.message.toString())
                }
                viewModel.addCartResponse.value = null
            }else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                addToCartProgressButton.btnFinishedFailed(getString(R.string.add_to_cart),null)
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(R.string.server_error)
                )
            }
        }
    }

    private fun setData() {
        val minQty=if ((discount?.quantityCart?.toInt()?:1)>0){
            discount?.quantityCart?.toInt()?:1
        }else{
            1
        }
        quantityButtonsController = QuantityButtonsController(
            binding.discountDetailsContainer.productQtyCartContainer.root,
            binding.discountDetailsContainer.minOrderNote,
            requireContext(),
            this,
            discount?.maxQty?: Int.MAX_VALUE,
            discount?.minQty?:1
        )
        quantityButtonsController.setQuantity()
        val qty=if ((discount?.quantityCart?.toInt() ?:0)>0){
            discount?.quantityCart?.toInt()!!
        }else{
            minQty
        }
        quantityButtonsController.updateQuantity(qty)
        quantityButtonsController.isBtnDecreaseEnabled()
        quantityButtonsController.isBtnIncreaseEnabled()
        val size = discount?.images?.size ?: 0
        if (size <= 1) {
            binding.discountDetailsContainer.dotsIndicator.visibility = View.GONE
        }
        imagesAdapter.updateImagesAndVideo(
            discount!!.images,
            null
        )

        discount!!.shop?.logo?.let {
            Helper.loadImage(requireContext(),
                it,binding.discountDetailsContainer.galleryImg)
        }

        binding.discountDetailsContainer.galleryName.text = discount!!.shop?.name
        val priceAfter = discount?.priceAfter?:0f
        "${java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(priceAfter)} ${getString(R.string.currency_name)}".also {
            binding.discountDetailsContainer.discountPriceAfter.text = it
        }
        val priceBefore = discount?.priceBefore?:0f
        "${java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(priceBefore)} ${getString(R.string.currency_name)}".also {
            binding.discountDetailsContainer.discountPriceBefore.text = it
        }
        "${discount!!.percent} %".also {
            binding.discountDetailsContainer.discountPercentage.text = it
        }
        calcPrice()
        binding.discountDetailsContainer.discountDetails.text = discount!!.productDescription
        binding.discountDetailsContainer.tvTitle.text = discount!!.productName
//        binding.discountDetailsContainer.rating.rating = discount?.rate?.toFloat() ?: 0f
//        binding.discountDetailsContainer.tvRating.text = discount?.rate.toString()
//        "${discount!!.rateCount} ${getString(R.string.rate_title)}".also {
//            binding.discountDetailsContainer.rateCount.text = it
//        }
    }

    override fun calcPrice() {
        val price = discount?.priceAfter?:0f
        val priceCents = (price * 100f).toInt()
        val totalPriceCents = priceCents * quantityButtonsController.getQuantity()
        totalPriceCents.toFloat() / 100f
        setPrice(totalPriceCents.toFloat() / 100f)
    }

    private fun setPrice(price: Float) {
        "${java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(price)} ${getString(R.string.currency_name)}".also {
            binding.discountDetailsContainer.totalPriceAfter.text = it
        }
    }

    private fun setImages() {
        imagesAdapter = ProductsImagesAdapter(
            requireContext(),
            listOf()
        )
        binding.discountImg.viewPager2.adapter = imagesAdapter
        binding.discountDetailsContainer.dotsIndicator.attachTo(binding.discountImg.viewPager2)
    }

    private fun addToCart(discount: Discount?) {
        discount.let { d ->
            addToCartProgressButton.btnActivated()
            viewModel.addToCart(
                EndPointConstants.add_discount,
                shopId = d?.shop?.id,
                modelType = d?.modelType,
                modelId = d?.id,
                qty = quantityButtonsController.getQuantity()
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.root.viewTreeObserver?.removeOnWindowFocusChangeListener(this)
        _binding = null
        Helper.exitFullScreen(requireContext())
    }

    private fun checkAuthorization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUIWithNavigation(requireActivity().window)
        else Helper.showSystemUIWithNavigation(requireActivity().window)
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}