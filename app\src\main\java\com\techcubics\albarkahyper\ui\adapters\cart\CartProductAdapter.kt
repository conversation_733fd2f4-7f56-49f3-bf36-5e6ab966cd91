package com.techcubics.albarkahyper.ui.adapters.cart

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.IRefreshListListener
import com.techcubics.albarkahyper.databinding.ItemCartProductBinding
import com.techcubics.albarkahyper.ui.adapters.holders.cart.CartProductViewHolder
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.data.model.pojo.CartDetails

class CartProductAdapter(
    private val context: Context,
    private var cartItems: List<CartDetails>,
    private val vm: CartFragmentViewModel,
    private val refreshCart: IRefreshListListener?,
    private var furnitureId: Int?
) :
    RecyclerView.Adapter<CartProductViewHolder>() {

    fun updateItems(furnitureId: Int?, cartItems: List<CartDetails>) {
        this.cartItems = cartItems
        this.furnitureId = furnitureId
        notifyDataSetChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CartProductViewHolder {
        val binding = ItemCartProductBinding.inflate(LayoutInflater.from(context), parent, false)
        return CartProductViewHolder(context, binding, furnitureId, refreshCart, vm)

    }

    override fun onBindViewHolder(holder: CartProductViewHolder, position: Int) {
        holder.setData(cartItems[position])
    }

    override fun getItemCount(): Int {
        return cartItems.size
    }
}

