package com.techcubics.albarkahyper.common

import android.content.Context
import android.text.InputType
import android.view.View
import com.techcubics.data.model.pojo.Size
import com.techcubics.albarkahyper.ui.dropdownlistadapter.SizeDropdownAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.IncludeSizeSectionBinding
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener


class SizeController(
    private val context: Context,
    private val listener: OnItemsChangedListener,
    view: View
) {
    private lateinit var sizesAdapter: SizeDropdownAdapter
    private var binding = IncludeSizeSectionBinding.bind(view)
    private var selectedSizeId = -1
    private var sizeIndex = -1
    private var sizes: MutableList<Size>? = null

    fun initDropdownList(Sizes: MutableList<Size>?) {
        this.sizes = Sizes
        binding.sizesDropdownList.inputType = InputType.TYPE_NULL
        if (Sizes == null || Sizes.size <= 0) {
            binding.root.visibility = View.GONE
            listener.calcPrice()
        } else {
            binding.root.visibility = View.VISIBLE
            if (Sizes.size > 0) {
//                onItemSelected(0)
//                setSize()
            }
        }

    }

    private fun setSize() {
        sizes?.let {
            sizesAdapter = SizeDropdownAdapter(context, R.layout.item_dropdown_list, it)
            binding.sizesDropdownList.setAdapter(sizesAdapter)
            binding.sizesDropdownList.setOnItemClickListener { _, _, position, _ ->
                onItemSelected(position)
            }
        }
    }

    private fun onItemSelected(position: Int) {
        binding.sizesDropdownList.setText(sizes?.get(position)?.name ?: "", false)
        selectedSizeId = sizes?.get(position)?.sizeID ?: -1
        listener.calcPrice()
    }

    fun getSelectedSizeIndex():Int{
        return sizeIndex
    }

    fun getSizeId(): Int {
        return selectedSizeId
    }

}