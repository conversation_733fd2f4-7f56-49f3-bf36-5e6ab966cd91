package com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Discount
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentDiscountsBinding
import com.techcubics.albarkahyper.ui.adapters.home.DiscountsAdapter
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.shared.enums.RateTypesEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class DiscountsFragment : Fragment(), IPagePagedRowset<Discount>,
    IOnAdapterItemClickHandler {

    private lateinit var binding: FragmentDiscountsBinding
    private lateinit var discountsAdapter: DiscountsAdapter<Discount>
    private  val discountsFragmentViewModel: SectionsViewModel by viewModel<SectionsViewModel>()
    private var isLoading: Boolean = false
    private lateinit var resultList: ArrayList<Discount>
//    private lateinit var discountProgressButton: CircleProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog : PopupDialog
    private val TAG = "DiscountsFragment"
//
//    companion object {
//        val instance: DiscountsFragment = DiscountsFragment()
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(DialogFragment.STYLE_NORMAL, com.techcubics.style.R.style.bottomsheet_style)
//
//    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentDiscountsBinding.inflate(inflater, container, false)
//        dialog?.setOnShowListener {
//            val d: BottomSheetDialog = dialog as BottomSheetDialog
//            val bottomSheetInternal: View =
//                d.findViewById(com.google.android.material.R.id.design_bottom_sheet)!!
//            BottomSheetBehavior.from(bottomSheetInternal)
//                .setState(BottomSheetBehavior.STATE_EXPANDED)
//        }

        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {
        //SharedPreferencesManager.init(requireContext())

        binding.toolbarDiscounts.tvTitle.text =
            resources.getText(com.techcubics.style.R.string.section_discounts_title)
       // discountsFragmentViewModel.getDiscounts(1)
        //
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvDiscounts.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
    }

    override fun observers() {

        discountsFragmentViewModel.discountsResponse.observe(this, Observer {


            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)

            try {
                if (it.status!!) {

                    if (it.data != null) {

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvDiscounts.visibility = View.VISIBLE

                        } else {

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }
        })

        discountsFragmentViewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null && it.message.toString() != Constants.SERVER_ERROR) {
                if (it.status == true) {
//                    discountProgressButton.btnRoundFinishedSuccessfully()
                    bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.added_to_cart))
                } else {
                    if(it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))){
                        CoroutineScope(Dispatchers.Main).launch {
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
//                        discountProgressButton.btnRoundFinishedFailed()
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }
                }
                discountsFragmentViewModel.addCartResponse.value = null
            } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(com.techcubics.style.R.string.server_error)
                )
            }
        }

    }

    override fun events() {

        binding.toolbarDiscounts.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
    }


    override fun showData(items: List<Discount>) {

        if (discountsFragmentViewModel.discountsResponse.value?.pagingator?.currentPage == 1) {
            resultList = items as ArrayList<Discount>
            discountsAdapter = DiscountsAdapter(onClickListener = this)
            discountsAdapter.setItemsList(resultList)
            binding.rvDiscounts.adapter = discountsAdapter
            binding.rvDiscounts.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            //
            var pageDelegate =
                PagingDelegate.Builder(discountsAdapter).attachTo(binding.rvDiscounts)
                    .listenWith(this).build()


        } else {

            items.forEach {
                resultList.add(it)
            }
            discountsAdapter.notifyDataSetChanged()
            onDonePaging()
        }

        Log.d(TAG, "showDiscounts: ${resultList.size}")

    }

    override fun onPage(p0: Int) {


        if (!isLoading) {
            Log.d(TAG, "onPage: ${p0}")
            if (discountsFragmentViewModel.discountsResponse.value?.pagingator?.hasMorePages!!) {
                isLoading = true
                binding.pagingLoadingImg.visibility = View.VISIBLE
               // discountsFragmentViewModel.getDiscounts(discountsFragmentViewModel.discountsResponse.value?.pagingator?.currentPage!! + 1)
            }
        }
    }

    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility = View.GONE
        isLoading = false
    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle = Bundle()
        bundle.putInt(Constants.INTENT_ID, itemId ?: -1)
//        findNavController().navigate(
//            R.id.action_discountsFragment_to_discountDetailsFragment,
//            bundle
//        )
        when(type){
            RateTypesEnum.Discount.value->{
                findNavController()
                    .navigate(R.id.action_discountsFragment_to_discountDetailsFragment,
                        bundle
                    )
            }
            RateTypesEnum.Store.value->{

            }
        }

    }

    override fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
        if (SharedPreferencesManager.isLoggedIn() == "true") {
//            discountProgressButton = progressButton
            discountsFragmentViewModel.addToCart(
                pathEndPoint,
                shopId,
                modelType,
                modelId,
                qty
            )
        } else {
            findNavController().navigate(R.id.go_to_login)
        }
    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}