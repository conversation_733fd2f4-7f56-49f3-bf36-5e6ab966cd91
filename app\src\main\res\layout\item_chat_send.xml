<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"

   >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/sendLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:padding="8dp"
        android:layout_gravity="start"
       >

        <TextView
            android:id="@+id/tvSMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_gray_background"
            android:padding="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="hi, how are you "
            style="@style/chat_sender"/>

        <TextView
            android:id="@+id/tvSDateTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="@+id/tvSMessage"
            app:layout_constraintTop_toBottomOf="@+id/tvSMessage"
            tools:text="04:30 PM"
            style="@style/chat_room_time"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/receiveLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:padding="8dp"
        android:layout_gravity="end"
        >

        <TextView
            android:id="@+id/tvRMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_dark_background"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="hi, how are you "
            style="@style/chat_receiver"/>

        <TextView
            android:id="@+id/tvRDateTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="@+id/tvRMessage"
            app:layout_constraintTop_toBottomOf="@+id/tvRMessage"
            tools:text="04:30 PM"
            style="@style/chat_room_time"/>

    </androidx.constraintlayout.widget.ConstraintLayout>



</LinearLayout>