<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="30dp"
    android:background="@color/white"
    android:paddingVertical="12dp">

    <TextView
        android:id="@+id/areaName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.AppCompat.Small"
        android:textColor="#8a000000"
        android:layout_marginVertical="6dp"
        style="@style/label_item_area"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/currentAreaIcon"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="Alexandria" />

    <ImageView
        android:id="@+id/currentAreaIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_location"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/areaName"
        app:layout_constraintBottom_toBottomOf="@id/areaName"
        android:contentDescription="@string/all_areas" />
</androidx.constraintlayout.widget.ConstraintLayout>