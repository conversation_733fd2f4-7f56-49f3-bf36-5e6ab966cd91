package com.techcubics.data.repos.chat

import com.techcubics.data.model.pojo.SendSupportData
import com.techcubics.data.model.pojo.SupportTicketData
import com.techcubics.data.model.requests.TicketSendCallRequest
import com.techcubics.data.model.requests.chat.SupportSendRequest
import com.techcubics.data.model.requests.chat.SupportTicketMessagesRequest
import com.techcubics.data.remote.BaseResponse

interface SupportChatRepo {

    suspend fun getHistory(request: SupportTicketMessagesRequest): BaseResponse<MutableList<SupportTicketData>>?

    suspend fun send(request: TicketSendCallRequest): BaseResponse<SendSupportData>?
}