package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.databinding.ItemBranchTypeBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemMainCategoryBinding
import com.techcubics.albarkahyper.databinding.ItemSliderBinding
import com.techcubics.shared.constants.Constants


class MainCategoryHolderItem (val binding: ItemMainCategoryBinding, val context: Context ,val onClick: IOnAdapterItemClickHandler): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "MainCategoryHolderItem"
    private lateinit var intent:Intent

    fun bind(data: Category){


        Helper.loadImage(context,data.image!!,binding.icon)
         binding.tvTitle.text=data.name
        binding.root.setOnClickListener {

            onClick.onItemClicked(data.categoryId,data.name!!)
        }

    }

}