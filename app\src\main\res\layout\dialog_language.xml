<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="300dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_popup_dialog">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/langRV"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
        tools:itemCount="4"
        tools:listitem="@layout/item_language"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingBottom="20dp"/>
    <TextView
        android:id="@+id/empty_region"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:text="@string/no_regions"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center"
        android:visibility="gone"/>



</androidx.constraintlayout.widget.ConstraintLayout>
