package com.techcubics.albarkahyper.ui.adapters.profile

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.IRefreshListListener
import com.techcubics.albarkahyper.databinding.ItemAddressBinding
import com.techcubics.albarkahyper.ui.adapters.holders.profile.LocationViewHolder
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.data.model.pojo.Locations


class LocationsAdapter(
    private val context: Context,
    private var listOfAddresses: List<Locations>,
    private val locationItemLiveData: MutableLiveData<Locations?>,
    private val selectedDeleteLocationMutableLiveData: MutableLiveData<Locations?>,
    private val itempositionMutableLiveData: MutableLiveData<Int?>,
    private val vm: CartFragmentViewModel?,
    private val refresh: IRefreshListListener?,
    private var isCart: Boolean,
    private var selectedLocation: Locations?
) :
    RecyclerView.Adapter<LocationViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LocationViewHolder {

        val itemBinding =
            ItemAddressBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return LocationViewHolder(
            context,
            isCart,
            listOfAddresses,
            locationItemLiveData,
            selectedDeleteLocationMutableLiveData,
            itempositionMutableLiveData,
            vm,
            refresh,
            ::getSelectedAddressLocation,
            itemBinding,
            ::updateSelectedLocation
        )
    }


    override fun onBindViewHolder(holder: LocationViewHolder, position: Int) {
        holder.setData(listOfAddresses[position])

    }

    override fun getItemCount() = listOfAddresses.size




    @SuppressLint("NotifyDataSetChanged")
    fun updateLocations(locations: List<Locations>) {
        listOfAddresses = locations
        notifyDataSetChanged()
    }
    fun getSelectedAddressLocation():Locations?{
        return selectedLocation
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateSelectedLocation(selectedLocation: Locations?) {
        this.selectedLocation = selectedLocation
        notifyDataSetChanged()
    }

}
