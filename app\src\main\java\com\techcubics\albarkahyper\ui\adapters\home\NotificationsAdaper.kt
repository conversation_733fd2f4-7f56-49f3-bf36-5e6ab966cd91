package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.data.model.pojo.Notification
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.NotificationClickListener
import com.techcubics.albarkahyper.databinding.ItemNotificationBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.NotificationHolderItem


class NotificationsAdaper (val onClick: NotificationClickListener):  PagingAdapter() {

    lateinit var items:List<Notification>
    lateinit var itemBinding:ItemNotificationBinding

    fun setItemsList(_items:List<Notification>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationHolderItem {
        val itemBinding = ItemNotificationBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return NotificationHolderItem(itemBinding,parent.context,onClick)
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {

        super.onBindViewHolder(defaultHolder, position)
        var holder= defaultHolder as NotificationHolderItem
        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingItemCount(): Int {

        return items.size
    }

    override fun getPagingLayout(): Int {

        return R.layout.item_notification
    }




}