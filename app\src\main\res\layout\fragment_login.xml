<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:theme="@style/Theme.BestylishApp"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:layout_marginTop="100dp"
                android:orientation="vertical">

<!--                <ImageView-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="120dp"-->
<!--                    android:layout_marginTop="14dp"-->
<!--                    android:src="@drawable/portrait_signin_img" />-->

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/signin"
                    android:textAppearance="@style/header_auth" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style"
                    android:gravity="right"
                    android:layoutDirection="ltr"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <com.hbb20.CountryCodePicker
                        android:id="@+id/country_picker"
                        android:layout_width="wrap_content"
                        app:ccp_showNameCode="false"
                        android:layout_height="wrap_content"
                        app:ccp_defaultNameCode="EG" />

                    <EditText
                        android:id="@+id/email_or_phone_edt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/et_style"
                        android:gravity="center_vertical"
                        android:hint="@string/email_or_phone"
                        android:inputType="phone"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:textAppearance="@style/label_auth_edittext"
                        />
                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:boxStrokeWidth="0dp"
                    app:hintEnabled="false"
                    app:boxStrokeWidthFocused="0dp"
                    app:passwordToggleEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/password_edt"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:inputType="textPassword"
                        android:hint="@string/password"
                        android:background="@drawable/et_style"
                        android:padding="10dp"
                        android:textAppearance="@style/label_auth_edittext" />
                </com.google.android.material.textfield.TextInputLayout>

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="16dp" />

<!--                <TextView-->
<!--                    android:id="@+id/forget_password"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:background="?attr/selectableItemBackground"-->
<!--                    android:clickable="true"-->
<!--                    android:text="@string/forget_password"-->
<!--                    android:textAppearance="@style/label_auth" />-->

<!--                <RelativeLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_gravity="center_vertical|center_horizontal"-->
<!--                    android:layout_marginLeft="16dp"-->
<!--                    android:layout_marginTop="24dp"-->
<!--                    android:layout_marginRight="16dp">-->

<!--                    <View-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="2dp"-->
<!--                        android:layout_centerVertical="true"-->
<!--                        android:layout_toRightOf="@+id/sign_with"-->
<!--                        android:background="#E0E0E0" />-->

<!--                    <TextView-->
<!--                        android:id="@+id/sign_with"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_alignParentTop="true"-->
<!--                        android:layout_centerHorizontal="true"-->
<!--                        android:layout_marginLeft="13dp"-->
<!--                        android:layout_marginRight="13dp"-->
<!--                        android:background="@android:color/transparent"-->
<!--                        android:text="@string/signin_with"-->
<!--                        android:textAppearance="@style/label_auth" />-->

<!--                    <View-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="2dp"-->
<!--                        android:layout_centerVertical="true"-->
<!--                        android:layout_toLeftOf="@+id/sign_with"-->
<!--                        android:background="#E0E0E0" />-->
<!--                </RelativeLayout>-->

<!--                <include-->
<!--                    android:id="@+id/fb_sign_btn_progress"-->
<!--                    layout="@layout/btn_fb_progress"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="50dp"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:layout_marginEnd="16dp" />-->

<!--                <include-->
<!--                    android:id="@+id/google_sign_btn_progress"-->
<!--                    layout="@layout/btn_google_progress"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="50dp"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:layout_marginEnd="16dp" />-->
                <include
                    android:id="@+id/terms_conditions_layout"
                    layout="@layout/include_terms_and_conditions_layout"/>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/navto_signup_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="53dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:textColor="@color/color_gray_5"
                    android:text="@string/have_no_account"
                    android:textAppearance="@style/label_auth"
                    android:textSize="15.5sp"/>


            </LinearLayout>


        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/action_loading_animation"
            layout="@layout/include_action_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

