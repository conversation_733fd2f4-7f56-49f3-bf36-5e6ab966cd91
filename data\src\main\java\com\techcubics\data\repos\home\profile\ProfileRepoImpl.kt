package com.techcubics.data.repos.home.profile

import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.pojo.Locations
import com.techcubics.data.model.requests.profile.UpdateProfileRequest
import com.techcubics.data.model.requests.profile.location.AddLoactionRequest
import com.techcubics.data.model.requests.profile.location.UpdateLocationRequest
import com.techcubics.data.model.requests.profile.ContactusRequest
import com.techcubics.data.model.requests.profile.JoinusRequest
import com.techcubics.data.model.requests.profile.MyPurchaseRequset
import com.techcubics.data.model.requests.profile.location.UpdatePurchaseRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody


class ProfileRepoImpl(private val retrofitBuilder: RetrofitBuilder) : ProfileRepo, RepositoryResponse {

    override suspend fun getCountries(): BaseResponse<List<Countries>>? {
        try {
            val result = retrofitBuilder.getBindObject()?.getCountries()
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun getLanguages(): BaseResponse<List<Language>>? {
        try {
            val result = retrofitBuilder.getBindObject()?.languagesCall()
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun profileCall(): BaseResponse<User>? {
        try {
            val result = retrofitBuilder.getBindObject()?.profileCall()
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updateProfileCall(updateProfileRequest: UpdateProfileRequest): BaseResponse<User>? {

        val nameBody: RequestBody = updateProfileRequest.name.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val facilityNameBody: RequestBody = updateProfileRequest.facility_name.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val phoneBody: RequestBody = updateProfileRequest.phone.toRequestBody("text/plain;charset=utf-8".toMediaType())
        //photo
        val reqFile: RequestBody? =
            updateProfileRequest.avatar?.let { RequestBody.create("image/*".toMediaType(), it) }
        val body: MultipartBody.Part? = reqFile?.let {
            MultipartBody.Part.createFormData("avatar", updateProfileRequest.avatar?.name,
                it
            )
        }
        val countryIdBody: RequestBody = updateProfileRequest.country_id.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())
        val governerateIdBody: RequestBody = updateProfileRequest.governorate_id.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())
        val regionIdBody: RequestBody = updateProfileRequest.region_id.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())

        val addressBody: RequestBody = updateProfileRequest.address.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val latBody: RequestBody = updateProfileRequest.lat.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val lngBody: RequestBody = updateProfileRequest.lng.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val branchType: RequestBody = updateProfileRequest.branch_type.toRequestBody("text/plain;charset=utf-8".toMediaType())
        val countryCode: RequestBody = updateProfileRequest.country_code.toRequestBody("text/plain;charset=utf-8".toMediaType())

        try {
            val result = retrofitBuilder.getBindObject()?.updateProfileCall(nameBody,phoneBody,body
                ,addressBody,countryCode,facilityNameBody,branchType,latBody,lngBody,countryIdBody,governerateIdBody,regionIdBody)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun getGovernerateByCountryId(country_id: Int): BaseResponse<ArrayList<Governerate>>? {
        try {
            val result = retrofitBuilder.getBindObject()?.getGovernerateByCountryId(country_id)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getRegionBygovernorateId(
        governorate_id: Int,
        country_id: Int
    ): BaseResponse<ArrayList<Regoin>>? {

        try {
            val result =
                retrofitBuilder.getBindObject()?.getregionBygovernorateId(governorate_id, country_id)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }


    override suspend fun storeLocation(request : AddLoactionRequest): BaseResponse<String>? {
        try {

            val result = retrofitBuilder.getBindObject()?.storeLocation(request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun updateLocation(request: UpdateLocationRequest): BaseResponse<String>? {
        try {

            val result = retrofitBuilder.getBindObject()?.updateLocation(request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun deleteLocation(location_id: Int, country_id: Int, country_code: String): BaseResponse<String>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.deleteLocation(
                    location_id,
                    country_id,
                    country_code
                )
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun listLocation(country_id: Int, country_code: String): BaseResponse<ArrayList<Locations>>? {
        try {
            val result = retrofitBuilder.getBindObject()?.listLocation(country_id, country_code)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun showLocation(location_id: Int, country_id: Int, country_code: String): BaseResponse<Locations>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.showLocation(location_id, country_id, country_code)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun storeTypesCall(): BaseResponse<NearStoreTypes>? {
        try {
            val result = retrofitBuilder.getBindObject()?.storeTypesCall()
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun joinus(joinusRequest: JoinusRequest): BaseResponse<String>? {
        try {
            val result = retrofitBuilder.getBindObject()?.joinus(joinusRequest)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun contactUsCall(contactusRequest: ContactusRequest): BaseResponse<Nothing>? {
        try {
            val result = retrofitBuilder.getBindObject()
                ?.contactUsCall(contactusRequest)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun pagesCall(page_name: String): BaseResponse<PageData>? {
        try {
            val result = retrofitBuilder.getBindObject()?.pagesCall(page_name)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getCountriesAndDeliveryAreasByFurnitureId(furnitureId: Int, countryId: Int): BaseResponse<ArrayList<DeliveryAreaId>>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.getDeliveryAreasByFurnitureId(furnitureId, countryId)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun getGovernorateAreasByDeliveryId(deliveryAreaId: Int, countryId: Int): BaseResponse<ArrayList<Governerate>>? {
        try {
            val result =
                retrofitBuilder.getBindObject()
                    ?.getGovernorateAreasByDeliveryAreaId(deliveryAreaId, countryId)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun getRegionByGovernorateAndDeliveryAreaId(governorateId: Int, deliveryAreaId: Int, countryId: Int): BaseResponse<ArrayList<Regoin>>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.getRegionByGovernorateAndDeliveryAreaId(
                    governorateId,
                    deliveryAreaId,
                    countryId
                )
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun storePurchase(purchaseRequset: MyPurchaseRequset): BaseResponse<String>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.addPurchase(
                   purchaseRequset
                )
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updatePurchase(updatePurchaseRequest: UpdatePurchaseRequest): BaseResponse<String>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.updatePurchase(updatePurchaseRequest)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun deletePurchase(listing_id: Int): BaseResponse<String>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.deleteLocation(listing_id)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun listPurchase(date: String): BaseResponse<ArrayList<Mypurchase>>? {
        try {
            val result =
               retrofitBuilder.getBindObject()?.getPurchasesList(date)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun showPurchase(listing_id: Int): BaseResponse<Mypurchase>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.showPurchase(listing_id)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun markPurchaseAsBuying(listing_id: Int): BaseResponse<String>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.markPurchaseAsBuying(listing_id)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

}