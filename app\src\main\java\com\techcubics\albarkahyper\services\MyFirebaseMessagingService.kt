package com.techcubics.albarkahyper.services

import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import com.techcubics.albarkahyper.common.NotificationHelper
import com.techcubics.albarkahyper.common.NotificationTypes

class MyFirebaseMessagingService :  FirebaseMessagingService() {

    private  val TAG = "MyFirebaseMessagingServ"
    override fun onMessageReceived(remoteMessage: RemoteMessage) {


        var title:String=""
        var body:String=""
        var forStore:Boolean=false
        var object_id:Int?=null


        /* remoteMessage.notification?.let {
             Log.d(TAG, "onMessageReceivedNotification: ${it.toString()}")
             title=remoteMessage.notification!!.title.toString()
             body=remoteMessage.notification!!.body.toString()
         }*/

        remoteMessage.data?.let {
            Log.d(TAG, "onMessageReceivedData: ${it.toString()}")
            title=remoteMessage.data["title"].toString()
            body=remoteMessage.data["body"].toString()

            val info=remoteMessage.data.getValue("info")
            val gson = Gson()
            val convertedInfo = gson.fromJson(info, Info::class.java)


            NotificationHelper.show(this, NotificationObject(title,body,convertedInfo))
        }
    }
}

class Info(val notification_type:String,val shop_id:Int?=null)
class NotificationObject(val title:String,val body:String,val info:Info)