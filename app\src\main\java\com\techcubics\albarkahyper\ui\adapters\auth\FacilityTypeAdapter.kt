package com.techcubics.albarkahyper.ui.adapters.auth

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemFacilityTypeBinding

//class FacilityTypeAdapter(
//    val context: Context,
//    private var facilities: ArrayList<StoreTypes>,
//    val listener: FacilityListener
//) :
//    RecyclerView.Adapter<FacilityViewHolder>() {
//
//    var selectedFacility: Int = if (facilities.isNotEmpty()) facilities[0].branchTypeID else -1
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FacilityViewHolder {
//        val binding = ItemFacilityTypeBinding.inflate(LayoutInflater.from(context), parent, false)
//        return FacilityViewHolder(context, binding, listener)
//    }
//
//    override fun onBindViewHolder(holder: FacilityViewHolder, position: Int) {
//        holder.setData(facilities[position])
//    }
//
//    override fun getItemCount(): Int {
//        return facilities.size
//    }
//
//    fun updateList(facilities: ArrayList<StoreTypes>) {
//        notifyItemRangeRemoved(0, itemCount)
//        this.facilities = facilities
//        selectedFacility = if (facilities.isNotEmpty()) facilities[0].branchTypeID else -1
//        if (selectedFacility > -1 && selectedFacility == facilities[0].branchTypeID) {
//            listener.onFacilityChangeListener(facilities[0])
//        }
//        notifyItemRangeInserted(0, itemCount)
//    }
//}
//
//class FacilityViewHolder(
//    val context: Context,
//    val binding: ItemFacilityTypeBinding,
//    val listener: FacilityListener
//) :
//    RecyclerView.ViewHolder(binding.root) {
//    fun setData(facility: StoreTypes) {
//
//        binding.rdBtn.text = facility.name
//
//        Helper.loadImage(context, facility.image, binding.img)
//
//        binding.rdBtn.isChecked =
//            ((bindingAdapter as FacilityTypeAdapter).selectedFacility == facility.branchTypeID)
//
//
//        binding.rdBtn.setOnClickListener {
//            (bindingAdapter as FacilityTypeAdapter).selectedFacility = facility.branchTypeID
//            listener.onFacilityChangeListener(facility)
//            bindingAdapter?.notifyItemRangeChanged(0, bindingAdapter?.itemCount ?: 0)
//        }
//        binding.root.setOnClickListener {
//            (bindingAdapter as FacilityTypeAdapter).selectedFacility = facility.branchTypeID
//            listener.onFacilityChangeListener(facility)
//            bindingAdapter?.notifyItemRangeChanged(0, bindingAdapter?.itemCount ?: 0)
//        }
//    }
//}