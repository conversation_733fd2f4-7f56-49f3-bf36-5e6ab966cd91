package com.techcubics.albarkahyper.common

import android.content.Context
import android.util.Log
import android.view.View
import androidx.core.content.res.ResourcesCompat
import com.techcubics.albarkahyper.databinding.BtnProgressCircleBinding
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class CircleProgressButton(private val context: Context) {


    lateinit var  binding : BtnProgressCircleBinding

    fun initRoundButton(roundBinding : BtnProgressCircleBinding){
        binding = roundBinding
        binding.btnAdd.text = context.getString(R.string.add)
    }



    fun btnRoundActivated(){
        binding.progressBar2.visibility = View.VISIBLE
        binding.icon.visibility = View.GONE
        binding.btnAdd.visibility = View.INVISIBLE
    }



    fun btnRoundFinishedSuccessfully(){
        CoroutineScope(Dispatchers.Main).launch {
            binding.progressBar2.visibility = View.GONE
            binding.icon.visibility = View.VISIBLE
            binding.btnAdd.visibility = View.VISIBLE
        }

    }

    fun btnRoundFinishedFailed() {
//        CoroutineScope(Dispatchers.Main).launch {
//            binding.constraintsLayout.background = ResourcesCompat.getDrawable(context.resources,com.techcubics.style.R.drawable.btn_round_red,context.theme)
            binding.progressBar2.visibility = View.GONE
            binding.icon.visibility = View.VISIBLE
            binding.btnAdd.visibility = View.VISIBLE
//            delay(2000)
//            binding.constraintsLayout.background = ResourcesCompat.getDrawable(context.resources,com.techcubics.style.R.drawable.btn_style,context.theme)
//        }
    }

}