<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:elevation="1dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="6dp">


        <FrameLayout
            android:id="@+id/photoLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.mikhaellopez.circularimageview.CircularImageView
                android:id="@+id/photo"
                android:layout_width="@dimen/icons_size_height_weight_4"
                android:layout_height="@dimen/icons_size_height_weight_4"
                android:scaleType="centerCrop"
                tools:srcCompat="@tools:sample/avatars" />


            <TextView
                android:id="@+id/tvUnReadNo"
                style="@style/chat_room_unread"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="top|right"
                android:background="@drawable/circle_red"
                android:ellipsize="end"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="1" />
        </FrameLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/photoLayout"
            app:layout_constraintTop_toTopOf="@id/photoLayout">

            <TextView
                android:id="@+id/tvName"
                style="@style/chat_room_shopname"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                android:textAlignment="viewStart"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
              android:gravity="start"
                tools:text="Shop Nameeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee" />

            <TextView
                android:id="@+id/tvShortMessage"
                style="@style/chat_room_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:textAlignment="viewStart"
                android:maxLines="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvName"
                tools:text="Ok" />

            <TextView
                android:id="@+id/tvTimeDate"
                style="@style/chat_room_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"

                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOrderId"
                tools:text="05:0 PM" />

            <TextView
                android:id="@+id/tvOrderId"
                style="@style/chat_room_order_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/com_facebook_blue"
                android:textSize="12sp"
                android:textAlignment="viewStart"
                app:layout_constraintStart_toStartOf="@+id/tvShortMessage"
                app:layout_constraintTop_toBottomOf="@+id/tvShortMessage"
                tools:text="Order#1111111111111111111111" />
        </androidx.constraintlayout.widget.ConstraintLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>