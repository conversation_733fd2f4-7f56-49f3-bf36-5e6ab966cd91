package com.techcubics.albarkahyper.ui.adapters.holders.cart

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.Toast
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.common.IRefreshListListener
import com.techcubics.albarkahyper.databinding.ItemCartProductBinding
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.CartDetails
import com.techcubics.albarkahyper.common.QuantityButtonsController
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.style.R
import org.koin.android.ext.android.get


class CartProductViewHolder(
    private val context: Context,
    private var binding: ItemCartProductBinding,
    private var furnitureId: Int?,
    private val refreshCart: IRefreshListListener?,
    private val vm: CartFragmentViewModel
) : RecyclerView.ViewHolder(binding.root) {
    private var bottomSheetAlertDialog = BottomSheetAlertDialog()
    private val sharedPreferencesManager: SharedPreferencesManager by lazy {
        (context.getActivity())!!.get()
    }
    private lateinit var quantityButtonsController: QuantityButtonsController

    private lateinit var cartDetails: CartDetails

    fun setData(cartDetails: CartDetails) {
        this.cartDetails = cartDetails
        if (!cartDetails.status){
            binding.productStatusNote.visibility = View.VISIBLE
        }else{
            binding.productStatusNote.visibility = View.GONE
        }
        with(binding) {
            if (cartDetails.productImages.size > 0) {
                Helper.loadImage(context, cartDetails.productImages[0].path, binding.productImage)
            }
            productTitle.text = cartDetails.productName
            if (cartDetails.colorId != null) {
                Helper.customView(
                    color,
                    Color.parseColor(cartDetails.colorCode),
                    Color.parseColor(cartDetails.colorCode),
                    borderWidth = 1
                )
            } else {
                colorTitle.visibility = View.GONE
                color.visibility = View.GONE
            }

            ("${cartDetails.price.toString()} ${context.getString(R.string.currency_name)}").also {
                productPrice.text = it
            }
            if (cartDetails.sizeId != null) {
                size.text = cartDetails.sizeName
            } else {
                sizeTitle.visibility = View.GONE
                size.visibility = View.GONE
            }

            observers()
//            val maxQty = if (cartDetails.modelType == RateTypesEnum.Product.value) {
            binding.minOrderNote.visibility = View.VISIBLE
            cartDetails.maxOrderNum
//            } else {
//                binding.minOrderNote.visibility = View.GONE
//                Int.MAX_VALUE
//            }
            val
                    quantityButtonsController = QuantityButtonsController(
                binding.productQtyCartContainer.root,
                binding.minOrderNote,
                context,
                maxQty = cartDetails.maxOrderNum,
                minQty = cartDetails.minOrderNum
            )
            cartDetails.productId?.let {
                quantityButtonsController.setExtras(
                    isCart = true,
                    updateQuantity = ::updateQuantity
                )
            }
            quantityButtonsController.setQuantity()
            cartDetails.qty?.let { quantityButtonsController.updateQuantity(it) }
            quantityButtonsController.isBtnDecreaseEnabled()
            quantityButtonsController.isBtnIncreaseEnabled()



            btnRemove.setOnClickListener {
                if (cartDetails.colorId != null) {
                    vm.removeCartItem(
                        cartDetails.modelType,
                        cartDetails.modelId,
                        cartDetails.colorId

                    )
                } else {
                    vm.removeCartItem(
                        cartDetails.modelType,
                        cartDetails.modelId
                    )
                }

            }
        }
    }


    private fun observers() {
        bottomSheetAlertDialog.init(context)
        vm.cartItemRemovedResponse.observe(context as LifecycleOwner) { cart ->
            if (cart != null) {
                bottomSheetAlertDialog.showDialog(cart.message.toString())
                refreshCart?.onRefreshList()
                vm.cartItemRemovedResponse.value = null
            }
        }
        vm.addCartResponse.observe(context as LifecycleOwner) { updateResponse ->
            if (updateResponse != null && updateResponse.message.toString() != Constants.SERVER_ERROR) {
                if (updateResponse.status == true) {
                    Toast.makeText(
                        context,
                        context.getString(R.string.cart_updated),
                        Toast.LENGTH_SHORT
                    )
                        .show()
                } else {
                    bottomSheetAlertDialog.showDialog(updateResponse.message.toString())
                }
                refreshCart?.onRefreshList()
                vm.addCartResponse.value = null
            } else if (updateResponse?.message.toString().contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    context,
                    context.getString(R.string.server_error)
                )
            }
        }
    }

    private fun updateQuantity(
        qty: Int,
    ) {
        val pathEndPoint = when (cartDetails.modelType) {
            RateTypesEnum.Save.value -> EndPointConstants.add_save
            RateTypesEnum.Offer.value -> EndPointConstants.add_offer
            RateTypesEnum.Discount.value -> EndPointConstants.add_discount
            else -> {
                EndPointConstants.add_product
            }
        }
        binding.productQtyCartContainer.productQuantity.text = qty.toString()
        vm.updateCartProduct(
            pathEndPoint,
            furnitureId,
            cartDetails.modelType,
            cartDetails.modelId, qty
        )
    }
}