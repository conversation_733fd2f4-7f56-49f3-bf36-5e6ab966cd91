package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreDetailsMenuData
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.databinding.ItemStoreCategoryProductsBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.StoreCategoryProductsHolderItem
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler


class StoreCategoryProductsAdapter (val margin:Int=0, val onFavClickListener: IFavClickListener,val onClickHandler: IOnAdapterItemClickHandler): RecyclerView.Adapter<StoreCategoryProductsHolderItem>() {

    lateinit var items:List<StoreDetailsMenuData>

    fun setItemsList(_items:List<StoreDetailsMenuData>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StoreCategoryProductsHolderItem {
        val itemBinding = ItemStoreCategoryProductsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return StoreCategoryProductsHolderItem(itemBinding,parent.context,margin=margin,onFavClickListener=onFavClickListener, onClickHandler)
    }

    override fun onBindViewHolder(holder: StoreCategoryProductsHolderItem, position: Int) {

        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
        return items.size
    }
}