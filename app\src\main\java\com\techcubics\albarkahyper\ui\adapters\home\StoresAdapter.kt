package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.databinding.ItemStoreBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.StoreHolderItem


class StoresAdapter<T>(val onFavClickListener: IFavClickListener?=null): PagingAdapter() {

    lateinit var items:List<T>
    lateinit var itemBinding: ItemStoreBinding
    private  val TAG = "StoresAdapter"
    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StoreHolderItem<T> {
        itemBinding = ItemStoreBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return StoreHolderItem(itemBinding,parent.context,onFavClickListener!!)
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as StoreHolderItem<T>
        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_store
    }

    override fun getPagingItemCount(): Int {
        return items.size
    }



}