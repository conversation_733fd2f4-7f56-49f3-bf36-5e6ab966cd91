package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class OrderMessageHistoryData(
    @SerializedName("sender_id")
    val sender_id:Int?=null,
    @SerializedName("receiver_id")
    val receiver_id:Int?=null,
    @SerializedName("user_id")
    val user_id:Int?=null,
    @SerializedName("read")
    val read:Int?=null,
    @SerializedName("message")
    val message:String,
    @SerializedName("date")
    val date:String,
    @SerializedName("time")
    val time:String,
    @SerializedName("format_date")
    val formatDate:String?=null,
    @SerializedName("last_message")
    val lastMessage:String?=null,
    @SerializedName("count_unread")
    val countUnread:Int?=null,

)