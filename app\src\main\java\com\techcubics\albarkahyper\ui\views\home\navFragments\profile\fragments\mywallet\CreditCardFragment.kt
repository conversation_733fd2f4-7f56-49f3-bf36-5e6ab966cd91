package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mywallet

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentCreditCardBinding
import com.techcubics.albarkahyper.common.ProgressButton


class CreditCardFragment : Fragment() {

    lateinit var binding : FragmentCreditCardBinding
    lateinit var progressButton: ProgressButton
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCreditCardBinding.inflate(layoutInflater,container,false)
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.signBtnProgress)
        initViews()
        return binding.root
    }

    private fun initViews() {
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.credit_card)
        progressButton.binding.textView.text = getString(com.techcubics.style.R.string.ok)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }

    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}