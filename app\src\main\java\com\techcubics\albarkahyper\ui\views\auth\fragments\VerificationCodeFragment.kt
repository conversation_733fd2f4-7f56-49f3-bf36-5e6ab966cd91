package com.techcubics.albarkahyper.ui.views.auth.fragments

import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.google.firebase.FirebaseException
import com.google.firebase.FirebaseTooManyRequestsException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthProvider
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentVerficationCodeBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.shared.constants.Constants
import org.koin.android.ext.android.inject
import java.util.*
import java.util.concurrent.TimeUnit


class VerificationCodeFragment : Fragment() {


    lateinit var binding : FragmentVerficationCodeBinding
    private var verificationId : String? = ""
    private var mobile : String? = ""
    private lateinit var timer: CountDownTimer
    private lateinit var progressBtn : ProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentVerficationCodeBinding.inflate(inflater,container,false)
        progressBtn = ProgressButton(requireContext())
        progressBtn.init(binding.signBtnProgress)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        verificationId = arguments?.getString(Constants.VERFICATION_ID)
        mobile = arguments?.getString(Constants.MOBILE)
        Log.i("clothes",verificationId+" "+mobile)
        configOtpEditText( binding.inputCode1, binding.inputCode2, binding.inputCode3, binding.inputCode4, binding.inputCode5, binding.inputCode6 )
        initViews()
        bottomSheetAlertDialog.init(requireContext())
        return binding.root
    }

    private fun initViews() {

        progressBtn.binding.textView.text = getString(com.techcubics.style.R.string.confirm)
        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            if( binding.inputCode1.text.toString().isEmpty() || binding.inputCode2.text.toString().isEmpty()|| binding.inputCode3.text.toString().isEmpty()
                || binding.inputCode4.text.toString().isEmpty() || binding.inputCode5.text.toString().isEmpty()|| binding.inputCode6.text.toString().isEmpty())
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.entervalidotp))
            else{
                progressBtn.btnActivated()
                val code = binding.inputCode1.text.toString()+binding.inputCode2.text.toString()+binding.inputCode3.text.toString()+binding.inputCode4.text.toString()+binding.inputCode5.text.toString()+binding.inputCode6.text.toString()
                Log.i("here",code)
                Log.i("here", "from vcf : $verificationId")
                if (verificationId != null){
                    val phoneAuthCred = PhoneAuthProvider.getCredential(verificationId!!,code)
                    FirebaseAuth.getInstance().signInWithCredential(phoneAuthCred).addOnCompleteListener {
                        if(it.isSuccessful){
                            progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.confirm),null)
                            findNavController().navigate(R.id.action_verficationCodeFragment_to_newPasswordFragment)
                        }else{
                            progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.confirm),null)
                            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.entervalidotp))
                        }
                    }
                }

            }

        }

        val duration = TimeUnit.MINUTES.toMillis(1)
        timer =  object : CountDownTimer(duration,1000){
            override fun onTick(remaining: Long) {
                val sDuration = String.format(Locale.ENGLISH,"%02d : %02d"
                    ,TimeUnit.MILLISECONDS.toMinutes(remaining)
                    ,TimeUnit.MILLISECONDS.toSeconds(remaining) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(remaining)))
                binding.countDown.text = sDuration
            }

            override fun onFinish() {
                binding.countDown.visibility = View.GONE
                binding.send.visibility = View.VISIBLE
            }
        }
        binding.send.setOnClickListener {
            resendCode()
        }
    }

    private fun resendCode() {
        progressBtn.btnActivated()

        val callbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {

            override fun onVerificationCompleted(credential: PhoneAuthCredential) {

                progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.confirm),null)
            }

            override fun onVerificationFailed(e: FirebaseException) {
                progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.confirm),null)
                Log.i("here",e.message.toString())
                if (e is FirebaseAuthInvalidCredentialsException) {
                    // Invalid request
                    Helper.ShowErrorDialog(requireContext(),e.message.toString())
                    Log.i("here",e.message.toString())
                } else if (e is FirebaseTooManyRequestsException) {
                    // The SMS quota for the project has been exceeded
                    Helper.ShowErrorDialog(requireContext(),e.message.toString())
                    Log.i("here",e.message.toString())
                }
            }

            override fun onCodeSent(newVerificationId: String, token: PhoneAuthProvider.ForceResendingToken) {
                progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.confirm),null)
                verificationId = newVerificationId
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.code_is_sent))
            }
        }

        PhoneAuthProvider.getInstance().verifyPhoneNumber(
            sharedPreferencesManager.getMobileCode()+ mobile,
            60,
            TimeUnit.SECONDS,
            requireActivity(),
            callbacks
        )
    }

    private fun configOtpEditText(vararg etList: EditText) {
        val afterTextChanged = { index: Int, e: Editable? ->
            val view = etList[index]
            val text = e.toString()

            when (view.id) {
                // first text changed
                etList[0].id -> {
                    if (text.isNotEmpty()) etList[index + 1].requestFocus()
                }

                // las text changed
                etList[etList.size - 1].id -> {
                    if (text.isEmpty()) etList[index - 1].requestFocus()
                }

                // middle text changes
                else -> {
                    if (text.isNotEmpty()) etList[index + 1].requestFocus()
                    else etList[index - 1].requestFocus()
                }
            }
            false
        }
        etList.forEachIndexed { index, editText ->
            editText.doAfterTextChanged { afterTextChanged(index, it) }
        }
    }

    override fun onStart() {
        timer.start()
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    override fun onDestroy() {
        timer.cancel()
        super.onDestroy()
    }


}