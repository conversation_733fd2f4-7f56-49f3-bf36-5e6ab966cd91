<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment">

    <fragment
        android:id="@+id/productsFilterFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.ProductsFilterFragment"
        android:label="fragment_products_filter"
        tools:layout="@layout/fragment_products_filter">
        <action
            android:id="@+id/productsFilterFragment_selectCategory"
            app:destination="@id/selectCategoryInFilterFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>

        <action
            android:id="@+id/productsFilterFragment_selectColor"
            app:destination="@id/selectColorInFilterFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>


        <action
            android:id="@+id/productsFilterFragment_selectSize"
            app:destination="@id/selectSizeInFilterFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
        <action
            android:id="@+id/view_areaFilter"
            app:destination="@id/productAreasFilterFragment" />
    </fragment>

    <fragment
        android:id="@+id/selectCategoryInFilterFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.SelectCategoryInFilterFragment"
        android:label="SelectCategoryInFilterFragment" />
    <fragment
        android:id="@+id/selectColorInFilterFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.SelectColorInFilterFragment"
        android:label="SelectColorInFilterFragment" />
    <fragment
        android:id="@+id/selectSizeInFilterFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.SelectSizeInFilterFragment"
        android:label="SelectSizeInFilterFragment" />
    <fragment
        android:id="@+id/productSearchWordResultFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.ProductSearchWordResultFragment"
        android:label="fragment_product_search_word_result"
        tools:layout="@layout/fragment_product_search_word_result">
        <action
            android:id="@+id/view_product_details_from_search_word"
            app:destination="@id/productDetailsFragment" />
        <action
            android:id="@+id/action_productSearchWordResultFragment_to_productsFilterFragment"
            app:destination="@id/productsFilterFragment" />
    </fragment>

    <fragment
        android:id="@+id/productDetailsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.details.fragments.ProductDetailsFragment"
        android:label="fragment_product_details"
        tools:layout="@layout/fragment_product_details">
        <action android:id="@+id/product_details_to_login"
            app:destination="@id/loginFragment"/>
        <action
            android:id="@+id/action_productDetailsFragment3_to_youtubeFullScreenFragment3"
            app:destination="@id/youtubeFullScreenFragment" />
        <action android:id="@+id/view_ratings"
            app:destination="@id/ratingFragment"/>

    </fragment>

    <fragment
        android:id="@+id/youtubeFullScreenFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.details.fragments.YoutubeFullScreenFragment"
        android:label="YoutubeFullScreenFragment" />

    <fragment
        android:id="@+id/productSearchCategoryResultFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.ProductSearchCategoryResultFragment"
        android:label="fragment_product_search_category_result"
        tools:layout="@layout/fragment_product_search_category_result">
        <action
            android:id="@+id/view_productDetails"
            app:destination="@id/productDetailsFragment" />
        <action
            android:id="@+id/view_discountDetails"
            app:destination="@id/discountDetailsFragment" />
        <action
            android:id="@+id/action_productSearchCategoryResultFragment_to_findInStoreFragment"
            app:destination="@id/findInStoreFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>


    </fragment>

    <action android:id="@+id/view_cart"
        app:destination="@id/cartFragment"/>

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.home.fragments.HomeFragment"
        android:label="fragment_home"
        tools:layout="@layout/fragment_home" >
        <action
            android:id="@+id/hometologin"
            app:destination="@+id/loginFragment"
            app:popUpTo="@id/homeFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/hometoself"
            app:destination="@+id/homeFragment"/>
        <action
            android:id="@+id/action_homeFragment_to_profileFragment"
            app:destination="@id/profileFragment" />
        <action
            android:id="@+id/action_homeFragment_to_discountsFragment"
            app:destination="@id/discountsFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
        <action
            android:id="@+id/action_homeFragment_to_discountDetailsFragment"
            app:destination="@id/discountDetailsFragment" />
        <action
            android:id="@+id/view_offerDetails"
            app:destination="@id/offerDetailsFragment" />
        <action
            android:id="@+id/view_saveDetails"
            app:destination="@id/saveDetailsFragment" />
        <action
            android:id="@+id/action_homeFragment_to_notificationsFragment"
            app:destination="@id/notificationsFragment" />

        <action
            android:id="@+id/startResultForCategory"
            app:destination="@id/storeSearchCategoryResultFragment" />
        <action
            android:id="@+id/StartResultForNearBy"
            app:destination="@id/storeSearchNearByResultFragment" />
        <action
            android:id="@+id/display_products_by_search_word"
            app:destination="@id/productSearchWordResultFragment" />
        <action
            android:id="@+id/display_products_by_category"
            app:destination="@id/productSearchCategoryResultFragment" />
        <action
            android:id="@+id/viewmore_categories"
            app:destination="@id/categoriesFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
        <action
            android:id="@+id/view_offersFragment"
            app:destination="@id/offersFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
        <action
            android:id="@+id/view_savesFragment"
            app:destination="@id/savesFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
        <action
            android:id="@+id/action_homeFragment_to_store"
            app:destination="@id/storeSearchCategoryResultFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
    </fragment>

    <fragment
        android:id="@+id/favoritesFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.fragments.FavoritesFragment"
        android:label="fragment_favorites"
        tools:layout="@layout/fragment_favorites" >
        <action
            android:id="@+id/view_productDetails"
            app:destination="@id/productDetailsFragment" />
         <action
            android:id="@+id/view_discountDetails"
            app:destination="@id/discountDetailsFragment" />
        <action
            android:id="@+id/fav_to_login"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/view_storeMainCategories"
            app:destination="@id/mainCategoriesFragment" />
    </fragment>

    <fragment
        android:id="@+id/cartFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.cart.fragments.CartFragment"
        android:label="fragment_cart"
        tools:layout="@layout/fragment_cart" >
        <action android:id="@+id/carttologin"
            app:destination="@id/loginFragment"/>
        <action android:id="@+id/open_payment_methods"
            app:destination="@id/paymentInCartFragment"/>
        <action
            android:id="@+id/action_cartFragment_to_addAddressFragment"
            app:destination="@id/addAddressFragment" />
        <action
            android:id="@+id/action_cartFragment_to_addAddressFragment2"
            app:destination="@id/addAddressFragment" />
    </fragment>

    <fragment
        android:id="@+id/ordersFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.orders.history.OrdersFragment"
        android:label="fragment_orders"
        tools:layout="@layout/fragment_orders" >
        <action
            android:id="@+id/action_ordersFragment_to_orderDetails"
            app:destination="@id/myOrderDetailsFragment" />
        <action
            android:id="@+id/ordertologin"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_ordersFragment_to_home"
            app:destination="@id/homeFragment" />
    </fragment>


    <fragment
        android:id="@+id/profileFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.ProfileFragment"
        android:label="fragment_profile"
        tools:layout="@layout/fragment_profile" >

        <action
            android:id="@+id/action_profileFragment_to_setting"
            app:destination="@id/settingFragment" />
        <action
            android:id="@+id/action_profileFragment_chattabs"
            app:destination="@id/chatTabsFragment" />
        <action
            android:id="@+id/action_profileFragment_to_updateProfileFragment"
            app:destination="@id/updateProfileFragment" />
        <action
            android:id="@+id/action_profileFragment_to_myAddressesFragment"
            app:destination="@id/myAddressesFragment" />
        <action
            android:id="@+id/action_profileFragment_to_myWalletFragment"
            app:destination="@id/myWalletFragment" />

        <action
            android:id="@+id/action_profileFragment_to_paymentInCartFragment"
            app:destination="@id/paymentInCartFragment" />
        <action
            android:id="@+id/action_profileFragment_loginFragment"
            app:destination="@id/loginFragment"
            app:popUpTo="@id/profileFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/view_chatRoomsFragment"
            app:destination="@id/chatRoomsFragment" />
        <action
            android:id="@+id/view_supportChatHistoryFragment"
            app:destination="@id/supportChatHistoryFragment" />

        <action
            android:id="@+id/action_profileFragment_purchasesFragment"
            app:destination="@id/myPurchasesFragment" />
    </fragment>



    <fragment
        android:id="@+id/loginFragment"
        android:name="com.techcubics.albarkahyper.ui.views.auth.fragments.LoginFragment"
        android:label="LoginFragment">
        <action
            android:id="@+id/action_loginFragment_to_signUpFragment"
            app:destination="@id/signUpFragment" />
        <action
            android:id="@+id/action_loginFragment_to_forgetPasswordFragment"
            app:destination="@id/forgetPasswordFragment" />
        <action
            android:id="@+id/action_loginFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/loginFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/signUpFragment"
        android:name="com.techcubics.albarkahyper.ui.views.auth.fragments.register.SignUpFragment"
        android:label="SignUpFragment" >
        <action
            android:id="@+id/action_signUpFragment_to_loginFragment"
            app:destination="@id/loginFragment" />
        <action
            android:id="@+id/action_signupFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/loginFragment"
            app:popUpToInclusive="true"/>
        <action
            android:id="@+id/move_to_stepper"
            app:destination="@id/register_stepps_nav_graph"
            app:popUpTo="@id/loginFragment"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/forgetPasswordFragment"
        android:name="com.techcubics.albarkahyper.ui.views.auth.fragments.ForgetPasswordFragment"
        android:label="ForgetPasswordFragment" >
        <action
            android:id="@+id/action_forgetPasswordFragment_to_verficationCodeFragment"
            app:destination="@id/verficationCodeFragment"
            />
        <action
            android:id="@+id/action_forgetPasswordFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:popUpTo="@id/forgetPasswordFragment"
            app:popUpToInclusive="true" />

    </fragment>

    <fragment
        android:id="@+id/newPasswordFragment"
        android:name="com.techcubics.albarkahyper.ui.views.auth.fragments.NewPasswordFragment"
        android:label="NewPasswordFragment"
        >
        <action
            android:id="@+id/action_newPasswordFragment_to_loginFragment"
            app:destination="@id/loginFragment"
            app:popUpTo="@id/newPasswordFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/verficationCodeFragment"
        android:name="com.techcubics.albarkahyper.ui.views.auth.fragments.VerificationCodeFragment"
        android:label="VerficationCodeFragment" >
        <action
            android:id="@+id/action_verficationCodeFragment_to_newPasswordFragment"
            app:destination="@id/newPasswordFragment"
            app:popUpTo="@id/forgetPasswordFragment"
            app:popUpToInclusive="true" />
    </fragment>


    <fragment
        android:id="@+id/updateProfileFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.updateprofile.UpdateProfileFragment"
        android:label="UpdateProfileFragment" >
        <action
            android:id="@+id/action_updateProfileFragment_to_profileFragment"
            app:destination="@id/profileFragment" />
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/myAddressesFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.addresses.MyAddressesFragment"
        android:label="MyAddressesFragment" >
        <action
            android:id="@+id/action_myAddressesFragment_to_addAddressFragment"
            app:destination="@id/addAddressFragment" />
        <action
            android:id="@+id/action_myAddressesFragment_self"
            app:destination="@id/myAddressesFragment" />
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/addAddressFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.addresses.AddAddressFragment"
        android:label="fragment_add_address"
        tools:layout="@layout/fragment_add_address" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/myWalletFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mywallet.MyWalletFragment"
        android:label="MyWalletFragment" >
        <action
            android:id="@+id/action_myWalletFragment_to_creditCardFragment"
            app:destination="@id/creditCardFragment" />
        <action
            android:id="@+id/action_myWalletFragment_to_vodafonCashFragment"
            app:destination="@id/vodafonCashFragment" />
    </fragment>

    <fragment
        android:id="@+id/creditCardFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mywallet.CreditCardFragment"
        android:label="CreditCardFragment" />

    <fragment
        android:id="@+id/vodafonCashFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mywallet.VodafonCashFragment"
        android:label="VodafonCashFragment" />

    <fragment
        android:id="@+id/joinUsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.joinus.JoinUsFragment"
        android:label="JoinUsFragment">
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/callUsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.callus.CallUsFragment"
        android:label="CallUsFragment" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/termsAndConditionsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.terms_conditions.TermsAndConditionsFragment"
        android:label="fragment_terms_and_conditions"
        tools:layout="@layout/fragment_terms_and_conditions" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/rateAppFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.rate_app.RateAppFragment"
        android:label="fragment_rate_app"
        tools:layout="@layout/fragment_rate_app" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/privacyPolicyFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.share_app.ShareAppFragment"
        android:label="fragment_privacy_policy"
        tools:layout="@layout/fragment_share_app" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/aboutUsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.aboutus.AboutUsFragment"
        android:label="fragment_about_us"
        tools:layout="@layout/fragment_about_us" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/paymentInCartFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.orders.PaymentInCartFragment"
        android:label="PaymentInCartFragment" >
        <action
            android:id="@+id/action_paymentInCartFragment_to_myOrderDetailsFragment"
            app:destination="@id/myOrderDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/myOrderDetailsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.orders.orderdetails.MyOrderDetailsFragment"
        android:label="fragment_my_order_details"
        tools:layout="@layout/fragment_my_order_details" >
        <action
            android:id="@+id/action_orderDetails_to_homeFragment"
            app:destination="@id/homeFragment"
             />
        <action
            android:id="@+id/action_orderDetails_to_orderFragment"
            app:destination="@id/ordersFragment"
            />
        <action
            android:id="@+id/view_orderChatHistoryFragment"
            app:destination="@id/orderChatHistoryFragment" />
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>

    <fragment
        android:id="@+id/discountsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments.DiscountsFragment"
        android:label="fragment_discounts"
        tools:layout="@layout/fragment_discounts">
        <action
            android:id="@+id/action_discountsFragment_to_discountDetailsFragment"
            app:destination="@id/discountDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/discountDetailsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.details.fragments.DiscountDetailsFragment"
        android:label="fragment_discount_details"
        tools:layout="@layout/fragment_discount_details" >
        <action android:id="@+id/discount_details_to_login"
            app:destination="@id/loginFragment"/>

    </fragment>

    <dialog
        android:id="@+id/notificationsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments.NotificationsFragment"
        android:label="fragment_notifications"
        tools:layout="@layout/fragment_notifications" >
        <action
            android:id="@+id/action_notificationsFragment_to_homeFragment"
            app:destination="@id/homeFragment" />
        <argument
            android:name="refresh"
            app:argType="com.techcubics.albarkahyper.common.IItemClickListener" />
        <action
            android:id="@+id/action_notificationsFragment_to_mainCategoriesFragment"
            app:destination="@id/mainCategoriesFragment" />
    </dialog>




    <fragment
        android:id="@+id/storeSearchCategoryResultFragment"
        android:name="com.techcubics.albarkahyper.ui.views.stores.searchResult.StoreSearchCategoryResultFragment"
        android:label="fragment_store_search_category_result"
        tools:layout="@layout/fragment_store_search_category_result" >
        <action
            android:id="@+id/view_storeMainCategories"
            app:destination="@id/mainCategoriesFragment" />
    </fragment>

    <fragment
        android:id="@+id/storeSearchNearByResultFragment"
        android:name="com.techcubics.albarkahyper.ui.views.stores.searchResult.StoreSearchNearByResultFragment"
        android:label="fragment_store_search_near_by_result"
        tools:layout="@layout/fragment_store_search_near_by_result" >

    </fragment>




    <fragment
        android:id="@+id/categoriesFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments.CategoriesFragment"
        android:label="fragment_categories"
        tools:layout="@layout/fragment_categories" >
        <action
            android:id="@+id/display_products_by_category"
            app:destination="@id/productSearchCategoryResultFragment" />
    </fragment>
    <fragment
        android:id="@+id/offersFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments.OffersFragment"
        android:label="fragment_offers"
        tools:layout="@layout/fragment_offers" >


        <action android:id="@+id/view_offerDetails"
            app:destination="@id/offerDetailsFragment"/>
    </fragment>
    <fragment
        android:id="@+id/savesFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments.SavesFragment"
        android:label="fragment_saves"
        tools:layout="@layout/fragment_saves" >


        <action android:id="@+id/view_saveDetails"
            app:destination="@id/saveDetailsFragment"/>

    </fragment>


    <fragment
        android:id="@+id/offerDetailsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.details.fragments.OfferDetailsFragment"
        android:label="OfferDetailsFragment" >
        <action android:id="@+id/offer_details_to_login"
            app:destination="@id/loginFragment"/>

    </fragment>
    <fragment
        android:id="@+id/saveDetailsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.details.fragments.SaveDetailsFragment"
        android:label="SaveDetailsFragment" >
        <action android:id="@+id/save_details_to_login"
            app:destination="@id/loginFragment"/>

    </fragment>
    <fragment
        android:id="@+id/myPurchasesFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mypurchases.MyPurchasesFragment"
        android:label="fragment_my_purchases"
        tools:layout="@layout/fragment_my_purchases" >
        <action
            android:id="@+id/goToLogin"
            app:destination="@id/loginFragment" />
    </fragment>
    <action android:id="@+id/go_to_login"
        app:destination="@id/loginFragment"/>
    <fragment
        android:id="@+id/ratingFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.details.fragments.RatingFragment"
        android:label="fragment_rating"
        tools:layout="@layout/fragment_rating" >
        <argument
            android:name="rates"
            app:argType="com.techcubics.data.model.pojo.Rates[]" />
    </fragment>

    <fragment
        android:id="@+id/mainCategoriesFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.MainCategoriesFragment"
        android:label="fragment_main_categories"
        tools:layout="@layout/fragment_main_categories" >
        <action
            android:id="@+id/view_productDetails"
            app:destination="@id/productDetailsFragment" />
        <action
            android:id="@+id/view_discountDetails"
            app:destination="@id/discountDetailsFragment" />

        <action
            android:id="@+id/action_mainCategoriesFragment_to_productSearchCategoryResultFragment"
            app:destination="@id/productSearchCategoryResultFragment" />
        <action
            android:id="@+id/action_mainCategoriesFragment_to_productSearchWordResultFragment"
            app:destination="@id/productSearchWordResultFragment"
            app:enterAnim="@anim/from_bottom"
            app:popExitAnim="@anim/to_bottom"/>
        <action
            android:id="@+id/view_generalStoreSearch"
            app:destination="@id/findInStoreFragment"/>
    </fragment>


    <include app:graph="@navigation/register_stepps_nav_graph" />
    <fragment
        android:id="@+id/productAreasFilterFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.ProductAreasFilterFragment"
        android:label="fragment_product_areas_filter"
        tools:layout="@layout/fragment_product_areas_filter" >
        <action
            android:id="@+id/view_regionFilter"
            app:destination="@id/productRegionFilterFragment"
            app:popUpTo="@id/productAreasFilterFragment"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/productRegionFilterFragment"
        android:name="com.techcubics.albarkahyper.ui.views.products.searchResult.ProductRegionFilterFragment"
        android:label="ProductRegionFilterFragment" >
        <argument
            android:name="searchAreaRequest"
            app:argType="com.techcubics.data.model.requests.SearchAreaRequest" />
    </fragment>


    <!--Chat -->
    <fragment
        android:id="@+id/chatTabsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.chat.fragments.ChatTabsFragment"
        android:label="ChatTabsFragment" >

        <action
            android:id="@+id/chatTabsFragment_to_orderChatHistoryFragment"
            app:destination="@id/orderChatHistoryFragment"
            />

        <action
            android:id="@+id/chatTabsFragment_to_liveChatFragment"
            app:destination="@id/liveChatFragment" />
    </fragment>

      <!--Order Chat -->
    <fragment
        android:id="@+id/chatRoomsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat.ChatRoomsFragment"
        android:label="fragment_chat_rooms"
        tools:layout="@layout/fragment_chat_rooms">

    </fragment>
    <fragment
        android:id="@+id/orderChatHistoryFragment"
        android:name="com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat.OrderChatHistoryFragment"
        android:label="OrderChatHistoryFragment">

        <argument
            android:name="refresh"
            app:nullable="true"
            app:argType="com.techcubics.albarkahyper.common.IItemClickListener" />
        <argument
            android:name="id"
            app:argType="integer" />
        <argument
            android:name="name"
            app:argType="string" />
    </fragment>
     <!--Live Chat -->
    <fragment
        android:id="@+id/liveChatRoomsFragment"
        android:name="com.techcubics.albarkahyper.ui.views.chat.fragments.livechat.LiveChatRoomsFragment"
        android:label="fragment_live_chat_rooms"
        tools:layout="@layout/fragment_live_chat_rooms" />
    <fragment
        android:id="@+id/liveChatFragment"
        android:name="com.techcubics.albarkahyper.ui.views.chat.fragments.livechat.LiveChatFragment"
        android:label="fragment_live_chat"
        tools:layout="@layout/fragment_live_chat">
        <argument
            android:name="refresh"
            app:nullable="true"
            app:argType="com.techcubics.albarkahyper.common.IItemClickListener" />
        <argument
            android:name="id"
            app:argType="integer" />
        <argument
            android:name="name"
            app:argType="string" />
        <argument
            android:name="url"
            app:argType="string" />
    </fragment>

     <!--Support Chat -->
    <fragment
        android:id="@+id/supportChatHistoryFragment"
        android:name="com.techcubics.albarkahyper.ui.views.chat.fragments.supportchat.SupportChatHistoryFragment"
        android:label="SupportChatHistoryFragment" />

    <fragment
        android:id="@+id/findInStoreFragment"
        android:name="com.techcubics.albarkahyper.ui.views.stores.details.FindInStoreFragment"
        android:label="fragment_find_in_store"
        tools:layout="@layout/fragment_find_in_store" >
        <action
            android:id="@+id/view_productDetails"
            app:destination="@id/productDetailsFragment"
            />
        <action
            android:id="@+id/view_discountDetails"
            app:destination="@id/discountDetailsFragment"
            />
        <action android:id="@+id/view_cart"
            app:destination="@id/cartFragment"/>
    </fragment>
    <fragment
        android:id="@+id/settingFragment"
        android:name="com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.setting.SettingFragment"
        android:label="SettingFragment" >
        <action
            android:id="@+id/action_settingFragment_to_callUsFragment"
            app:destination="@id/callUsFragment" />
        <action
            android:id="@+id/action_settingFragment_to_aboutUsFragment"
            app:destination="@id/aboutUsFragment" />
        <action
            android:id="@+id/action_settingFragment_to_privacyPolicyFragment"
            app:destination="@id/privacyPolicyFragment" />
        <action
            android:id="@+id/action_settingFragment_to_rateAppFragment"
            app:destination="@id/rateAppFragment" />
        <action
            android:id="@+id/action_settingFragment_to_termsAndConditionsFragment"
            app:destination="@id/termsAndConditionsFragment" />
    </fragment>

</navigation>