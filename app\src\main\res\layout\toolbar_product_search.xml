<?xml version="1.0" encoding="utf-8"?>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
        xmlns:tools="http://schemas.android.com/tools"
       >
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/et_searchview"
            >

            <EditText
                android:id="@+id/txtSearchWord"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_search"
                android:drawablePadding="10dp"
                android:hint="@string/hint_write_product_name"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:background="@null"
                style="@style/toolbar_edittext_search"
                android:layout_marginEnd="16dp"

                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btnFilter" />


            <ImageButton
                android:id="@+id/btnFilter"
                android:layout_width="@dimen/icons_size_height_weight_9"
                android:layout_height="@dimen/icons_size_height_weight_9"
                android:layout_marginEnd="10dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:scaleType="centerInside"
                android:src="@drawable/ic_filter"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />






        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>












