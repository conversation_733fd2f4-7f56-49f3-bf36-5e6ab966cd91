package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class Settings(
    @SerializedName("icon")
    val icon:String,
    @SerializedName("web_logo")
    val webLogo: String,
    @SerializedName("mobile_logo")
    val mobileLogo: String,
    @SerializedName("favicon")
    val favIcon: String,
    @SerializedName("company_email")
    val companyEmail: String,
    @SerializedName("phone")
    val phone: String,
    @SerializedName("country")
    val country: String,
    @SerializedName("city")
    val city: String,
    @SerializedName("state")
    val state: String,
    @SerializedName("address")
    val address: String,
    @SerializedName("business_address_1")
    val businessAddress1: String,
    @SerializedName("business_address_2")
    val businessAddress2: String,
    @SerializedName("timezone")
    val timezone: String,
    @SerializedName("pagination_limit")
    val paginationlimit: String,
    @SerializedName("company_copyright_text")
    val companyCopyrightText: String,
    @SerializedName("latitude")
    val latitude: String,
    @SerializedName("longitude")
    val longitude: String,
    @SerializedName("currency")
    val currency: String,
    @SerializedName("currency_symbol")
    val currencySymbol: String,
    @SerializedName("date_format")
    val dateFormat: String,
    @SerializedName("link_google_play")
    val linkGooglePlay: String,
    @SerializedName("link_apple_store")
    val linkAppleStore: String,
    @SerializedName("link_website")
    val linkWebsite: String,
    @SerializedName("radius")
    val radius: String,
    @SerializedName("company_name_ar")
    val companyNameAR: String,
    @SerializedName("meta_keywords_ar")
    val metaKeywordsAR: String,
    @SerializedName("meta_description_ar")
    val metaDescriptionAR: String,
    @SerializedName("company_name_en")
    val companyNameEn: String,
    @SerializedName("meta_keywords_en")
    val metaKeywordsEN: String,
    @SerializedName("meta_description_en")
    val metaDescriptionEN: String,
    @SerializedName("qr_link_google_play")
    val qrLinkGooglePlay: String,
    @SerializedName("qr_link_apple_store")
    val qrLinkAppleStore: String,
    @SerializedName("qr_link_website")
    val qrLinkWebsite: String,
)
