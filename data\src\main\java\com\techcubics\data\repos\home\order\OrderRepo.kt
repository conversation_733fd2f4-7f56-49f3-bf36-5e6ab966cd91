package com.techcubics.data.repos.home.order

import com.techcubics.data.model.pojo.Order
import com.techcubics.data.model.requests.CheckoutRequest
import com.techcubics.data.remote.BaseResponse

interface OrderRepo {

    suspend fun getOrderDetails(order_id : Int): BaseResponse<Order>?
    suspend fun getCurrentOrders(): BaseResponse<ArrayList<Order>>?
    suspend fun getPastOrders(): BaseResponse<ArrayList<Order>>?
    suspend fun checkout(request: CheckoutRequest): BaseResponse<Order>?

}