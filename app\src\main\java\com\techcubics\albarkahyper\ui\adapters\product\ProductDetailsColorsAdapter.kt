package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemProductColorBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductDetailsColorViewHolder
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener


class ProductDetailsColorsAdapter(
    var items: List<com.techcubics.data.model.pojo.ColorObject>,
    private val onColorSelected: OnItemsChangedListener
) :
    RecyclerView.Adapter<ProductDetailsColorViewHolder>() {

    var selectedColorId: Int = if (items.isNotEmpty()) items[0].colorID else 0
    fun setItemsList(_items: List<com.techcubics.data.model.pojo.ColorObject>) {
        notifyItemRangeRemoved(0, itemCount)
        items = _items
        selectedColorId = if (items.isNotEmpty()) items[0].colorID else 0
        notifyItemRangeInserted(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductDetailsColorViewHolder {
        val itemBinding =
            ItemProductColorBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductDetailsColorViewHolder(
            itemBinding,
            parent.context,
            ::setColorId,
            ::getColorId,
            ::updateAdapterOnSelectColor,
            onColorSelected
        )
    }

    override fun onBindViewHolder(holder: ProductDetailsColorViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int {
        return items.size
    }

    private fun setColorId(colorId: Int) {
        selectedColorId = colorId
    }

    private fun getColorId(): Int {
        return selectedColorId
    }

    private fun updateAdapterOnSelectColor() {
        notifyItemRangeChanged(0, itemCount)
    }


}
