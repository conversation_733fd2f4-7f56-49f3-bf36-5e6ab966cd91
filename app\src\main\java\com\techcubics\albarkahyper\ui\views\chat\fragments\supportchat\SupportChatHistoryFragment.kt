package com.techcubics.albarkahyper.ui.views.chat.fragments.supportchat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.SupportTicketData
import com.techcubics.data.model.pojo.User
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IPageRowset
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.albarkahyper.databinding.FragmentChatHistoryBinding
import com.techcubics.albarkahyper.ui.adapters.chat.ChatHistoryAdapter
import com.techcubics.albarkahyper.ui.views.chat.viewmodels.ChatViewModel
import com.techcubics.shared.enums.LottieIconEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class SupportChatHistoryFragment :  Fragment() , IPageRowset<SupportTicketData> {

    private  lateinit var binding: FragmentChatHistoryBinding
    private lateinit var supportChatHistoryAdapter: ChatHistoryAdapter<SupportTicketData>
    private val chatViewModel by viewModel<ChatViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    private  val TAG = "SupportChatHistoryFragment"
    companion object {
        val instance: SupportChatHistoryFragment = SupportChatHistoryFragment()
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding= FragmentChatHistoryBinding.inflate(inflater,container,false)
        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        binding.toolbar.tvTitle.text=resources.getText(com.techcubics.style.R.string.chat_support)
      /*  binding.toolbar.tvTitle.setCompoundDrawablesWithIntrinsicBounds(0,0,
            com.techcubics.style.R.drawable.ic_support_user,0)*/
        //

        chatViewModel.getSupportChatHistory()
        //
        showHidePlaceHolder(show=false,type= null,message=null)
        binding.rvHistory.visibility=View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)

    }
    override  fun observers(){

        chatViewModel.supportTicketResponse.observe(viewLifecycleOwner, Observer { it->



            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

            //it.data?.clear()
            try {
                if (it.status!!) {

                    if(it.data!=null){


                        if (!it.data!!.isEmpty()) {

                            showData(it.data!!)
                            binding.rvHistory.visibility = View.VISIBLE

                        }else{

                            val user = sharedPreferencesManager.getUser()
                            it?.data!!.add(getUserDataAsAMessage(user))
                            showData(it.data!!)

                            /*showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )*/
                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })

        chatViewModel.sendSupportResponse.observe(viewLifecycleOwner, Observer {

            binding.includeChatSendBox.btnSend.isEnabled=true
            try {
                if(it.status!!){
                    chatViewModel.getSupportChatHistory()
                    binding.includeChatSendBox.txtMessage.setText("")

                }else{

                    //error
                    Helper.showDialog(requireContext(),it.message!!)
                }

            }catch(ex:Exception) {
                //error
                Helper.showDialog(requireContext(),ex.message.toString())
            }
        })


    }
    override fun events(){

        binding.toolbar.tvTitle.setOnClickListener {

            findNavController().popBackStack()
        }

        binding.toolbar.btnBack.setOnClickListener {

            findNavController().popBackStack()
        }

        binding.includeChatSendBox.btnSend.setOnClickListener{

            if(binding.includeChatSendBox.txtMessage.text.isNotEmpty() && binding.includeChatSendBox.txtMessage.text.isNotBlank()){

                binding.includeChatSendBox.btnSend.isEnabled=false
                chatViewModel.sendToSupport(binding.includeChatSendBox.txtMessage.text.trim().toString())
            }

        }
    }

    private fun getUserDataAsAMessage(user: User): SupportTicketData {

        val timeFormatter = DateTimeFormatter.ofPattern("hh:mm")
        var t = LocalDateTime.now().format(timeFormatter)

        var phone:String?=if(user.phone.isNullOrEmpty()) getString(com.techcubics.style.R.string.phone_not_entered) else user.phone

        var msg="${getString(com.techcubics.style.R.string.full_name)} ${user.name}\n" +
                "${getString(com.techcubics.style.R.string.phone_number)}: ${phone}\n" +
                "${getString(com.techcubics.style.R.string.email)}: ${user.email}"

        return SupportTicketData(message =msg,time =t )

    }

    override fun showData( items:List<SupportTicketData>){

        binding.placeholder.root.visibility=View.GONE
        supportChatHistoryAdapter = ChatHistoryAdapter<SupportTicketData>()
        supportChatHistoryAdapter.setItemsList(items)
        binding.rvHistory.adapter = supportChatHistoryAdapter
        binding.rvHistory.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL,true)

        binding.scroller.postDelayed(Runnable {
            binding.scroller.fullScroll(View.FOCUS_DOWN)
        }, 100)


    }
    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    if(message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!){
                        CoroutineScope(Dispatchers.Main).launch {
                            binding.chathistoryLayout.visibility = View.GONE
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
                        binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                        binding.placeholder.tvMessage.text = message
                    }
                } else -> throw IllegalStateException("error")
            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}