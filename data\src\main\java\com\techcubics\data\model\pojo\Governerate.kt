package com.techcubics.data.model.pojo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Governerate(
    @SerializedName("id")
    override var id:Int,
    @SerializedName("governorate_id")
    val governorate_id:Int,
    @SerializedName("name")
    override var name:String
):AreaAdapterData,Parcelable

interface AreaAdapterData{
    var id:Int
    var name:String
}
