package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemBranchTypeBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.BranchTypeHolderItem


class BranchesTypesAdaper (val onClick: IOnAdapterItemClickHandler): RecyclerView.Adapter<BranchTypeHolderItem>() {

    lateinit var items:List<StoreTypes>

    fun setItemsList(_items:List<StoreTypes>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BranchTypeHolderItem {
        val itemBinding = ItemBranchTypeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BranchTypeHolderItem(itemBinding,parent.context, onClick )
    }

    override fun getItemViewType(position: Int): Int {

        if (position % 2 == 0)
          return 1
        else
            return 2

    }

    override fun onBindViewHolder(holder: BranchTypeHolderItem, position: Int) {
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return items.size
    }




}