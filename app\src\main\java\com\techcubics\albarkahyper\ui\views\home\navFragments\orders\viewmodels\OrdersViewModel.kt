package com.techcubics.albarkahyper.ui.views.home.navFragments.orders.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.model.pojo.Order
import com.techcubics.data.model.requests.CheckoutRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.order.OrderRepo
import com.techcubics.data.repos.home.order.OrderRepoImpl
import kotlinx.coroutines.launch


class OrdersViewModel(private val orderRepo: OrderRepo): ViewModel() {

    val orderDetailsMutableLiveData=MutableLiveData<BaseResponse<Order>?>()
    val currentOrdersMutableLiveData=MutableLiveData<BaseResponse<ArrayList<Order>>?>()
    val pastOrdersMutableLiveData=MutableLiveData<BaseResponse<ArrayList<Order>>?>()
    val checkoutLiveData = MutableLiveData<BaseResponse<Order>?>()
    val currentItemClickedLiveData = MutableLiveData<Order?>()
    val pastItemClickedLiveData = MutableLiveData<Order?>()

     fun getOrderDetails(order_id : Int){

     viewModelScope.launch {
         val orderDetailsResponse = orderRepo.getOrderDetails(order_id)

         if (orderDetailsResponse != null){
             orderDetailsMutableLiveData.postValue(orderDetailsResponse)
         }
     }

     }

     fun getCurrentOrders(){

        viewModelScope.launch {
            val currentOrdersResponse = orderRepo.getCurrentOrders()

            if (currentOrdersResponse != null){
                currentOrdersMutableLiveData.postValue(currentOrdersResponse)
            }

        }

    }

    fun getPastOrders(){

        viewModelScope.launch {
            val pastOrdersResponse = orderRepo.getPastOrders()

            if (pastOrdersResponse != null){
                pastOrdersMutableLiveData.postValue(pastOrdersResponse)
            }
        }

    }

    fun checkout(paymentMethod: String, note: String?, countryId: Int) {
        viewModelScope.launch {
            val request=CheckoutRequest(paymentMethod=paymentMethod, note = note, country_id = countryId)
            val res = orderRepo.checkout(request)
            checkoutLiveData.postValue(res)
        }
    }
}