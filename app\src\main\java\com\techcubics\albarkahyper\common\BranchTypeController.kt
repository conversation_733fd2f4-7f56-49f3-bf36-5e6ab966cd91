package com.techcubics.albarkahyper.common

import android.content.Context
import android.text.InputType
import android.view.View
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.pojo.StoreTypesList
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.IncludeSizeSectionBinding
import com.techcubics.albarkahyper.ui.dropdownlistadapter.BranchTypeDropdownAdapter
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener

class BranchTypeController(
    private val context: Context,
    private val listener: OnItemsChangedListener,
    view: View
) {
    private lateinit var brancTypeAdapter: BranchTypeDropdownAdapter
    private var binding = IncludeSizeSectionBinding.bind(view)
    private var selectedBranchId = -1
    private var sizeIndex = -1
    private var sizes: MutableList<StoreTypes>? = null

    fun initDropdownList(storelist:MutableList<StoreTypes>?) {
        this.sizes = storelist
        binding.sizesDropdownList.inputType = InputType.TYPE_NULL
        if (storelist == null || storelist.size <= 0) {
            binding.root.visibility = View.GONE
            listener.calcPrice()
        } else {
            binding.root.visibility = View.VISIBLE
            if (storelist.size > 0) {
                onItemSelected(0)
                setSize()
            }
        }

    }

    private fun setSize() {
        sizes?.let {
            brancTypeAdapter = BranchTypeDropdownAdapter(context, R.layout.item_dropdown_list, it)
            binding.sizesDropdownList.setAdapter(brancTypeAdapter)
            binding.sizesDropdownList.setOnItemClickListener { _, _, position, _ ->
                onItemSelected(position)
            }
        }
    }

    private fun onItemSelected(position: Int) {
        binding.sizesDropdownList.setText(sizes?.get(position)?.name ?: "", false)
        selectedBranchId = sizes?.get(position)?.branchTypeID ?: -1
        listener.calcPrice()
    }

    fun getSelectedSizeIndex(): Int {
        return sizeIndex
    }

    fun getBranchId(): Int {
        return selectedBranchId
    }

}