<?xml version="1.0" encoding="utf-8"?>

<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
   android:orientation="vertical"
    tools:context=".ui.views.products.searchResult.MainCategoriesFragment">

    <include layout="@layout/toolbar_fragment" android:id="@+id/toolbarMain" android:layout_width="match_parent" android:layout_height="wrap_content"/>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="8dp">


            <include layout="@layout/toolbar_product_search" android:id="@+id/toolbarSearch" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="16dp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/rvBanners"
                tools:itemCount="1"
                android:layout_marginTop="16dp"


                tools:listitem="@layout/item_slider"/>
            <ru.tinkoff.scrollingpagerindicator.ScrollingPagerIndicator
                android:id="@+id/indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:spi_dotColor="@color/color_gray_1"
                app:spi_dotSelectedColor="@color/app_color"
                app:spi_dotSelectedSize="12dp"
                app:spi_dotSize="12dp"
                app:spi_visibleDotCount="3"
                app:spi_visibleDotThreshold="0"
                android:layout_gravity="center"
                android:layout_marginTop="8dp"/>


            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    style="@style/label_section_header"
                    android:text="@string/today_offer"
                    />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/rvDiscounts"
                        tools:itemCount="1"
                        android:layout_weight="1"
                        tools:listitem="@layout/item_store_product"
                        android:layout_marginTop="16dp"/>
                    <ProgressBar
                        android:id="@+id/horizontal_loading"
                        android:layout_width="35dp"
                        android:layout_gravity="center_vertical"
                        android:layout_height="35dp"
                        android:visibility="gone"
                        android:indeterminateTint="@color/app_color"
                        />
                </LinearLayout>


            </androidx.appcompat.widget.LinearLayoutCompat>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                style="@style/label_section_header"
                android:text="@string/section_main_category_title"
                />
            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/rvCategories"
                tools:itemCount="1"
                tools:listitem="@layout/item_main_category"
                android:layout_marginTop="16dp"/>



        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/include_qty_cart_info"
        layout="@layout/include_qty_cart_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.appcompat.widget.LinearLayoutCompat>