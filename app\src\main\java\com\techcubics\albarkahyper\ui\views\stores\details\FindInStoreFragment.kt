package com.techcubics.albarkahyper.ui.views.stores.details

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.text.Spanned
import android.text.TextWatcher
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageButton
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.ProductSearchData
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentFindInStoreBinding
import com.techcubics.albarkahyper.ui.adapters.store.ProductsAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*
import kotlin.collections.ArrayList


class FindInStoreFragment : Fragment(), IPagePagedRowset<ProductSearchData>, IFavClickListener,
    IOnAdapterItemClickHandler {

    private var _binding: FragmentFindInStoreBinding? = null
    private val binding get() = _binding!!
    private lateinit var productsAdapter: ProductsAdapter<ProductSearchData>
    private val productsFragmentViewModel: ProductsViewModel by viewModel<ProductsViewModel>()
    private lateinit var resultList: ArrayList<ProductSearchData>
    private lateinit var etSearch: EditText
    private var isLoading: Boolean = false
    private var id: Int? = null
    private var catID: Int? = null
    private lateinit var name: String
    private var isGeneral: Boolean = true
    private var word: String? = null
    private var pages: MutableMap<Int, Int> = mutableMapOf()

    private var _operation: Int = -1
    private var _position: Int? = null

    //    private lateinit var productSearchProgressButton: CircleProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private var totalPrice = 0f

    private val TAG = "FindInStoreFragment"


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        Helper.exitFullScreen(requireContext())
//        if (_binding==null) {
        _binding = FragmentFindInStoreBinding.inflate(inflater, container, false)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        init()
//        }
        observers()
        events()

        return binding.root
    }


    override fun init() {
        println("I ammmmmmmmmmmmmmmmmmmmmm heeeeeeereeeeeeee")
        resultList = arrayListOf()
        setProductAdapter()
        binding.toolbarSearchByStore.btnCart.visibility = View.VISIBLE
        arguments?.let {
            id = it.getInt(Constants.INTENT_ID, -1)
            name = it.getString(Constants.INTENT_NAME, "")
            minOrder = it.getFloat(Constants.MIN_ORDER, 0f)
            catID = it.getInt(Constants.INTENT_TYPE_ID, -1)
            if (word == null) {
                word = it.getString(Constants.INTENT_WORD) ?: ""
            }
            productsFragmentViewModel.getCart()
            binding.toolbarSearchByStore.tvTitle.text = name
            if (catID == null || catID == -1) {
                productsFragmentViewModel.getProductsByStore(storeID = id!!, word = word, page = 1)
            } else {
                val cat = Array<Int>(1) { catID!! }
                productsFragmentViewModel.getProductsByStore(
                    storeID = id!!,
                    word = word,
                    page = 1,
                    catIDList = cat
                )
            }
            //
            showHidePlaceHolder(show = false, type = null, message = null)

            binding.pagingLoadingImg.visibility = View.GONE
            binding.includeQtyCartInfo.root.visibility = View.GONE
            binding.rvProducts.visibility = View.GONE
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            etSearch = binding.searchToolbar.txtSearchWord
            etSearch.setText(word)
            etSearch.hint = getString(com.techcubics.style.R.string.hint_write_product_name)
            var x: ImageButton = binding.searchToolbar.btnFilter
            x.visibility = View.GONE

        }
        calcPrice(minOrder, totalPrice)
    }

    override fun observers() {
        productsFragmentViewModel.getCartResponse.observe(viewLifecycleOwner) { cartRes ->
            if (cartRes != null) {
                if (cartRes.status == true && cartRes.data != null && cartRes.data?.size!! > 0) {
                    totalPrice = cartRes.data!![0].totalPrice!!
                    calcPrice(minOrder, totalPrice)
                }
                productsFragmentViewModel.getCartResponse.value = null
            }
        }

        productsFragmentViewModel.productSearchResponse.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)

//            try {
            if (it.status!!) {

                if (it.data != null) {

                    if (it.data!!.isNotEmpty()) {
                        showData(it.data!!)
                        binding.includeQtyCartInfo.root.visibility = View.VISIBLE
                        binding.rvProducts.visibility = View.VISIBLE
                        showHidePlaceHolder(
                            show = false,
                            type = null,
                            message = null
                        )
                    } else {
                        binding.pagingLoadingImg.visibility = View.GONE
                        binding.includeQtyCartInfo.root.visibility = View.GONE
                        binding.rvProducts.visibility = View.GONE
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = getString(com.techcubics.style.R.string.message_empty_list_general)
                        )
                    }

                } else {
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    //empty
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )

                }

            } else {
                binding.pagingLoadingImg.visibility = View.GONE
                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                //error
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Error,
                    message = it.message
                )
            }

//            } catch (ex: Exception) {
//                //error
//                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
//            }
        }

        productsFragmentViewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner, Observer {


            try {

                if (it.status!!) {

                    when (_operation) {
                        1 -> resultList[_position!!].isFav = false
                        2 -> resultList[_position!!].isFav = true
                    }
                    showHidePlaceHolder(
                        show = false,
                        type = null,
                        message = null
                    )
                    // productsAdapter.notifyDataSetChanged()

                } else {

                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {


                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.pagingLoadingImg.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }


        })

        productsFragmentViewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null) {
                val offset = if (qty == minQty) qty else 1
                val p = ((totalPrice * 100) - (offset * (itemPrice * 100))) / 100
                totalPrice = p
                if (it.message.toString() != Constants.SERVER_ERROR) {
                    if (it.status == true) {
                        totalPrice = it.data?.totalPrice ?: 0f
                    } else {
                        productsAdapter.notifyItemChanged(itemPosition)
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }
                } else if (it.message.toString().contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                calcPrice(minOrder, totalPrice)
                productsFragmentViewModel.addCartResponse.value = null
            }
        }
        productsFragmentViewModel.cartItemRemovedResponse.observe(context as LifecycleOwner) { cart ->
            if (cart != null) {
                if (!cart.status!!) {
                    val p = ((totalPrice * 100) + (qty * (itemPrice * 100))) / 100
                    totalPrice = p
                    bottomSheetAlertDialog.showDialog(cart.message.toString())
                } else {
                    totalPrice = cart.data?.totalPrice ?: 0f
                    if (resultList[itemPosition].isDiscount==true){
                        resultList[itemPosition].discount?.get(0)?.quantityCart = 0
                    }else{
                        resultList[itemPosition].qtyCart = 0
                    }
                }
                productsAdapter.items = resultList
                productsAdapter.notifyItemChanged(itemPosition)
                productsFragmentViewModel.cartItemRemovedResponse.value = null
            }
            calcPrice(minOrder, totalPrice)
        }

    }

    override fun events() {
        binding.toolbarSearchByStore.btnCart.setOnClickListener {
            val b = Bundle()
            b.putString(Constants.INTENT_PAGE_TYPE, "store_details")
            findNavController().navigate(R.id.view_cart, b)
        }
        binding.includeQtyCartInfo.openCartBtn.setOnClickListener {
            val b = Bundle()
            b.putString(Constants.INTENT_PAGE_TYPE, "store_details")

            findNavController().navigate(R.id.view_cart, b)
        }
        binding.toolbarSearchByStore.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
        etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {

            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                word = p0.toString()
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        }
        )

        etSearch.setOnEditorActionListener { v, actionId, event ->
            when (actionId) {
                EditorInfo.IME_ACTION_SEARCH -> {
                    //word= ""+v.text
                    // var e:String= word
                    Log.d(TAG, "events: ${word}")
                    v.clearFocus()
                    v.hideKeyboard()
                    resultList.clear()
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvProducts.visibility = View.INVISIBLE
                    Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
                    showHidePlaceHolder(show = false, type = null, message = null)
                    isGeneral = false

                    if (catID == null || catID == -1) {
                        productsFragmentViewModel.getProductsByStore(
                            storeID = id!!,
                            word = this.word,
                            page = 1
                        )
                    } else {
                        val cat = Array<Int>(1) { catID!! }
                        productsFragmentViewModel.getProductsByStore(
                            storeID = id!!,
                            word = this.word,
                            page = 1,
                            catIDList = cat
                        )
                    }

                    true
                }

                else -> false
            }
        }
    }

    fun View.hideKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)
    }

    //    private var itemPage = 1
//    private fun getCurrentPage() {
//        val offset = 10
//        val numPages = ceil(resultList.size.toFloat() / offset.toFloat()).toInt()
//        for (i in 0 until numPages) {
//            val start = (offset * i)
//            val end = offset * (i + 1)
//            if (itemPosition in start until end) {
//                itemPage = i + 1
//                break
//            }
//        }
//    }
    private fun setProductAdapter() {

        productsAdapter = ProductsAdapter(
            onFavClickListener = this,
            onClickHandler = this,
            isHorizontal = false,
            context = requireContext()
        )
        productsAdapter.setItemsList(resultList, minOrder)
        binding.rvProducts.adapter = productsAdapter
        val manager = GridLayoutManager(requireContext(), 2)
        binding.rvProducts.layoutManager = manager
        var pageDelegate =
            PagingDelegate.Builder(productsAdapter).attachTo(binding.rvProducts)
                .listenWith(this).build()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun showData(items: List<ProductSearchData>) {

        if (productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage == 1) {
            resultList = items as ArrayList<ProductSearchData>
//            productsAdapter = ProductsAdapter(onFavClickListener = this, onClickHandler = this)
            productsAdapter.setItemsList(resultList, minOrder)
//            binding.rvProducts.adapter = productsAdapter
//            binding.rvProducts.layoutManager =
//                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            //
//            var pageDelegate =
//                PagingDelegate.Builder(productsAdapter).attachTo(binding.rvProducts)
//                    .listenWith(this).build()


        } else {

            items.forEach {
                val p = resultList.find { p -> p.id == it.id }
                val index = resultList.indexOf(p)
                if (p == null) {
                    resultList.add(it)
                } else {
                    resultList[index] = it
                }
            }
            onDonePaging()
        }
        productsAdapter.notifyDataSetChanged()

    }

    override fun onPage(p0: Int) {


        if (!isLoading) {
            Log.d(TAG, "onPage: ${p0}")
            if (productsFragmentViewModel.productSearchResponse.value?.pagingator?.hasMorePages!!) {
                isLoading = true
                binding.pagingLoadingImg.visibility = View.VISIBLE

                if (catID == null || catID == -1) {
                    when (isGeneral) {
                        true -> productsFragmentViewModel.getProductsByStore(
                            storeID = id!!,
                            page = productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage!! + 1
                        )
                        false -> productsFragmentViewModel.getProductsByStore(
                            storeID = id!!,
                            page = productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage!! + 1,
                            word = word
                        )
                    }

                } else {

                    val cat = Array<Int>(1) { catID!! }

                    when (isGeneral) {
                        true -> productsFragmentViewModel.getProductsByStore(
                            storeID = id!!,
                            page = productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage!! + 1,
                            catIDList = cat
                        )
                        false -> productsFragmentViewModel.getProductsByStore(
                            storeID = id!!,
                            page = productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage!! + 1,
                            word = word,
                            catIDList = cat
                        )
                    }
                }


            }
        }
    }

    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility = View.GONE
        isLoading = false
    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    if (message == Constants.SERVER_ERROR) {
                        binding.placeholder.tvMessage.text =
                            getString(com.techcubics.style.R.string.server_error)

                    } else {
                        binding.placeholder.tvMessage.text = message

                    }
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onFavClick(parent: Int, position: Int, operation: Int) {

        _operation = operation
        _position = position
        productsFragmentViewModel.productSearchResponse.value?.data!![position].id?.let {
            productsFragmentViewModel.addRemoveFav(
                it
            )
        }


    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle = Bundle()
        bundle.putInt(Constants.INTENT_ID, itemId ?: -1)
        findNavController().navigate(R.id.view_productDetails, bundle)

    }

    private var qty = 0
    private var maxQty = 0
    private var minQty = 0
    private var itemPrice = 0f
    private var minOrder = 0f
    private var itemPosition = -1

    override fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
        this.qty = qty ?: 0
        this.itemPrice = itemPrice
        this.maxQty = maxQty
        this.minQty = minQty
        this.itemPosition = itemPosition
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            val offset = if (qty == minQty) qty else 1
            val p = ((totalPrice * 100) + (offset * (itemPrice * 100))) / 100
            totalPrice = p
            calcPrice(this.minOrder, totalPrice)
            productsFragmentViewModel.addToCart(pathEndPoint, id, modelType, modelId, qty)

        } else {
            findNavController().navigate(R.id.go_to_login)
        }

    }

    override fun removeItemFromCart(
        modelType: String?,
        modelId: Int?,
        productPrice: Float,
        itemPosition: Int,
    ) {
        this.itemPosition = itemPosition
        this.itemPrice = productPrice
        val p = ((totalPrice * 100) - (qty * (productPrice * 100))) / 100
        totalPrice = p
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            calcPrice(this.minOrder, totalPrice)
            productsFragmentViewModel.removeCartItem(modelType, modelId)

        } else {
            findNavController().navigate(R.id.go_to_login)
        }
    }

    private fun calcPrice(minOrder: Float, totalPrice: Float) {
        binding.includeQtyCartInfo.progressBar.max = 100
        binding.includeQtyCartInfo.progressBar.progress = if (minOrder > 0) {
            (((totalPrice * 100) / (minOrder * 100)) * 100).toInt()
        } else {
            0
        }
        //min order
        val numFormatMinOrder =
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(minOrder) + "&#8200;" +
                    getString(com.techcubics.style.R.string.currency_name)
        val minOrderStr = getString(com.techcubics.style.R.string.min_order, numFormatMinOrder)
        val minOrderStyledText: Spanned = Html.fromHtml(minOrderStr, Html.FROM_HTML_MODE_LEGACY)
        binding.includeQtyCartInfo.minOrder.text = minOrderStyledText
        //total amount
        val numFormatPrice = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH)
            .format(totalPrice) + "&#8200;" +
                getString(com.techcubics.style.R.string.currency_name)
        val totalAmountStr =
            getString(com.techcubics.style.R.string.product_order_total_amount, numFormatPrice)
        val totalAmountStyledText: Spanned = Html.fromHtml(
            totalAmountStr,
            Html.FROM_HTML_MODE_LEGACY
        )
        binding.includeQtyCartInfo.totalAmount.text = totalAmountStyledText

    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }


}