package com.techcubics.albarkahyper.ui.views.home.navFragments.orders

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.ui.views.home.navFragments.orders.viewmodels.OrdersViewModel
import com.techcubics.albarkahyper.databinding.FragmentPaymentInCartBinding
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.shared.constants.Constants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class PaymentInCartFragment : Fragment() {

    lateinit var binding: FragmentPaymentInCartBinding
    lateinit var progressButton: ProgressButton
    val viewModel by viewModel<OrdersViewModel>()
    private var pageType : String? = ""
    private var note : String? = ""
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPaymentInCartBinding.inflate(layoutInflater, container, false)
        pageType = arguments?.getString(Constants.INTENT_PAGE_TYPE)
        note = arguments?.getString(Constants.INTENT_NOTE)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.signBtnProgress)
        initViews()
        viewObservers()
        return binding.root
    }

    private fun viewObservers() {
        viewModel.checkoutLiveData.observe(viewLifecycleOwner) { orderResponse ->
            if (orderResponse?.status == true) {
                if (sharedPreferencesManager.getLocationID()!=-1){
                    sharedPreferencesManager.removeLocationID()
                }
                CoroutineScope(Dispatchers.Main).launch {
                    progressButton.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.execute),null)
                    delay(500)
                    val bundle = Bundle()
                    bundle.putInt(Constants.ORDER_ID, orderResponse.data?.orderId ?: -1)
                    bundle.putString(Constants.INTENT_PAGE_TYPE,pageType)
                    findNavController().navigate(
                        R.id.action_paymentInCartFragment_to_myOrderDetailsFragment,
                        bundle
                    )
                }

            }else{
                progressButton.btnFinishedFailed(getString(com.techcubics.style.R.string.execute),null)
                bottomSheetAlertDialog.showDialog(orderResponse?.message.toString())
            }
        }
    }


    private fun initViews() {
//        viewModel = ViewModelProvider(this)[OrdersViewModel::class.java]
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.mycart)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }
        progressButton.binding.textView.text = getString(com.techcubics.style.R.string.execute)

        progressButton.binding.constraintsLayout.setOnClickListener {
            if (binding.payCash.isChecked) {
                progressButton.btnActivated()
                viewModel.checkout("cash",note, sharedPreferencesManager.getCountryID().toInt())
            }else{
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.please_choose_paymentmethod))
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}