package com.techcubics.data.model.requests.home

import com.google.gson.annotations.SerializedName
import com.techcubics.shared.enums.RateTypesEnum
import retrofit2.http.Part

data class AddRateRequest(

    val countryID:Int,
    @SerializedName("rate_id")
    val rateID:Int,
    @SerializedName("rate_type")
    val type: String,
    @SerializedName("degree")
    val degree:Int,
    @SerializedName("comment")
    val comment:String
)
