# دليل حل مشاكل بناء APK - APK Build Troubleshooting Guide

## 🚨 المشكلة: ملفات APK و AAB لم يتم إنشاؤها

## 🔍 الخطوة 1: فحص المتطلبات

### قم بتشغيل ملف الفحص:
```cmd
check_requirements.bat
```

هذا الملف سيتحقق من:
- ✅ وجود ملفات المشروع
- ✅ تثبيت Java
- ✅ إعداد Android SDK
- ✅ عمل Gradle Wrapper

## 🛠️ الخطوة 2: إصلاح المشاكل الشائعة

### مشكلة 1: Java غير مثبت أو غير موجود في PATH

**الحل:**
1. حمل Java JDK 17 من: https://adoptium.net/
2. ثبته في المسار الافتراضي
3. أعد تشغيل Command Prompt
4. اختبر بالأمر: `java -version`

### مشكلة 2: Android SDK غير موجود

**الحل:**
1. حمل Android Studio من: https://developer.android.com/studio
2. ثبته وافتح Android Studio مرة واحدة
3. اذهب إلى: File → Settings → Appearance & Behavior → System Settings → Android SDK
4. تأكد من تثبيت Android SDK Platform 35
5. سجل مسار SDK (عادة: `C:\Users\<USER>\AppData\Local\Android\Sdk`)

### مشكلة 3: متغيرات البيئة غير مضبوطة

**لضبط متغيرات البيئة في Windows:**

1. **اضغط Windows + R**
2. **اكتب:** `sysdm.cpl`
3. **اضغط Enter**
4. **اضغط "Environment Variables"**
5. **في "System Variables" اضغط "New"**
6. **أضف:**
   - **Variable name:** `JAVA_HOME`
   - **Variable value:** `C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot` (أو مسار Java لديك)
7. **أضف متغير آخر:**
   - **Variable name:** `ANDROID_HOME`
   - **Variable value:** `C:\Users\<USER>\AppData\Local\Android\Sdk`
8. **عدل متغير PATH وأضف:**
   - `%JAVA_HOME%\bin`
   - `%ANDROID_HOME%\tools`
   - `%ANDROID_HOME%\platform-tools`

## 🚀 الخطوة 3: بناء التطبيق

بعد إصلاح جميع المشاكل:

### الطريقة المبسطة:
```cmd
simple_build.bat
```

### الطريقة اليدوية:
```cmd
# انتقل إلى مجلد المشروع
cd "d:\YASSER ALSOUSI\Software Development AI\Application AI\AlbarkaHyper-android-app\AlbarkaHyper-android-development"

# تنظيف المشروع
gradlew clean

# بناء APK
gradlew assembleRelease

# بناء AAB
gradlew bundleRelease
```

## 📁 الخطوة 4: التحقق من الملفات

بعد اكتمال البناء بنجاح، ستجد الملفات في:

### APK:
```
d:\YASSER ALSOUSI\Software Development AI\Application AI\AlbarkaHyper-android-app\AlbarkaHyper-android-development\app\build\outputs\apk\release\app-release.apk
```

### AAB:
```
d:\YASSER ALSOUSI\Software Development AI\Application AI\AlbarkaHyper-android-app\AlbarkaHyper-android-development\app\build\outputs\bundle\release\app-release.aab
```

## 🔧 حلول للمشاكل المتقدمة

### مشكلة: Gradle Daemon failed
```cmd
gradlew --stop
gradlew clean --refresh-dependencies
```

### مشكلة: Out of memory
أضف إلى `gradle.properties`:
```
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m
```

### مشكلة: Keystore not found
تأكد من وجود الملف:
```
app\signature\albarkahypersecretkeystore.jks
```

### مشكلة: SDK not found
في `local.properties` أضف:
```
sdk.dir=C\:\\Users\\[اسمك]\\AppData\\Local\\Android\\Sdk
```

## 📞 إذا استمرت المشاكل

1. **تأكد من إغلاق Android Studio** أثناء البناء
2. **أعد تشغيل الكمبيوتر** بعد تثبيت Java/Android SDK
3. **احذف مجلد `.gradle`** في مجلد المشروع وأعد المحاولة
4. **تأكد من وجود اتصال إنترنت** لتحميل dependencies

## ✅ علامات النجاح

عند نجاح البناء ستشاهد:
```
BUILD SUCCESSFUL in Xs
```

وستجد الملفات:
- `app-release.apk` (حجم تقريبي: 20-50 MB)
- `app-release.aab` (حجم تقريبي: 15-40 MB)

## 🎯 الخطوات التالية بعد النجاح

1. **اختبر APK** على جهاز Android
2. **راجع ملفات Google Play** في مجلد `google-play-assets`
3. **ارفع AAB** على Google Play Console (مفضل)
4. **أو ارفع APK** إذا لم يعمل AAB

---

**ملاحظة:** إذا واجهت مشاكل محددة، انسخ رسالة الخطأ كاملة وابحث عنها في Google أو اسأل للمساعدة.
