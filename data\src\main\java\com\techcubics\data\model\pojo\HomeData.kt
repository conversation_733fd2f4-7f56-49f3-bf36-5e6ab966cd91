package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class HomeData(
    @SerializedName("categories")
    val categories:MutableList<Category>,
    @SerializedName("offers")
    val Offers:MutableList<Offer>,
    @SerializedName("saves")
    val Saves:MutableList<Save>,
    @SerializedName("discounts")
    val discounts:MutableList<Discount>,
    @SerializedName("branch_type")
    val stores:MutableList<StoreTypes>,
    @SerializedName("total_of_unread_notifications")
     val total_of_unread_notifications:Int?
)