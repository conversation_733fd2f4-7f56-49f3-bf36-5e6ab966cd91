<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            style="@style/label_section_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:text="@string/available_colors" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvColors"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="16dp"
            tools:listitem="@layout/item_color" />

        <include
            android:id="@+id/placeholderColors"
            layout="@layout/include_placeholder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|center" />
    </androidx.appcompat.widget.LinearLayoutCompat>




</RelativeLayout>