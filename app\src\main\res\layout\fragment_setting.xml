<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/profile_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_31">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintTop_toTopOf="parent" />
    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/color_gray_31"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/call_us"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:paddingStart="16dp">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/pgreen"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_contactus" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/call_us"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/share_app"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:paddingStart="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/orange"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_share" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/share_app"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/rate_app"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:paddingStart="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/darkRed"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_rate" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:drawablePadding="10dp"
                            android:gravity="center_vertical"
                            android:text="@string/rate_app"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/terms_conditions"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:paddingStart="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/color_59"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_policy_white" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:text="@string/conditions"
                            android:textAppearance="@style/label_profile" />
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="50dp"
                        android:background="@drawable/line_divder_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <LinearLayout
                        android:id="@+id/know_us"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:paddingStart="16dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_centerInParent="true"
                            android:backgroundTint="@color/color_57"
                            android:innerRadius="0dp"
                            android:shape="ring"
                            app:cardCornerRadius="75dp">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_about_white" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="center_vertical"
                            android:text="@string/know_us"
                            android:textAppearance="@style/label_profile"
                            android:textColor="@color/black" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/includeNetwork"
        layout="@layout/include_no_network_layout"
        android:visibility="gone" />


    <include
        android:id="@+id/page_loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
