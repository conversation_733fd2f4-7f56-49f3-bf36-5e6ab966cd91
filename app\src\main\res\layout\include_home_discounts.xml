<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
   >

    <ImageView
        android:id="@+id/imageView3"
        android:layout_width="@dimen/icons_size_height_weight_9"
        android:layout_height="@dimen/icons_size_height_weight_9"
        android:layout_marginTop="8dp"
        android:src="@drawable/ic_fire"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        style="@style/label_section_header"
        android:text="@string/section_discounts_title"
        app:layout_constraintBottom_toBottomOf="@+id/imageView3"
        app:layout_constraintStart_toEndOf="@+id/imageView3"
        app:layout_constraintTop_toTopOf="@+id/imageView3"
        app:layout_constraintVertical_bias="0.82" />


    <TextView
        android:id="@+id/tvMore"
        style="@style/label_section_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/more"
        app:layout_constraintBottom_toBottomOf="@+id/textView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/textView"
        android:drawablePadding="10dp"
        app:drawableEndCompat="@drawable/ic_arrow_left"
        android:clickable="true"
        android:background="?attr/selectableItemBackground"/>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDiscounts"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView"
        tools:itemCount="1"
        tools:listitem="@layout/item_discounts" />
</androidx.constraintlayout.widget.ConstraintLayout>