package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentProfileBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants.furniture
import com.techcubics.shared.enums.LoginStateEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*

class ProfileFragment : Fragment() {


    private lateinit var profileBinding: FragmentProfileBinding
    private var languagesStringList: MutableList<String> = mutableListOf()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private lateinit var popupDialog: PopupDialog
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        profileBinding = FragmentProfileBinding.inflate(inflater, container, false)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            initViews()
            observeAdapters()
            obserViews()
            profileBinding.includeNetwork.networkView.visibility = View.GONE
            profileBinding.scView.visibility = View.VISIBLE
        } else {
            profileBinding.includeNetwork.networkView.visibility = View.VISIBLE
            profileBinding.scView.visibility = View.GONE
        }
        profileBinding.includeNetwork.refresh.setOnClickListener {
            findNavController().run {
                popBackStack()
                navigate(com.techcubics.albarkahyper.R.id.profileFragment)
            }
        }

        return profileBinding.root
    }

    private fun obserViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!) {
                    CoroutineScope(Dispatchers.Main).launch {
                        profileBinding.profileLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(R.id.action_profileFragment_loginFragment)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }

        profileViewModel.countriesMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE,profileBinding.actionLoadingAnimation.root)
            if (it != null) {
                if (!it.data.isNullOrEmpty()) {
                    sharedPreferencesManager.saveList("LISTCOUNTRIES", it.data)
                    popupDialog.showCounrtyDialog(
                        requireContext(),
                        R.layout.dialog_country,
                        it.data,
                        profileViewModel.countriesPopupMenuItemLiveData as MutableLiveData<Any?>,
                        "profile"
                    )
                }
                profileViewModel.countriesMutableLiveData.value = null
            }
        }

        profileViewModel.languageMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE,profileBinding.actionLoadingAnimation.root)
            if (it != null) {
                languagesStringList.clear()
                if (it.data != null) {
                    for (langCode in it.data!!) {
                        if (langCode.code.equals("de"))
                            continue
                        if (langCode.code.equals("tr"))
                            continue
                        languagesStringList.add(langCode.code)
                    }
                    sharedPreferencesManager.saveList(Constants.langaueList, languagesStringList)
                    if (languagesStringList.isNotEmpty()) {
                        popupDialog.showCounrtyDialog(
                            requireContext(),
                            R.layout.dialog_country,
                            languagesStringList,
                            profileViewModel.popupMenuItemLiveData as MutableLiveData<Any?>,
                            Constants.LANGUAGE
                        )
                    }
                }
                profileViewModel.languageMutableLiveData.value = null

            }
        }


//        profileViewModel.profileMutableLiveData.observe(viewLifecycleOwner) {
//            Helper.loadingAnimationVisibility(View.GONE,profileBinding.pageLoadingAnimation.root)
//            Log.i("clothes",it?.message.toString())
//            if (it != null) {
//                if (it.status!!) {
//                    profileBinding.profileData.visibility = View.VISIBLE
//                    sharedPreferencesManager.saveObject("USER", it.data)
//                    if(it.data?.avatar != null){
//                        Helper.loadImage(requireContext(), it.data?.avatar!!, profileBinding.userPhoto)
//                    }else{
//                        profileBinding.userPhoto.setImageResource(com.techcubics.style.R.drawable.portrait_placeholder)
//                    }
//
//                    profileBinding.userName.text = it.data?.name
//                    val email: String = it.data?.email ?: ""
//                    profileBinding.phone.text = it.data?.phone
//
//                    if(it.data?.unreadSupportChats != null){
//                        if (it.data?.unreadSupportChats!! > 0) {
//                            profileBinding.supportUnReadNo.visibility = View.VISIBLE
//                            profileBinding.supportUnReadNo.text =
//                                it.data?.unreadSupportChats.toString()
//                        }
//                    }
//                    if(it.data?.unseenChats != null){
//                        if (it.data?.unseenChats!! > 0) {
//                            profileBinding.chatUnReadNo.visibility = View.VISIBLE
//                            profileBinding.chatUnReadNo.text =
//                                it.data?.unseenChats.toString()
//                        }
//                    }
//
//                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
//                    Helper.ShowErrorDialog(
//                        requireContext(),
//                        getString(com.techcubics.style.R.string.server_error)
//                    )
//                }
//                profileViewModel.profileMutableLiveData.value = null
//            }
//
//        }

        authViewModel.logoutMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(View.GONE,profileBinding.actionLoadingAnimation.root)
            if(it!=null){
                if (it.status!!) {
                    sharedPreferencesManager.loggedIn("false")
                    sharedPreferencesManager.setLoginState(LoginStateEnum.Other.value)
                    CoroutineScope(Dispatchers.Main).launch {
                        bottomSheetAlertDialog.showDialog(it.message!!)
                        delay(1200)
                        bottomSheetAlertDialog.onDismiss()
                        findNavController().navigate(R.id.action_profileFragment_loginFragment)
                    }

                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    if(it.message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!){
                        sharedPreferencesManager.setLoginState(LoginStateEnum.SessionExpired.value)
                        CoroutineScope(Dispatchers.Main).launch {
                            profileBinding.profileLayout.visibility = View.GONE
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.action_profileFragment_loginFragment)
                        }
                    }else{
                        sharedPreferencesManager.setLoginState(LoginStateEnum.LoggedIn.value)
                        bottomSheetAlertDialog.showDialog(it.message!!)
                    }
                }
                authViewModel.logoutMutableLiveData.value = null
            }
        }
    }


    private fun observeAdapters() {

        profileViewModel.countriesPopupMenuItemLiveData.observe(this.viewLifecycleOwner) {
            if (it != null) {
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.selected_country) + " " + it.name)
                sharedPreferencesManager.saveCountryCode(it.country_code)
                sharedPreferencesManager.saveCountryID(it.country_id.toString())
                profileViewModel.countriesPopupMenuItemLiveData.value = null
            }
        }
    }

    private fun initViews() {

        checkAutherization()

        if(sharedPreferencesManager.getLanguage().equals("ar")){
            profileBinding.developedBy.gravity = Gravity.END
        }
        profileBinding.userName.text = sharedPreferencesManager.getName()
        profileBinding.shopName.text = sharedPreferencesManager.getShopName()
        Helper.loadImage(requireContext(),sharedPreferencesManager.getUserPhoto(),profileBinding.userPhoto)

        if (sharedPreferencesManager.isLoggedIn() == "true") {
            profileBinding.profileData.visibility = View.VISIBLE
            profileBinding.signIn.text = getString(com.techcubics.style.R.string.signout)
            profileBinding.chat.visibility = View.VISIBLE
            profileBinding.userServices.visibility = View.VISIBLE
            profileBinding.profile.visibility = View.VISIBLE
        } else {
            profileBinding.profileData.visibility = View.GONE
            profileBinding.chat.visibility = View.GONE
            profileBinding.userServices.visibility = View.GONE
            profileBinding.profile.visibility = View.GONE


            profileBinding.signIn.text = getString(com.techcubics.style.R.string.signin)
        }

        profileBinding.signInLayout.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            if (profileBinding.signIn.text.equals(getString(com.techcubics.style.R.string.signin))) {
//                activity?.startActivity(Intent(requireContext(), AuthActivity::class.java))
//                activity?.finishAffinity()
                findNavController().navigate(R.id.action_profileFragment_loginFragment)
            } else {
                //signout
                authViewModel.logout()
                findNavController().navigate(R.id.action_profileFragment_loginFragment)
            }

        }
        profileBinding.language.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            profileViewModel.getLanguages()
        }
        profileBinding.country.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            profileViewModel.getCountries()
        }

        profileBinding.profile.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_profileFragment_to_updateProfileFragment)
        }

        profileBinding.mypurchases.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_profileFragment_purchasesFragment)

        }
        profileBinding.addresses.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_profileFragment_to_myAddressesFragment)
        }

        profileBinding.myWallet.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,profileBinding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_profileFragment_to_myWalletFragment)
        }

        profileBinding.support.setOnClickListener {
            findNavController().navigate(R.id.view_supportChatHistoryFragment)
        }
        profileBinding.chat.setOnClickListener {
            findNavController().navigate(R.id.action_profileFragment_chattabs)
        }

        profileBinding.setting.setOnClickListener {
            findNavController().navigate(R.id.action_profileFragment_to_setting)
        }

        profileBinding.website.setOnClickListener {
            val websiteUrl = Constants.TECHCUBIC_WEBSITE_URL
            startActivity(Intent(Intent.ACTION_VIEW).setData(Uri.parse(websiteUrl)))
        }

        profileBinding.facebook.setOnClickListener {
            val websiteUrl = Constants.TECHCUBIC_FACEBOOK_URL
            startActivity(Intent(Intent.ACTION_VIEW).setData(Uri.parse(websiteUrl)))
        }

        profileBinding.whatsapp.setOnClickListener {
            openTechcubicsWhatsappChat()
        }

    }

    private fun openTechcubicsWhatsappChat() {
//        val packageManager = requireContext().packageManager
        val intent = Intent(Intent.ACTION_VIEW)
        intent.setPackage("com.whatsapp")
        intent.data = Uri.parse(Constants.TECHCUBIC_WHATSAPP_URL)
        requireContext().startActivity(intent)
//        if (intent.resolveActivity(packageManager) != null) {
//        } else {
//            Toast.makeText(requireContext(), "WhatsApp not installed", Toast.LENGTH_SHORT).show()
//        }
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }


    override fun onAttach(context: Context) {
        val newBase: Context =
            ContextWrapper.wrap(context, Locale(sharedPreferencesManager.getLanguage()))
        super.onAttach(newBase)
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
    }
}