@echo off
echo Building APK for AlBarkaHyper...

REM Set environment variables with quotes to handle spaces
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "PATH=%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo ANDROID_HOME: %ANDROID_HOME%
echo Current Directory: %CD%
echo.

REM Check if gradlew exists
if not exist "gradlew.bat" (
    echo gradlew.bat not found!
    echo Please make sure you are in the correct directory.
    pause
    exit /b 1
)

echo Cleaning project...
call gradlew.bat clean --stacktrace
if %ERRORLEVEL% neq 0 (
    echo Clean failed! Error code: %ERRORLEVEL%
    pause
    exit /b 1
)

echo.
echo Building release APK...
call gradlew.bat assembleRelease --stacktrace
if %ERRORLEVEL% neq 0 (
    echo Build failed! Error code: %ERRORLEVEL%
    pause
    exit /b 1
)

echo.
echo Building release Bundle (AAB)...
call gradlew.bat bundleRelease --stacktrace
if %ERRORLEVEL% neq 0 (
    echo Bundle build failed! Error code: %ERRORLEVEL%
    echo Continuing with APK only...
)

echo.
echo Build completed successfully!
echo APK location: app\build\outputs\apk\release\
echo AAB location: app\build\outputs\bundle\release\
echo.
echo Listing output files:
if exist "app\build\outputs\apk\release\" (
    dir "app\build\outputs\apk\release\" /b
) else (
    echo APK directory not found!
)
if exist "app\build\outputs\bundle\release\" (
    dir "app\build\outputs\bundle\release\" /b
) else (
    echo Bundle directory not found!
)
pause
