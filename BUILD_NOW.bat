@echo off
title AlBarkaHyper APK Builder
color 0A

echo.
echo ==========================================
echo    AlBarkaHyper APK Builder
echo ==========================================
echo.

REM Set Java path (try common locations)
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\OpenJDK\jdk-17"

REM Set Android SDK path (try common locations)
set "ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk"
if not exist "%ANDROID_HOME%" set "ANDROID_HOME=C:\Android\Sdk"
if not exist "%ANDROID_HOME%" set "ANDROID_HOME=C:\Users\<USER>\Android\Sdk"

REM Update PATH
set "PATH=%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%"

echo Current directory: %CD%
echo Java Home: %JAVA_HOME%
echo Android Home: %ANDROID_HOME%
echo.

REM Check if we're in the right directory
if not exist "gradlew.bat" (
    echo ERROR: gradlew.bat not found!
    echo Please make sure you're running this from the project root directory.
    echo Expected location: AlbarkaHyper-android-development folder
    echo.
    pause
    exit /b 1
)

if not exist "app\build.gradle" (
    echo ERROR: app\build.gradle not found!
    echo This doesn't appear to be an Android project directory.
    echo.
    pause
    exit /b 1
)

echo ✓ Project files found
echo.

REM Test Java
echo Testing Java installation...
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found!
    echo.
    echo Please install Java JDK 17 from: https://adoptium.net/
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
)
echo ✓ Java is working
echo.

REM Make gradlew executable and test it
echo Testing Gradle wrapper...
call gradlew.bat --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Gradle wrapper failed!
    echo This might be due to missing Android SDK or other dependencies.
    echo.
    echo Trying to download Gradle wrapper...
    call gradlew.bat wrapper --gradle-version=8.10.2
)
echo ✓ Gradle wrapper is working
echo.

echo ==========================================
echo    Starting Build Process
echo ==========================================
echo.

echo Step 1/3: Cleaning project...
call gradlew.bat clean --no-daemon --stacktrace
if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Clean failed!
    echo Check the error messages above.
    echo.
    pause
    exit /b 1
)
echo ✓ Clean completed
echo.

echo Step 2/3: Building APK...
call gradlew.bat assembleRelease --no-daemon --stacktrace
if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: APK build failed!
    echo Check the error messages above.
    echo.
    pause
    exit /b 1
)
echo ✓ APK build completed
echo.

echo Step 3/3: Building AAB (Android App Bundle)...
call gradlew.bat bundleRelease --no-daemon --stacktrace
if %ERRORLEVEL% neq 0 (
    echo.
    echo WARNING: AAB build failed!
    echo But APK was built successfully.
    echo You can use the APK file for now.
    echo.
) else (
    echo ✓ AAB build completed
    echo.
)

echo ==========================================
echo    Build Completed!
echo ==========================================
echo.

REM Check and display results
echo Checking output files...
echo.

if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ SUCCESS: APK file created!
    echo   Location: %CD%\app\build\outputs\apk\release\app-release.apk
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo   Size: %%~zI bytes
    echo.
) else (
    echo ✗ ERROR: APK file was not created!
    echo.
)

if exist "app\build\outputs\bundle\release\app-release.aab" (
    echo ✓ SUCCESS: AAB file created!
    echo   Location: %CD%\app\build\outputs\bundle\release\app-release.aab
    for %%I in ("app\build\outputs\bundle\release\app-release.aab") do echo   Size: %%~zI bytes
    echo.
) else (
    echo ✗ WARNING: AAB file was not created (but APK should work)
    echo.
)

echo ==========================================
echo    Next Steps
echo ==========================================
echo.
echo 1. Test the APK on an Android device
echo 2. Check the google-play-assets folder for store materials
echo 3. Upload to Google Play Store
echo.
echo Full paths:
echo APK: %CD%\app\build\outputs\apk\release\app-release.apk
echo AAB: %CD%\app\build\outputs\bundle\release\app-release.aab
echo.

pause
