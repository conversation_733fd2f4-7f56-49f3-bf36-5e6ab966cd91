<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="10dp"
    app:cardElevation="3dp"
    android:layout_margin="10dp"
    app:cardPreventCornerOverlap="true"
    android:id="@+id/main_banner">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/image1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/portrait_placeholder"
            android:layout_alignParentTop="true"
            android:layout_alignParentBottom="true" />

        <ImageView
            android:id="@+id/close_banner"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignStart="@id/image1"
            android:layout_alignTop="@id/image1"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/btn_ripple_gray"
            android:clickable="true"
            android:padding="5dp"
            android:src="@drawable/ic_close_dark" />

    </RelativeLayout>


</androidx.cardview.widget.CardView>