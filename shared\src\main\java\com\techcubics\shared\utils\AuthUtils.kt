package com.techcubics.shared.utils

import android.content.Context
import android.graphics.Color
import android.os.Build
import android.view.View
import android.widget.TextView
import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber
import java.util.regex.Matcher
import java.util.regex.Pattern


class AuthUtils {

    companion object {

        fun validateEmail(userEmail: String): Boolean {
//            val patternEmail: Pattern = Pattern.compile("^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}\$")
//            val matcherEmail: Matcher = patternEmail.matcher(userEmail)
//            val emailMatchResult: Boolean = matcherEmail.matches()
            return true
        }

//        fun validatePassword(password: String): Boolean {
//
//            //Minimum eight characters, at least one letter and one number
//            val patternPassword: Pattern =
//                Pattern.compile("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,}$")
//            val matcherPassword: Matcher = patternPassword.matcher(password)
//            val passowordMatchResult: Boolean = matcherPassword.matches()
//
//            return passowordMatchResult
//        }

        fun validatePassword(password: String,specialchar:TextView,AtoZ:TextView,num:TextView,charcount:TextView): Boolean {

            var check = true
            // check for pattern
            val uppercase = Pattern.compile("[A-Z]")
            val special = Pattern.compile("[@#\$%^&+=*-]")
            val digit = Pattern.compile("[0-9]")

            // if lowercase character is not present
            if (!special.matcher(password).find()) {
                specialchar.setTextColor(Color.RED)
                check = false
            } else {
                // if lowercase character is  present
                specialchar.setTextColor(Color.GREEN)
            }

            // if uppercase character is not present
            if (!uppercase.matcher(password).find()) {
                AtoZ.setTextColor(Color.RED)
                check = false
            } else {
                // if uppercase character is  present
                AtoZ.setTextColor(Color.GREEN)
            }
            // if digit is not present
            if (!digit.matcher(password).find()) {
                num.setTextColor(Color.RED)
                check = false
            } else {
                // if digit is present
                num.setTextColor(Color.GREEN)
            }
            // if password length is less than 8
            if (password.length < 8) {
                charcount.setTextColor(Color.RED)
                check = false
            } else {
                charcount.setTextColor(Color.GREEN)
            }
            return check
        }
        fun validatePhone(phone: String,countryCode : String): Boolean {
//            Log.i("here",phone+" "+countryCode)
//            val phoneNumberUtil = PhoneNumberUtil.getInstance()
//            val isoCode = phoneNumberUtil.getRegionCodeForCountryCode(countryCode.toInt())
//            var phoneNumber: Phonenumber.PhoneNumber? = null
//            try {
//                //phoneNumber = phoneNumberUtil.parse(phNumber, "IN");  //if you want to pass region code
//                phoneNumber = phoneNumberUtil.parse(phone, isoCode)
//            } catch (e: NumberParseException) {
//                System.err.println(e)
//            }
//            val isValid = phoneNumberUtil.isValidNumber(phoneNumber)
//            return if (isValid) {
//                val internationalFormat = phoneNumberUtil.format(
//                    phoneNumber,
//                    PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL
//                )
//                true
//            } else {
//                false
//            }
            return true
        }

        fun isNumeric(toCheck: String): Boolean {
            val regex = "-?[0-9]+(\\.[0-9]+)?".toRegex()
            return toCheck.matches(regex)
        }

        fun isRTL(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                (context.getResources().getConfiguration().getLayoutDirection() == View.LAYOUT_DIRECTION_RTL)
                // Another way:
                // Define a boolean resource as "true" in res/values-ldrtl
                // and "false" in res/values
                // return context.getResources().getBoolean(R.bool.is_right_to_left);
            } else {
                false
            }
        }

    }


}