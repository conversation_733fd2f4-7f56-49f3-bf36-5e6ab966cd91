package com.techcubics.albarkahyper.ui.views.chat.fragments

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.ogaclejapan.smarttablayout.utils.v4.FragmentPagerItemAdapter
import com.ogaclejapan.smarttablayout.utils.v4.FragmentPagerItems
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.NetworkChangeReceiver
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.albarkahyper.databinding.FragmentChatTabsBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.chat.fragments.livechat.LiveChatRoomsFragment
import com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat.ChatRoomsFragment
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class ChatTabsFragment : Fragment() {
    private lateinit var binding : FragmentChatTabsBinding
    private val authViewModel by viewModel<AuthViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChatTabsBinding.inflate(layoutInflater,container,false)

        binding.chatToolbar.tvTitle.text = getString(com.techcubics.style.R.string.chat)
        binding.chatToolbar.tvTitle.setCompoundDrawablesWithIntrinsicBounds(null,null,null,null)
        if(NetworkChangeReceiver.isOnline(requireContext())){
            setUpTabBar()
            initViews()
            observeViews()
            binding.ordersView.visibility = View.VISIBLE
            binding.includeNetwork.networkView.visibility = View.GONE
        }else{
            binding.ordersView.visibility = View.GONE
            binding.includeNetwork.networkView.visibility = View.VISIBLE
        }

        binding.includeNetwork.refresh.setOnClickListener {
            findNavController().run {
                popBackStack()
                navigate(com.techcubics.albarkahyper.R.id.ordersFragment)
            }
        }
        return binding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){

            if(it != null){
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.orderLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(com.techcubics.albarkahyper.R.id.ordertologin)
                    }
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }


        }
    }

    private fun initViews() {
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        checkAutherization()
    }

    private fun setUpTabBar() {

        val adapter = FragmentPagerItemAdapter(
            childFragmentManager,
            FragmentPagerItems.with(activity)
                .add(getString(com.techcubics.style.R.string.live_chat), LiveChatRoomsFragment::class.java)
                .add(getString(com.techcubics.style.R.string.reservation_chat), ChatRoomsFragment::class.java)
                .create()
        )

        binding.viewpager.adapter = adapter
        binding.viewpagertab.setupWithViewPager(binding.viewpager)

        for (i in 0 until binding.viewpagertab.getTabCount()) {
            val tab = (binding.viewpagertab.getChildAt(0) as ViewGroup).getChildAt(i)
            when(i){
                0-> tab.setBackgroundResource(com.techcubics.style.R.drawable.tab_shape_right)
                1-> tab.setBackgroundResource(com.techcubics.style.R.drawable.tab_shape_left)
            }

            tab.requestLayout()
        }
    }
    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
    }
}