package com.techcubics.albarkahyper.common

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Bundle
import androidx.core.app.NotificationCompat
import androidx.navigation.NavDeepLinkBuilder
import com.techcubics.albarkahyper.MainActivity
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.services.NotificationObject
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.shared.constants.Constants
import com.techcubics.style.R
import org.koin.android.compat.ViewModelCompat.viewModel
import org.koin.android.ext.android.get
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object NotificationHelper: KoinComponent {


    private  val TAG = "NotificationHelper"
    fun show(mContext: Context,notificationObject : NotificationObject){

        var pendingIntent: PendingIntent?=null
        val intent: Intent
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {


            var c: NotificationChannel = NotificationChannel(
                mContext.getString(R.string.fcm_notfication_channel_id),
                "channel1",
                NotificationManager.IMPORTANCE_HIGH
            )
            c.description = mContext.getString(R.string.fcm_notfication_channel_description)

            var m: NotificationManager = mContext.getSystemService(NotificationManager::class.java)
            m.createNotificationChannel(c)

        }
        /////////////////////////////////////////////////


        pendingIntent=pendingIntent(mContext,notificationObject)

        var builder = NotificationCompat.Builder(
            mContext,
            mContext.getString(R.string.fcm_notfication_channel_id)
        )

            .setContentTitle(notificationObject.title)
            .setContentText(notificationObject.body)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setLargeIcon(BitmapFactory.decodeResource(mContext.getResources(), R.mipmap.ic_launcher))
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText(notificationObject.body).setBigContentTitle(
                        notificationObject.title
                    ).setSummaryText(mContext.getString(R.string.fcm_notficiation_channel_summary))
            )

        if(android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.LOLLIPOP){

            builder.setSmallIcon(R.mipmap.ic_launcher)
            builder.setColor(mContext.getColor(R.color.white))

        }else{

            builder.setSmallIcon(R.mipmap.ic_launcher)
            builder.setColor(mContext.getColor(com.techcubics.style.R.color.color_gray_4))

        }
        var mNotificationManager: NotificationManager =  mContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        mNotificationManager.notify(System.currentTimeMillis().toString(), 1, builder.build());


    }

    val notificationsFragmentViewModel: SectionsViewModel by inject()
    private lateinit var refreshClickListener: IItemClickListener

    fun pendingIntent(context: Context,notificationObject : NotificationObject): PendingIntent {

        var destination:Int=-1
        val bundle= Bundle()
        refreshClickListener=object:IItemClickListener{
            override fun perform() {
                notificationsFragmentViewModel.readNotifications()
            }
        }
        bundle.putSerializable("refresh",refreshClickListener)

        when(notificationObject.info.notification_type){

            NotificationTypes.NotificationFurniture.value->{
                destination=com.techcubics.albarkahyper.R.id.notificationsFragment
            }
            NotificationTypes.Coupon.value->{
                destination=com.techcubics.albarkahyper.R.id.notificationsFragment
            }
            else->{
             destination=com.techcubics.albarkahyper.R.id.homeFragment
            }


        }



        return NavDeepLinkBuilder(context)
            .setComponentName(MainActivity::class.java)
            .setGraph(com.techcubics.albarkahyper.R.navigation.main_nav_graph)
            .setDestination(destination,bundle)
            .createPendingIntent()
    }



}