package com.techcubics.albarkahyper.ui.adapters.product

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.AreaAdapterData
import com.techcubics.albarkahyper.databinding.ItemAreaBinding
import com.techcubics.shared.constants.Constants
import com.techcubics.albarkahyper.ui.views.products.searchResult.AreaSelectionListener
import com.techcubics.style.R.*

class AreaAdapter<T>(
    private val context: Context,
    private var areaList: ArrayList<T>,
    private val listener: AreaSelectionListener
) : RecyclerView.Adapter<AreaViewHolder<T>>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AreaViewHolder<T> {
        val binding = ItemAreaBinding.inflate(LayoutInflater.from(context), parent, false)
        return AreaViewHolder(binding, context, listener)
    }

    override fun onBindViewHolder(holder: AreaViewHolder<T>, position: Int) {
        holder.setData(areaList[position])
    }

    override fun getItemCount(): Int {
        return areaList.size
    }

    fun updateAreaList(areaList: ArrayList<T>) {
        notifyItemRangeRemoved(0, itemCount)
        this.areaList = areaList
        notifyItemRangeInserted(0, itemCount)
    }
}

class AreaViewHolder<T>(
    private val binding: ItemAreaBinding,
    private val context: Context,
    private val listener: AreaSelectionListener
) : RecyclerView.ViewHolder(binding.root) {
    fun setData(area: T) {
        binding.currentAreaIcon.visibility = View.GONE
        if (area is AreaAdapterData) {
            binding.areaName.text = when (area.name) {
                Constants.DETECT_LOCATION -> {
                    binding.currentAreaIcon.visibility = View.VISIBLE
                    context.getString(string.detect_location)
                }
                Constants.ALL_CITIES -> {
                    context.getString(string.all_cities)
                }
                Constants.ALL_AREAS->{
                    context.getString(string.all_areas)
                }
                else -> {
                    area.name
                }
            }
            binding.root.setOnClickListener {
                listener.onAreaSelected(area.id, area.name)
            }
        }

    }
}