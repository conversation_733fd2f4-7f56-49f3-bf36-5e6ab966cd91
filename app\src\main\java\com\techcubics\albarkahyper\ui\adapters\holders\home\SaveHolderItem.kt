package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import android.os.CountDownTimer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.CircleProgressButton
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Save
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.databinding.ItemSavesBinding
import com.techcubics.albarkahyper.ui.adapters.product.OfferProductsAdapter
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import org.koin.android.ext.android.get
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

class SaveHolderItem<T>(
    val binding: ItemSavesBinding,
    val context: Context,
    val margin: Int = 0,
    val clickHandler: IOnAdapterItemClickHandler
) : RecyclerView.ViewHolder(binding.root) {

    private val TAG = "SaveHolderItem"
    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    fun bind(data: T) {

        when (SharedPreferencesManager.getLanguage()) {
            "ar" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "eg" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "sa" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            else -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
        }

        when (data) {
            is Save -> {
                fillByGeneral(data)
            }

        }


    }

    private fun fillByGeneral(save: Save) {


        Helper.loadImage(context, save.Images[0].path, binding.imgThumb)
        Helper.loadImage(context, save.shop.logo, binding.logo)
        binding.tvBranchName.text = save.shop.name
        //binding.tvDescription.text = save.shop.description
        ////
        binding.tvTitle.text = save.name
        binding.tvCurrentPrice.text = "${save.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        //

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        /*val start="2022-07-08 12:00:00"
        val end="2022-07-08 12:01:00"*/

        val start=save.startDate+" "+save.fromTime
        val end=save.endDate+" "+save.toTime

        //
        val from = LocalDateTime.parse(start, formatter)
        val to = LocalDateTime.parse(end, formatter)

        val fromPeriod=Date.from(from.atZone(ZoneId.systemDefault()).toInstant())
        val toPeriod= Date.from(to.atZone(ZoneId.systemDefault()).toInstant())

        val diff: Long =  toPeriod.getTime() - fromPeriod.getTime()



        //   Log.d(TAG, "bind: ${minutes*-1}" )

        if(save.timer!=null){
            save.timer.cancel()
        }

        save.timer= object :CountDownTimer(diff, 1000) {

            // Callback function, fired on regular interval
            override fun onTick(millisUntilFinished: Long) {

               /* val seconds = millisUntilFinished / 1000 % 60
                val minutes = seconds/ 60
                val hours = minutes / 60
                val days = hours / 24*/

                val x=  Helper.formatToDigitalClock(millisUntilFinished).split(':')
                binding.includeTimerSection.tvHours.text="${x[0]}"
                binding.includeTimerSection.tvMinutes.text="${x[1]}"
                binding.includeTimerSection.tvSeconds.text="${x[2]}"


            }

            // Callback function, fired
            // when the time is up
            override fun onFinish() {
                // textView.setText("done!")
            }
        }.start()

        //
        val productsAdapter: OfferProductsAdapter = OfferProductsAdapter()
        productsAdapter.items = save.products
        binding.rvOfferProducts.adapter = productsAdapter
        val spanCount = if(save.products.size>3) 3 else save.products.size
        binding.rvOfferProducts.layoutManager =
                // LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL,false)
            GridLayoutManager(context, spanCount)
        itemView.setOnClickListener {
            clickHandler.onItemClicked(save.id, RateTypesEnum.Save.value)
        }
        binding.branchContainer.setOnClickListener {view->
            clickHandler.onItemClicked(save.shop.id, RateTypesEnum.Store.value)
        }
        val progressButton: CircleProgressButton
        progressButton = CircleProgressButton(context)
        progressButton.initRoundButton(binding.btnOrder)
        binding.btnOrder.constraintsLayout.setOnClickListener {
            if(SharedPreferencesManager.isLoggedIn() == "true"){
                progressButton.btnRoundActivated()
            }
            clickHandler.addToCart(
                EndPointConstants.add_save,
                save.shop.id,
                save.model_type,
                save.saveID,
                1,
                maxQty = 1,
                minQty = 1,
                minOrder = 0f,
                itemPosition = bindingAdapterPosition
            )
        }
    }

}