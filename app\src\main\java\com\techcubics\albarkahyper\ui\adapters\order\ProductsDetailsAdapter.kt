package com.techcubics.albarkahyper.ui.adapters.order

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.denzcoskun.imageslider.constants.ScaleTypes
import com.denzcoskun.imageslider.models.SlideModel
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemOrderBinding
import com.techcubics.albarkahyper.ui.adapters.holders.order.OrdersHolderItem
import com.techcubics.albarkahyper.ui.adapters.profile.LocationsAdapter
import com.techcubics.data.model.pojo.Order
import com.techcubics.data.model.pojo.ProductItem
import com.techcubics.data.model.pojo.Products
import com.techcubics.albarkahyper.databinding.ItemProductDetailsBinding
import com.techcubics.albarkahyper.ui.adapters.holders.order.ProductsDetailsHolderItem
import com.techcubics.style.R



class ProductsDetailsAdapter(
    private val context: Context,
    private val listOfProducts: List<ProductItem>):
    RecyclerView.Adapter<ProductsDetailsHolderItem>(){

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductsDetailsHolderItem {

        val itemBinding = ItemProductDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductsDetailsHolderItem(itemBinding)
    }


    @SuppressLint("ResourceAsColor")
    override fun onBindViewHolder(holder: ProductsDetailsHolderItem, position: Int) {
        Helper.loadImage(context,listOfProducts[position].productIcon.toString(),holder.productImg)
        holder.productName.text = listOfProducts[position].productName
        holder.productQty.text = listOfProducts[position].productQty
        holder.productPrice.text = listOfProducts[position].productPrice

    }

    override fun getItemCount() = listOfProducts.size

}