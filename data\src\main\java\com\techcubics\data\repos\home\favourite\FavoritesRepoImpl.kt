package com.techcubics.data.repos.home.favourite

import com.techcubics.data.model.pojo.FavouriteProduct
import com.techcubics.data.model.pojo.FavouriteStore
import com.techcubics.data.model.requests.home.AddRemoveFavoriteRequest
import com.techcubics.data.model.requests.home.MyFavoirtesRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

class FavoritesRepoImpl(private val retrofitBuilder: RetrofitBuilder): RepositoryResponse, FavoritesRepo {


    override suspend fun addRemoveFav(request: AddRemoveFavoriteRequest): BaseResponse<String>?{

        return try {

            val result= retrofitBuilder.getBindObject()?.addRemoveFavoriteCall(request=request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getMyFavoirteProducts(request: MyFavoirtesRequest): BaseResponse<MutableList<FavouriteProduct>>?{
        return try {
            val result =  retrofitBuilder.getBindObject()?.myFavoriteProductsCall(type=request.type.value,page=request.page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getMyFavoirteStores(request: MyFavoirtesRequest): BaseResponse<MutableList<FavouriteStore>>?{

        return try {
            val result =  retrofitBuilder.getBindObject()?.myFavoriteStoresCall(type=request.type.value,page=request.page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
}