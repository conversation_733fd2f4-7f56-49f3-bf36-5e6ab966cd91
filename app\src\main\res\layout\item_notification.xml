<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/sendLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintHorizontal_bias="1.0"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    android:padding="8dp"
    android:layout_gravity="start"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_round_gray"
    android:layout_marginStart="4dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="16dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/iconContainer"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:backgroundTint="@color/app_color"
        android:innerRadius="0dp"
        android:shape="ring"
        app:cardCornerRadius="75dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            app:tint="@color/white"
            android:src="@drawable/ic_notifications" />
    </androidx.cardview.widget.CardView>


    <TextView
        android:id="@+id/tvTitle"
        style="@style/item_notification_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iconContainer"
        app:layout_constraintTop_toTopOf="parent"
        android:textAlignment="viewStart"
        tools:text="Title"
        />

    <TextView
        android:id="@+id/tvMessage"
        style="@style/item_notification_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textAlignment="viewStart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="hi, how are you " />

    <TextView
        android:id="@+id/tvDateTime"
        style="@style/item_notification_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="@+id/tvMessage"
        app:layout_constraintTop_toBottomOf="@+id/tvMessage"
        tools:text="04:30 PM" />

</androidx.constraintlayout.widget.ConstraintLayout>