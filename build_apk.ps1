# PowerShell script to build AlBarkaHyper APK
Write-Host "Building APK for AlBarkaHyper..." -ForegroundColor Green

# Set environment variables
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
$env:ANDROID_HOME = "$env:LOCALAPPDATA\Android\Sdk"
$env:PATH = "$env:JAVA_HOME\bin;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\platform-tools;$env:PATH"

Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Yellow
Write-Host "ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor Yellow
Write-Host "Current Directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if gradlew exists
if (-not (Test-Path "gradlew.bat")) {
    Write-Host "gradlew.bat not found!" -ForegroundColor Red
    Write-Host "Please make sure you are in the correct directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Clean project
Write-Host "Cleaning project..." -ForegroundColor Cyan
try {
    & .\gradlew.bat clean --stacktrace
    if ($LASTEXITCODE -ne 0) {
        throw "Clean failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "Clean failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Build release APK
Write-Host "Building release APK..." -ForegroundColor Cyan
try {
    & .\gradlew.bat assembleRelease --stacktrace
    if ($LASTEXITCODE -ne 0) {
        throw "APK build failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "APK build failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Build release Bundle (AAB)
Write-Host "Building release Bundle (AAB)..." -ForegroundColor Cyan
try {
    & .\gradlew.bat bundleRelease --stacktrace
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Bundle build failed with exit code $LASTEXITCODE" -ForegroundColor Yellow
        Write-Host "Continuing with APK only..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Bundle build failed: $_" -ForegroundColor Yellow
    Write-Host "Continuing with APK only..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "APK location: app\build\outputs\apk\release\" -ForegroundColor Green
Write-Host "AAB location: app\build\outputs\bundle\release\" -ForegroundColor Green
Write-Host ""

# List output files
Write-Host "Listing output files:" -ForegroundColor Cyan
if (Test-Path "app\build\outputs\apk\release\") {
    Get-ChildItem "app\build\outputs\apk\release\" | ForEach-Object { Write-Host $_.Name -ForegroundColor White }
} else {
    Write-Host "APK directory not found!" -ForegroundColor Red
}

if (Test-Path "app\build\outputs\bundle\release\") {
    Get-ChildItem "app\build\outputs\bundle\release\" | ForEach-Object { Write-Host $_.Name -ForegroundColor White }
} else {
    Write-Host "Bundle directory not found!" -ForegroundColor Yellow
}

Read-Host "Press Enter to exit"
