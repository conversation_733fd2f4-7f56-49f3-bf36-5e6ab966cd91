<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    xmlns:tools="http://schemas.android.com/tools">

    <com.mikhaellopez.circularimageview.CircularImageView

        android:id="@+id/logo"
        android:layout_width="@dimen/icons_size_height_weight_3"
        android:layout_height="@dimen/icons_size_height_weight_3"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        tools:srcCompat="@tools:sample/avatars" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        android:padding="8dp"
        android:background="@drawable/bg_round_gray_background"
        >

        <TextView
            android:id="@+id/tvName"
            style="@style/rating_label_item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Mohammed ALkhayat"
            android:maxLines="1"
            android:ellipsize="end"/>


        <RatingBar
            android:id="@+id/rate_bar"
            style="?android:attr/ratingBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:isIndicator="false"
            android:theme="@style/ratingbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvName" />


        <TextView
            android:id="@+id/tvDate"
            style="@style/rating_label_item_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:layout_constraintStart_toEndOf="@+id/rate_bar"
            app:layout_constraintTop_toTopOf="@+id/rate_bar"
            tools:text="17-08-2022" />

        <TextView
            android:id="@+id/tvDescription"
            style="@style/rating_label_item_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:textAlignment="viewStart"
            app:layout_constraintTop_toBottomOf="@+id/tvDate"
            tools:text="dfjpodfjpodfjpgojdfpogjpdfogpodfgpodfjpgodfjpgojpdfojpgodfjgpjodfgj" />

    </androidx.constraintlayout.widget.ConstraintLayout>






</androidx.appcompat.widget.LinearLayoutCompat>