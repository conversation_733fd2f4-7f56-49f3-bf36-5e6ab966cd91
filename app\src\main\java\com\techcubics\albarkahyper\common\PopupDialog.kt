package com.techcubics.albarkahyper.common

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.*
import androidx.core.widget.addTextChangedListener
import androidx.appcompat.widget.AppCompatButton
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.DeliveryAreaId
import com.techcubics.data.model.pojo.Governerate
import com.techcubics.data.model.pojo.Regoin
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.requests.auth.UpdatePasswordRequest
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.ui.adapters.profile.*
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.utils.AuthUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PopupDialog {

    private lateinit var dialog: Dialog
    private lateinit var mcontext: Context
    private  var bottomSheetAlertDialog =  BottomSheetAlertDialog()

    fun init(context: Context) {
        dialog = Dialog(context)
        mcontext = context
        bottomSheetAlertDialog.init(context)
    }

    fun showLanguageDialog(
        progressbar: ProgressBar,
        context: Context,
        languagePopup: Int,
        itemList: List<String>,
        popupMenuItemLiveData: MutableLiveData<String?>
    ) {
        dialog.setContentView(languagePopup)
        val language_rv: RecyclerView = dialog.findViewById(R.id.langRV)
        language_rv.adapter =
            SingleTextLineAdapter(progressbar, context, itemList, popupMenuItemLiveData, dialog)

        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }
    fun showCartProductsNotAvailableDialog() {
        dialog.setContentView(R.layout.dialog_cart_products_not_available)
        val close: ImageView = dialog.findViewById(R.id.close)
        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        close.setOnClickListener {
            dialog.dismiss()
        }
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }

    fun showRegionDialog(
        context: Context,
        languagePopup: Int,
        itemList: ArrayList<Regoin>,
        popupMenuItemLiveData: MutableLiveData<Regoin?>
    ) {
        dialog.setContentView(languagePopup)
        val language_rv: RecyclerView = dialog.findViewById(R.id.langRV)
        val textview: TextView = dialog.findViewById(R.id.empty_region)

        if (itemList.isEmpty()) {
            textview.visibility = View.VISIBLE
            language_rv.visibility = View.GONE
        }
        language_rv.adapter = RegionAdapter(context, itemList, popupMenuItemLiveData, dialog)


        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }

    fun showCounrtyDialog(
        context: Context,
        countryPopup: Int,
        listOfCountries: List<Any>?,
        popupMenuItemLiveData: MutableLiveData<Any?>,
        TAG: String
    ) {
        dialog.setContentView(countryPopup)
        val country_rv: RecyclerView = dialog.findViewById(R.id.countryRV)
        val progressBar: ProgressBar = dialog.findViewById(R.id.progress_bar)
        val textview: TextView = dialog.findViewById(R.id.empty_region)

        if (TAG.equals("add")) {
            val list = listOfCountries as List<DeliveryAreaId>
            if (list.size <= 0 || list.isNullOrEmpty() || list.get(0).deliveryAreaId == null) {
                textview.visibility = View.VISIBLE
                country_rv.visibility = View.GONE
            }
        }

        country_rv.adapter = CountryAdapter(
            context,
            listOfCountries,
            popupMenuItemLiveData,
            progressBar,
            country_rv,
            TAG,
            dialog
        )

        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }

    fun showGovernerateDialog(
        context: Context,
        languagePopup: Int,
        list: ArrayList<Governerate>,
        popupMenuItemLiveData: MutableLiveData<Governerate?>
    ) {
        dialog.setContentView(languagePopup)
        val language_rv: RecyclerView = dialog.findViewById(R.id.langRV)
        val textview: TextView = dialog.findViewById(R.id.empty_region)

        if (list.isEmpty()) {
            textview.visibility = View.VISIBLE
            language_rv.visibility = View.GONE
        }
        language_rv.adapter = GovernerateAdapter(context, list, popupMenuItemLiveData, dialog)
        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }

    fun showDialog(
        mutableliveDate: MutableLiveData<UpdatePasswordRequest?>,
        viewLifecycleOwner: LifecycleOwner,
        authViewModel: AuthViewModel
    ) {
        bottomSheetAlertDialog.init(mcontext)
        dialog.setContentView(R.layout.dialog_changepassword)
        val oldPass: EditText = dialog.findViewById(R.id.old_pass)
        val newPass: EditText = dialog.findViewById(R.id.new_password)
        val confirmPass: EditText = dialog.findViewById(R.id.confirm_pass)
        val constraintLayout: ConstraintLayout = dialog.findViewById(R.id.sign_btn_progress)
        val progressbar: ProgressBar = constraintLayout.findViewById(R.id.progressBar2)
        val textview: TextView = constraintLayout.findViewById(R.id.textView)

        textview.text = mcontext.getString(com.techcubics.style.R.string.change)



        constraintLayout.setOnClickListener {
            if (checkUiValidity(oldPass, newPass, confirmPass)) {
                progressbar.visibility = View.VISIBLE
                textview.visibility = View.GONE
                mutableliveDate.postValue(
                    UpdatePasswordRequest(
                        true,
                        oldPass.text.toString(),
                        newPass.text.toString(),
                        confirmPass.text.toString()
                    )
                )
            }
        }

        mutableliveDate.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it?.check!!) {
                    authViewModel.updatePassword(
                        UpdatePasswordRequest(
                            true,
                            it.old_password,
                            it.password,
                            it.password_confirmation
                        )
                    )
                }
                mutableliveDate.value = null
            }

        }

        authViewModel.updatePasswordResetMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    btnFinishedSuccessfully(constraintLayout, textview, progressbar)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    btnFinishedFailed(constraintLayout, textview, progressbar)
                    Helper.ShowErrorDialog(
                        mcontext,
                        mcontext.getString(com.techcubics.style.R.string.server_error)
                    )
                } else {
                    btnFinishedFailed(constraintLayout, textview, progressbar)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                authViewModel.updatePasswordResetMutableLiveData.value = null
            }
        }


        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (dialog.window != null) {
            dialog.setCancelable(true)
        }
    }

    fun showTermsAndConditionsDialog(
        context: Context,
        termsAndConditionsData: String,
        sharedPreferencesManager: SharedPreferencesManager,
        termsConditionsCheckbox: CheckBox
    ) {
        dialog.setContentView(R.layout.dialog_terms_and_conditions)
        val constraintLayout: ConstraintLayout = dialog.findViewById(R.id.sign_btn_progress)
        val progressbar: ProgressBar = constraintLayout.findViewById(R.id.progressBar2)
        val textview: TextView = constraintLayout.findViewById(R.id.textView)
        val webview: WebView = dialog.findViewById(R.id.terms_conditions_webview)

        textview.text = context.getString(com.techcubics.style.R.string.ok)
        constraintLayout.setOnClickListener {
            CoroutineScope(Dispatchers.Main).launch {
                progressbar.visibility = View.VISIBLE
                textview.visibility = View.GONE
                delay(1000)
                sharedPreferencesManager.setTerms(true)
                termsConditionsCheckbox.isChecked = true
                btnFinishedSuccessfully(constraintLayout, textview, progressbar)
            }

        }


        webview.loadDataWithBaseURL(
            null,
            termsAndConditionsData,
            "text/html",
            "utf-8",
            null
        )
        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

    }

    fun showSessionExpiredDialog(
        context: Context
    ) {
        dialog.setContentView(R.layout.dialog_session_expired)
        val progressbar: ProgressBar = dialog.findViewById(R.id.session_expired_progressbar)
        progressbar.visibility = View.VISIBLE
        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

    }

    fun onDismiss(){
        dialog.dismiss()
    }

    fun multiChoiceDialog(
        context: Context,
        shopTypePopup: Int,
        itemList: List<StoreTypes>,
        popupMenuItemLiveData: MutableLiveData<ArrayList<StoreTypes?>>
    ) {
        dialog.setContentView(shopTypePopup)
        val shoptype_rv: RecyclerView = dialog.findViewById(R.id.shoptypeRV)
        val yesbtn: AppCompatButton = dialog.findViewById(R.id.yes)
        val nobtn: AppCompatButton = dialog.findViewById(R.id.no)
        shoptype_rv.adapter = BranchesAdapter(
            context, itemList, popupMenuItemLiveData, yesbtn, nobtn,
            dialog
        )

        dialog.show()
        dialog.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

    }

    fun btnFinishedSuccessfully(
        constraintLayout: ConstraintLayout,
        textview: TextView,
        progressbar: ProgressBar
    ) {
        CoroutineScope(Dispatchers.Main).launch {
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.style.R.drawable.btn_green,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.visibility = View.VISIBLE
            textview.text = mcontext.getString(com.techcubics.style.R.string.done)
            delay(1500)
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.style.R.drawable.btn_style,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.text = mcontext.getString(com.techcubics.style.R.string.change)
            dialog.dismiss()
        }

    }


    fun btnFinishedFailed(
        constraintLayout: ConstraintLayout,
        textview: TextView,
        progressbar: ProgressBar
    ) {
        CoroutineScope(Dispatchers.Main).launch {
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.style.R.drawable.btn_red,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.visibility = View.VISIBLE
            textview.text = mcontext.getString(com.techcubics.style.R.string.fail)
            delay(1500)
            constraintLayout.background = ResourcesCompat.getDrawable(
                mcontext.resources, com.techcubics.style.R.drawable.btn_style,
                mcontext.theme
            )
            progressbar.visibility = View.GONE
            textview.text = mcontext.getString(com.techcubics.style.R.string.change)
        }

    }


    fun checkUiValidity(
        oldPass: EditText,
        newPass: EditText,
        confirmPass: EditText
    ): Boolean {

        var check = true

        if (oldPass.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(mcontext.getString(com.techcubics.style.R.string.invalid_old_password))
            check = false
        } else if (newPass.text.isNullOrEmpty()
        ) {
            bottomSheetAlertDialog.showDialog(mcontext.getString(com.techcubics.style.R.string.invalid_new_password))
            check = false
        } else if (!confirmPass.text.toString().equals(newPass.text.toString())
            || confirmPass.text.isNullOrEmpty()
        ) {
            bottomSheetAlertDialog.showDialog(mcontext.getString(com.techcubics.style.R.string.confirm_password_isincorrect))
            check = false
        }


        return check
    }

}