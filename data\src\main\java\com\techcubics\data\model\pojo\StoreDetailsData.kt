package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class StoreDetailsData(
    @SerializedName("shop")
    val shop: StoreDetailsGeneralData,
    @SerializedName("most_wanted_products")
    val mostWantedProducts: MutableList<StoreDetailsMostWantedData>,
    @SerializedName("menu")
    val menu: MutableList<StoreDetailsMenuData>,
    @SerializedName("offers")
    val offers:MutableList<StoreDetailsOffersData>,
    @SerializedName("saves")
    val saves:MutableList<StoresDetailsSavesData>,
    @SerializedName("discounts")
    val discounts:MutableList<StoreDetailsDiscountsData>,
    @SerializedName("branches")
    val branches:MutableList<StoreDetailsBranchesData>,
    @SerializedName("rates")
    val rates:MutableList<StoreDetailsRatesData>

)