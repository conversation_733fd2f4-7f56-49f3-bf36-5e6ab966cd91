package com.techcubics.data.repos.product

import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.ItemRemovingRequest
import com.techcubics.data.model.requests.SelectLocationRequest
import com.techcubics.data.model.requests.SubmitCouponRequest
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse

class ProductsRepoImpl(private val retrofitBuilder: RetrofitBuilder) : RepositoryResponse, ProductsRepo {
    private val TAG = "ProductsRepo"

    //key & country
    /* suspend fun getProductsByWord(request: ProductSearchRequest): ProductSearchResponse?{

         val key_search=request.key_search.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())

         val result= retrofitBuilder.getBindObject()?.productSearchCall(country_id = request.country_id,key_search=key_search,page=request.page)
         when (result?.code()) {
             //Success
             200 -> return result.body()
             // Unprocessable Content and UnAuthorized
             422 -> {
                 val jsonObj = JSONObject(result?.errorBody()?.charStream()!!.readText())
                 return ProductSearchResponse(message = jsonObj.getString("message"), status = false)
             }
             //General Error
             else -> return ProductSearchResponse(message = result?.message()!!, status = false)
         }
     }*/
    //key , colors , price start , price end , country

    override suspend fun removeAllCart(): BaseResponse<CartData>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.removeAllCart()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
    override suspend fun getProductsFilter(request: ProductSearchRequest): BaseResponse<List<ProductSearchData>>? {
        return try {


            val result = retrofitBuilder.getBindObject()?.productSearchCall(

                request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    //category & country
    override suspend fun getProductsByCategory(request: ProductByCategoryRequest): BaseResponse<MutableList<ProductDetailsDto>>? {


        return try {
            val result = retrofitBuilder.getBindObject()?.productByCategoryCall(
                price = request.priceOrder.value,
                categoryID = request.categoryID,
                branch_type_id=request.branch_type_id,
                storeID = request.storeID,
                page = request.page?:1,
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
    override suspend fun getProductsBySubCategory(request: ProductByCategoryRequest): BaseResponse<MutableList<ProductDetailsDto>>?
    {


        return try {
            val result = retrofitBuilder.getBindObject()?.productBySubCategoryCall(
                price = request.priceOrder.value,
                subCatId = request.categoryID,
                branch_type_id=request.branch_type_id,
                storeID = request.storeID,
                page = request.page?:1,
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }




    override suspend fun getColors(): BaseResponse<MutableList<ColorObject>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.productColorsCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getColorsSizes(request: ProductColorsSizesRequest): BaseResponse<ProductColorsSizesData>? {
        return try {
            val result = retrofitBuilder.getBindObject()
                ?.productColorsSizesCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getProductDetails(request: ProductDetailsRequest): BaseResponse<ProductData>? {
        return try {
            val result = retrofitBuilder.getBindObject()
                ?.getProductDetails(request.id)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun addRate(request: AddRateRequest2): BaseResponse<Any>? {

        return try {
            val result = retrofitBuilder.getBindObject()?.addRate(
                request

            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }

    }

    override suspend fun addToCart(
        pathEndPoint: String,
        request: AddCartRequest
    ): BaseResponse<CartData>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.addToCart(
                pathEndPoint,
                request

            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }

    }


    override suspend fun getCart(): BaseResponse<ArrayList<CartData>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getCart(

            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun removeCartItem(

        request: ItemRemovingRequest
    ): BaseResponse<CartData>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.removeModelType(

                request
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }


        override suspend fun submitCoupon(request:SubmitCouponRequest): BaseResponse<Coupon>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.submitCoupon(

                request
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }


    override  suspend fun selectLocation(
       request:SelectLocationRequest
    ): BaseResponse<SelectedLocationData>? {
        return try {

            val result = retrofitBuilder.getBindObject()?.selectLocation(
                request=request
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getDiscountDetails(
        discountId: Int

    ): BaseResponse<Discount>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getDiscountDetails(
                discountId
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }

    }

    override suspend fun getOfferDetails(offerId: String): BaseResponse<Offer>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getOfferDetails(
                offerId

            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }

    }

    override suspend fun getSaveDetails(saveId: String): BaseResponse<Save>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getSaveDetails(
                saveId

            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }

    }

    override suspend fun getBranchTypesCategories(branchTypeID: Int): BaseResponse<MutableList<Category>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getBranchTypesCategories(
                branchTypeID

            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
    override suspend fun getCategoriesByShopId(shopId:Int): BaseResponse<MutableList<Category>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getCategoriesByShopId(shopId)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
    override suspend fun getSubCategoriesByCatId(catId:Int): BaseResponse<MutableList<SubCategory>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getSubCategoriesByCatId(catId)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }



    override suspend fun getBanners(

    ): BaseResponse<MutableList<BannerData>>? {

        return try {
            val result = retrofitBuilder.getBindObject()?.getBanners()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getMostWantedProducts(store: Int, category_id: Int, page: Int): BaseResponse<MutableList<ProductDetailsDto>>? {

        return try {
            val result = retrofitBuilder.getBindObject()?.mostWantedProductsCall(store,category_id,page
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getHomeBanner(): BaseResponse<MutableList<BannerData>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.getHomeBanner()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getStoreDiscounts(store: Int, page: Int): BaseResponse<MutableList<ProductDetailsDto>>? {
        return try {
            val result = retrofitBuilder.getBindObject()?.storeDiscountsCall(store,page
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }


}