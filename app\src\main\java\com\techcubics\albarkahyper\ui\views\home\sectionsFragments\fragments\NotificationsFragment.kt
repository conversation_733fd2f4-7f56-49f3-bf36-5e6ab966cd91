package com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments

import android.R
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IPagePagedRowset
import com.techcubics.albarkahyper.common.NotificationClickListener
import com.techcubics.albarkahyper.common.NotificationTypes
import com.techcubics.albarkahyper.databinding.FragmentNotificationsBinding
import com.techcubics.albarkahyper.ui.adapters.home.NotificationsAdaper
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.albarkahyper.ui.views.stores.StoresViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Notification
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class NotificationsFragment : BottomSheetDialogFragment(), IPagePagedRowset<Notification>,
    NotificationClickListener {

    private lateinit var binding: FragmentNotificationsBinding
    private lateinit var notificationsAdapter: NotificationsAdaper
    private  val notificationsFragmentViewModel: SectionsViewModel by viewModel<SectionsViewModel>()
    private  val storesViewModel: StoresViewModel by viewModel<StoresViewModel>()
    private var isLoading: Boolean = false
    private lateinit var resultList: ArrayList<Notification>
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private val TAG = "NotificationsFragment"
    private val args:NotificationsFragmentArgs by navArgs()

    companion object {
        val instance: NotificationsFragment = NotificationsFragment()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, com.techcubics.style.R.style.bottomsheet_style)

    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentNotificationsBinding.inflate(inflater, container, false)
        dialog?.setOnShowListener {
            val d: BottomSheetDialog = dialog as BottomSheetDialog
            val bottomSheetInternal: View =
                d.findViewById(com.google.android.material.R.id.design_bottom_sheet)!!
            BottomSheetBehavior.from(bottomSheetInternal)
                .setState(BottomSheetBehavior.STATE_EXPANDED)
        }


        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {
        //SharedPreferencesManager.init(requireContext())

        binding.toolbarNotifications.tvTitle.text =
            resources.getText(com.techcubics.style.R.string.section_notifications_title)
        notificationsFragmentViewModel.getNotifications(1)
        //
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvNotifications.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
    }

    override fun observers() {

        notificationsFragmentViewModel.notificationsResponse.observe(this, Observer {


            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)

            try {
                if (it.status!!) {

                    if (it.data != null) {


                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            args.refresh.perform()
                            binding.rvNotifications.visibility = View.VISIBLE

                        } else {

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    } else {
                        Log.d(TAG, "observers: 3")
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {
                    Log.d(TAG, "observers: 4")
                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                Log.d(TAG, "observers: 5")
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }
        })
        storesViewModel.storeDetailsResponse.observe(this, Observer {

            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

            try {
                if (it.status!!) {

                    if (it.data != null) {
                        var bundle=Bundle()
                        /*parentID = it?.getInt(Constants.INTENT_TYPE_ID)!!
                         shopID = it.getInt(Constants.INTENT_PLACE_ID)
                         minOrder = it.getFloat(Constants.MIN_ORDER)
                         name = it.getString(Constants.INTENT_NAME, "")*/

                        if(it.data!!.shop.branchTypes.size>0) {
                            bundle.putInt(
                                Constants.INTENT_TYPE_ID,
                                it.data!!.shop.branchTypes.get(0).branchTypeId!!
                            )
                            bundle.putInt(Constants.INTENT_ID, it.data!!.shop.id)
                            bundle.putFloat(Constants.MIN_ORDER, it.data!!.shop.minOrder)
                            bundle.putString(Constants.INTENT_NAME, it.data!!.shop.name)

                            findNavController().navigate(
                                com.techcubics.albarkahyper.R.id.action_notificationsFragment_to_mainCategoriesFragment,
                                bundle
                            )
                        }

                    } else {
                        Log.d(TAG, "observers: 3")
                        //empty
                        Helper.ShowEmptyDialog(requireContext(),it.message)

                    }

                } else {
                    Log.d(TAG, "observers: 4")
                    //error
                    Helper.ShowEmptyDialog(requireContext(),it.message)
                }

            } catch (ex: Exception) {
                Log.d(TAG, "observers: 5")
                //error
                Helper.ShowErrorDialog(requireContext(),ex.message)
            }

        })


    }

    override fun events() {

        binding.toolbarNotifications.mainToolbar.setNavigationOnClickListener {

            dismiss()
        }
    }


    override fun showData(items: List<Notification>) {

        if (notificationsFragmentViewModel.notificationsResponse.value?.pagingator?.currentPage == 1) {
            resultList = items as ArrayList<Notification>
            notificationsAdapter = NotificationsAdaper(this)
            notificationsAdapter.setItemsList(resultList)
            binding.rvNotifications.adapter = notificationsAdapter
            binding.rvNotifications.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            //
            var pageDelegate =
                PagingDelegate.Builder(notificationsAdapter).attachTo(binding.rvNotifications)
                    .listenWith(this).build()


        } else {

            items.forEach {
                resultList.add(it)
            }
            notificationsAdapter.notifyDataSetChanged()
            onDonePaging()
        }



    }

    override fun onPage(p0: Int) {


        if (!isLoading) {
            Log.d(TAG, "onPage: ${p0}")
            if (notificationsFragmentViewModel.notificationsResponse.value?.pagingator?.hasMorePages!!) {
                isLoading = true
                binding.pagingLoadingImg.visibility = View.VISIBLE
                notificationsFragmentViewModel.getNotifications(notificationsFragmentViewModel.notificationsResponse.value?.pagingator?.currentPage!! + 1)
            }
        }
    }

    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility = View.GONE
        isLoading = false
    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onClick(type: String, id: Int?) {


        when(type){


            NotificationTypes.NotificationFurniture.value->{
                Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
                storesViewModel.getStoreDetails(id.toString()!!)
            }
            NotificationTypes.Coupon.value->{
                Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
                storesViewModel.getStoreDetails(id.toString()!!)
            }



        }
    }


}