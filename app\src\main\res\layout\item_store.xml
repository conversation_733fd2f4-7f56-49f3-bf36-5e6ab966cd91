<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/item_branch_type_container"
    android:layout_marginStart="0dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="1dp"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:clickable="true"

    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnAddFav"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_add_favorite"
            app:backgroundTint="@color/white"
            app:fabSize="mini"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/icon"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@null"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@tools:sample/avatars"


            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/icon">
            <TextView
                android:id="@+id/tvTitle"
                style="@style/label_item_stores_category_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="4dp"
                android:layout_marginBottom="8dp"
                android:background="@null"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"


                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/icon"
                tools:text="testtesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center">
                <TextView
                    style="@style/store_details_rating_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:background="@null"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/icon"
                    android:text="@string/min" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/minimum"
                        style="@style/store_details_rating_value"
                        android:textSize="12sp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="2dp"
                        android:background="@null"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="@color/app_color"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/icon"
                        android:text="500" />
                    <TextView
                        style="@style/store_details_rating_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="2dp"
                        android:background="@null"
                        android:ellipsize="end"
                        android:textSize="12sp"
                        android:maxLines="2"
                        android:textColor="@color/app_color"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/icon"
                        android:text="@string/currency_name" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp"
                android:gravity="center">

                <RatingBar
                    android:id="@+id/rate_bar"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:isIndicator="false"
                    android:theme="@style/ratingbar"/>

                <TextView
                    android:id="@+id/tvRating"
                    style="@style/store_details_rating_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    app:layout_constraintStart_toEndOf="@+id/rate_bar"
                    tools:text="9833" />

            </LinearLayout>

        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>