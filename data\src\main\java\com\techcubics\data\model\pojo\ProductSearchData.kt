package com.techcubics.data.model.pojo

import android.provider.MediaStore
import com.google.gson.annotations.SerializedName


data class ProductSearchData(

    @SerializedName("id")
    val id: Int? = null,
    @SerializedName("product_id")
    val productID: Int? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("price")
    val price: Float? = null,
    @SerializedName("video")
    val video: String? = null,
    @SerializedName("icon")
    val icon: String? = null,
    @SerializedName("images")
    val images: MutableList<Image>? = null,
    @SerializedName("sizes")
    val Sizes: MutableList<Size>? = null,
    @SerializedName("colors")
    val colorObjects: MutableList<ColorObject>? = null,
   //cat
    @SerializedName("shop")
    val shop: ProductSearchStoreData? = null,
    @SerializedName("is_fav")
    var isFav: Boolean? = null,
    @SerializedName("rate")
    val rate: Float? = null,
    @SerializedName("rate_count")
    val rate_count: Int? = null,
    @SerializedName("model_type")
    val model_type: String? = null,
    @SerializedName("qty_cart")
    var qtyCart: Int? = null,
    @SerializedName("minimum_order_number"   ) var minQty     : Int=1,
    @SerializedName("maximum_order_number"   ) var maxQty     : Int=1,
    @SerializedName("is_discount"          ) var isDiscount         : Boolean?          = null,
//    @SerializedName("discount_id"          ) var discountId         : Int?              = null,
//    @SerializedName("price_before"         ) var priceBefore        : Float?              = null,
//    @SerializedName("price_after"          ) var priceAfter         : Float?           = null,
//    @SerializedName("percent"              ) var percent            : Double?           = null
    @SerializedName("discounts"              ) var discount          : ArrayList<DiscountDto>?           = null,
)
