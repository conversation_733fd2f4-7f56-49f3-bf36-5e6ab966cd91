package com.techcubics.albarkahyper.ui.adapters.home


import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import com.techcubics.data.model.pojo.StoreDetailsBranchesData
import com.techcubics.albarkahyper.R

class SpinnerBranchesAdapter(val data: List<StoreDetailsBranchesData>,
                             context: Context,
                             resource: Int,

                             ): ArrayAdapter<StoreDetailsBranchesData>(context, resource, data) {

    lateinit var view:View
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        return initView(position, convertView, parent)
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        return initView(position, convertView, parent)
    }

    fun initView(position:Int,convertView: View?,parent: ViewGroup):View{

        if (convertView==null){
            view= LayoutInflater.from(getContext()).inflate(R.layout.item_spinner,parent,false);
        }

        var tvName: TextView = view.findViewById<TextView>(com.techcubics.albarkahyper.R.id.tvName)
       tvName.text=getItem(position)!!.name
        view.setTag(com.techcubics.style.R.integer.selected_value,getItem(position)!!.id)

     return view
    }


}