package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.navigation.Navigation
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.databinding.ItemStoreBinding
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.FavouriteStore
import com.techcubics.data.model.pojo.StoreByCategoryData
import com.techcubics.shared.constants.Constants
import org.koin.android.ext.android.get

class StoreHolderItem<T>(
    val binding: ItemStoreBinding,
    val context: Context,
    val onFavClickListener: IFavClickListener
) : RecyclerView.ViewHolder(binding.root) {


    private val TAG = "StoreHolderItem"
    private lateinit var intent: Intent
    private val SharedPreferencesManager: SharedPreferencesManager by lazy {
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }

    fun bind(data: T) {

        when (data) {

            is StoreByCategoryData -> {
                fillByCategory(data)
            }
//            is StoresNearbyData ->{
//                fillByNear(data)
//            }
//            is StoresNearbyByWordFurnitureData ->{
//                fillByNear(data)
//            }
            is FavouriteStore -> {
                fillByFavorite(data)
            }
        }


    }

    private fun fillByCategory(data: StoreByCategoryData) {

        binding.tvTitle.text = data.name
        data.logo?.let { Helper.loadImage(context, it, binding.icon) }
        binding.tvRating.text = data.rateCount.toString()
        binding.rateBar.rating = data.rate!!
        binding.minimum.text = data.districts?.get(0)?.minOrderPrice.toString()

        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }

        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                binding.btnAddFav.setOnClickListener {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                binding.btnAddFav.setOnClickListener {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
            else -> {}
        }


        binding.root.setOnClickListener {

            Log.d(TAG, "fillByCategory: inside item ")
            val bundle = Bundle()
            data.id?.let { it1 -> bundle.putInt(Constants.INTENT_PLACE_ID, it1) }
            data.districts?.get(0)?.minOrderPrice?.let { it1 -> bundle.putFloat(Constants.MIN_ORDER, it1) }
            data.name?.let { it1 -> bundle.putString(Constants.INTENT_NAME, it1) }
            data.branchTypes?.get(0).let {it1-> it1?.branchTypeId?.let { it2 ->
                bundle.putInt(Constants.INTENT_TYPE_ID,
                    it2
                )
            } }




            //  binding.root.findNavController().navigate(R.id.action_storeSearchCategoryResultFragment_to_storeDetailsFragment,bundle)


            Navigation
                .createNavigateOnClickListener(
                    R.id.view_storeMainCategories,
                    bundle
                ).onClick(binding.root)
        }
    }

    //  private  fun fillByNear(data:StoresNearbyData){
//
//        binding.tvBranchName.text=data.name
//        Helper.loadImage(context, data.logo, binding.logo)
//      binding.tvDescription.text=data.description
//      binding.tvAddress.text=data.address
//        binding.tvRating.text=data.rate_count.toString()
//        binding.rateBar.rating=data.rate
//
//      if (data.isOpened) {
//          binding.tvStatus.isEnabled = true
//          binding.tvStatus.text = context.getString(com.techcubics.style.R.string.opened)
//      }else{
//          binding.tvStatus.isEnabled = false
//         binding.tvStatus.text = context.getString(com.techcubics.style.R.string.closed)
//      }
//
//      if(SharedPreferencesManager.isLoggedIn().equals("false")){
//          binding.btnAddFav.visibility= View.GONE
//      }
//
//        when (data.isFav) {
//            true -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        bindingAdapterPosition,
//                        position =  bindingAdapterPosition,
//                        operation =  1
//                    )
//                }
//            }
//            false -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position =  bindingAdapterPosition,
//                        operation =  2
//                    )
//                }
//            }
//        }
//
//        binding.root.setOnClickListener {
//
//
//            val bundle=Bundle()
//            bundle.putInt(Constants.INTENT_ID, data.id)
//            binding.root.findNavController().navigate(R.id.action_storeSearchNearByResultFragment_to_storeDetailsFragment,bundle)
//
//
//
//
//
//
//        }
//    }
//
//    private  fun fillByNear(data:StoresNearbyByWordFurnitureData){
//
//        binding.tvBranchName.text=data.name
//        Helper.loadImage(context, data.logo, binding.logo)
//        binding.tvDescription.text=data.description
//        binding.tvAddress.text=data.address
//        binding.tvRating.text=data.rate_count.toString()
//        binding.rateBar.rating=data.rate
//
//        if (data.isOpened) {
//            binding.tvStatus.isEnabled = true
//            binding.tvStatus.text = context.getString(com.techcubics.style.R.string.opened)
//        }else{
//            binding.tvStatus.isEnabled = false
//            binding.tvStatus.text = context.getString(com.techcubics.style.R.string.closed)
//        }
//
//        if(SharedPreferencesManager.isLoggedIn().equals("false")){
//            binding.btnAddFav.visibility= View.GONE
//        }
//
//        when (data.isFav) {
//            true -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        bindingAdapterPosition,
//                        position =  bindingAdapterPosition,
//                        operation =  1
//                    )
//                }
//            }
//            false -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position =  bindingAdapterPosition,
//                        operation =  2
//                    )
//                }
//            }
//        }
//
//        binding.root.setOnClickListener {
//
//
//            val bundle=Bundle()
//            bundle.putInt(Constants.INTENT_ID, data.id)
//            binding.root.findNavController().navigate(R.id.action_storeSearchNearByResultFragment_to_storeDetailsFragment,bundle)
//
//
//
//
//
//
//        }
//    }
    private fun fillByFavorite(data: FavouriteStore) {
        binding.tvTitle.text = data.name
        data.logo.let { Helper.loadImage(context, it, binding.icon) }
        binding.tvRating.text = data.rateCount.toString()
        binding.rateBar.rating = data.rate

        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }

        binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
        binding.btnAddFav.setOnClickListener {
            onFavClickListener.onFavClick(
                position = bindingAdapterPosition,
                operation = 1
            )
        }

        binding.root.setOnClickListener { view ->
            val bundle = Bundle()
            data.shopID.let { it1 -> bundle.putInt(Constants.INTENT_ID, it1) }
            data.shopDistricts?.get(0)?.minOrderPrice?.let { it1 -> bundle.putFloat(Constants.MIN_ORDER, it1) }
            data.name.let { it1 -> bundle.putString(Constants.INTENT_NAME, it1) }
            Navigation.createNavigateOnClickListener(
                    R.id.view_storeMainCategories,
                    bundle
                ).onClick(binding.root)
        }
    }

}