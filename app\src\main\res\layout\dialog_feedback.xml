<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_round_gray"
    android:padding="?dialogPreferredPadding"
    android:theme="@style/Theme.FurnitureApp.MaterialComponent"
    tools:ignore="ContentDescription">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:src="@drawable/ic_feedback" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/rate_app_feedback"
            android:textColor="?android:textColorPrimary"
            android:textSize="22sp"
            android:textStyle="bold" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/text_input"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:hint="@string/rate_app_feedback_here"
            app:counterEnabled="true"
            app:counterMaxLength="500"
            app:errorEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_feedback"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top|start"
                android:minHeight="110dp" />

        </com.google.android.material.textfield.TextInputLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="end"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/bt_maybeLater"
                style="@style/Widget.Material3.Button.TextButton.Dialog.Flush"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textAppearance="?android:attr/textAppearanceButton" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/bt_feedbackSend"
                style="@style/Widget.Material3.Button.UnelevatedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:text="@string/send"
                android:textAllCaps="false"
                android:textAppearance="?android:attr/textAppearanceButton" />

        </LinearLayout>

    </LinearLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
