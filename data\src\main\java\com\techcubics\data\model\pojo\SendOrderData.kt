package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class SendOrderData(
    @SerializedName("sender_id")
    val sender_id:Int,
    @SerializedName("receiver_id")
    val receiver_id:Int,
    @SerializedName("user_id")
    val user_id:Int,
    @SerializedName("read")
    val read:<PERSON><PERSON><PERSON>,
    @SerializedName("message")
    val message:String,
    @SerializedName("date")
    val date:String,
    @SerializedName("time")
    val time:String,
    @SerializedName("format_date")
    val formatDate:String,
    @SerializedName("last_message")
    val lastMessage:String,
    @SerializedName("count_unread")
    val countUnread:Int
)