package com.techcubics.albarkahyper.ui.views.products.searchResult

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.techcubics.data.model.pojo.ColorObject
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IColorsClickListener
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentSelectColorInFilterBinding
import com.techcubics.albarkahyper.ui.adapters.home.ColorsAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.ArrayList

class SelectColorInFilterFragment : Fragment() , IColorsClickListener {

    private lateinit var binding : FragmentSelectColorInFilterBinding
    private lateinit var colorsAdapter: ColorsAdapter
    private  val productsViewModel: ProductsViewModel by viewModel<ProductsViewModel>()
    private lateinit var progressButton: ProgressButton
    private var selectedColor: ArrayList<ColorObject> = arrayListOf()
    private val mainViewModel: MainViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentSelectColorInFilterBinding.inflate(inflater, container, false)
        initView()
        observeViews()
        return binding.root
    }

    private fun observeViews() {
        productsViewModel.productColorsSizesResponse.observe(viewLifecycleOwner, Observer {

            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            binding.containerLayout.visibility = View.VISIBLE
            binding.btnSearchColor.constraintsLayout.visibility = View.VISIBLE

            if (it.status!!) {

                if (it.data != null) {
                    if (it.data!!.colors!!.size > 0) {
                        showColorData(it.data?.colors!!)
                    } else {

                    }
                }

            }
        })
    }

    private fun initView() {
        binding.toolbarFilter.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.btnSearchColor)
        binding.btnSearchColor.textView.text = getString(com.techcubics.style.R.string.done)
        binding.toolbarFilter.tvTitle.text = getString(com.techcubics.style.R.string.color)
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        binding.containerLayout.visibility = View.GONE
        colorsAdapter = ColorsAdapter(handler = this)
        productsViewModel.getColorsSizes()
        progressButton.binding.constraintsLayout.setOnClickListener {
            mainViewModel.setColorList(selectedColor)
            CoroutineScope(Dispatchers.Main).launch {
                progressButton.btnActivated()
                delay(1000)
                progressButton.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.done),null)
                delay(300)
                findNavController().popBackStack()
            }

        }
    }

    fun showColorData(data: MutableList<ColorObject>) {
         colorsAdapter.setItemsList(data)
        binding.includeFilterColorSection.rvColors.adapter = colorsAdapter
        binding.includeFilterColorSection.rvColors.layoutManager =
            GridLayoutManager(context, 4)
    }


    override fun onAddColorClick(color: ColorObject) {
        selectedColor.add(color)
    }

    override fun onRemoveColor(color: ColorObject) {
        selectedColor.remove(color)
    }



}