<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center_horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/portrait_new_pass_img"
                    android:layout_marginTop="93dp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/new_pass"
                    android:layout_marginTop="27.5dp"
                    android:textAppearance="@style/header_auth"/>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:boxStrokeWidth="0dp"
                    app:hintEnabled="false"
                    app:boxStrokeWidthFocused="0dp"
                    app:passwordToggleEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/password_edt"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:inputType="textPassword"
                        android:hint="@string/new_the_pass"
                        android:background="@drawable/et_style"
                        android:padding="10dp"
                        android:textAppearance="@style/label_auth_edittext" />
                </com.google.android.material.textfield.TextInputLayout>
                <include
                    android:id="@+id/validate_pass_group"
                    layout="@layout/include_password_validation_layout"/>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:boxStrokeWidth="0dp"
                    app:hintEnabled="false"
                    app:boxStrokeWidthFocused="0dp"
                    app:passwordToggleEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/confirm_password_edt"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:inputType="textPassword"
                        android:hint="@string/confirm_password"
                        android:background="@drawable/et_style"
                        android:padding="10dp"
                        android:textAppearance="@style/label_auth_edittext" />
                </com.google.android.material.textfield.TextInputLayout>

                <include
                    android:id="@+id/sign_btn_progress"
                    android:layout_width="match_parent"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_height="50dp"
                    android:layout_marginTop="37dp"
                    layout="@layout/btn_progress"
                    />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>