package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.ICategoryClickListener
import com.techcubics.albarkahyper.databinding.ItemSizeFilterBinding
import com.techcubics.albarkahyper.ui.adapters.product.ProductCategoryFilterHolderItem
import com.techcubics.data.model.pojo.Category


class FilterCategoryAdapter(val handler: ICategoryClickListener?=null): RecyclerView.Adapter<ProductCategoryFilterHolderItem>() {

    lateinit var items:List<Category>
    var check = false

    fun setItemsList(_items:List<Category>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductCategoryFilterHolderItem {
        val itemBinding = ItemSizeFilterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductCategoryFilterHolderItem(itemBinding,parent.context,handler=this.handler)
    }

    override fun onBindViewHolder(holder: ProductCategoryFilterHolderItem, position: Int) {
        if(check){
            holder.binding.size.isChecked = false
        }
        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
        return items.size
    }

    fun resetCategories() {
        check = true
    }

}