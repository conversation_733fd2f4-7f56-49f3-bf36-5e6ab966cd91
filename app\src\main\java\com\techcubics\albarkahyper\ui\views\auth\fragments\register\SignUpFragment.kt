package com.techcubics.albarkahyper.ui.views.auth.fragments.register

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.get
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.aceinteract.android.stepper.StepperNavListener
import com.aceinteract.android.stepper.StepperNavigationView
import com.aceinteract.android.stepper.menus.tab.TabNumberedStepperMenu
import com.aceinteract.android.stepper.menus.tab.TabStepperMenuItem
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.requests.auth.RegisterRequest
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentSignUpBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.utils.AuthUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class SignUpFragment : Fragment() {

    private lateinit var signUpBinding: FragmentSignUpBinding
    private lateinit var nextprogressBtn: ProgressButton
    private lateinit var previousprogressBtn: ProgressButton
    private val mainViewModel: MainViewModel by activityViewModels()
    private var facilityInfo: FacilityInfo? = null
    private var facilityLocationInfo: FacilityLocationInfo? = null
    private var personalData: PersonalData? = null
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val signUpViewModel by viewModel<AuthViewModel>()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private lateinit var popupDialog: PopupDialog
    private lateinit var stepper: StepperNavigationView
    private lateinit var navController: NavController

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        signUpBinding = FragmentSignUpBinding.inflate(inflater, container, false)

        stepper = signUpBinding.stepper

        val navHostFragment =
            childFragmentManager.findFragmentById(R.id.frame_stepper) as NavHostFragment
        navController = navHostFragment.navController
        signUpBinding.stepper.setupWithNavController(navController)


        nextprogressBtn = ProgressButton(requireContext())
        nextprogressBtn.init(signUpBinding.nextProgress)
        previousprogressBtn = ProgressButton(requireContext())
        previousprogressBtn.init(signUpBinding.previousProgress)
        nextprogressBtn.binding.textView.text = getString(com.techcubics.style.R.string.next)
        previousprogressBtn.binding.textView.text =
            getString(com.techcubics.style.R.string.previous)

//
        initViews()
        viewObserver()
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        return signUpBinding.root
    }

    private fun initViews() {
        previousprogressBtn.binding.constraintsLayout.setOnClickListener {
            stepper.goToPreviousStep()
        }

        nextprogressBtn.binding.constraintsLayout.setOnClickListener {
            setNextBtnConstraints()
        }
        stepper.stepperNavListener = object : StepperNavListener {
            override fun onCompleted() {

            }

            override fun onStepChanged(step: Int) {
                if (step in 1..2) {
                    (stepper.menu.getItem(step - 1) as TabStepperMenuItem).iconView.layoutDirection =
                        View.LAYOUT_DIRECTION_LTR
                }
                when (step) {
                    2 -> {
                        nextprogressBtn.binding.textView.text =
                            getString(com.techcubics.style.R.string.sign_up)
                    }
                    else -> {
                        nextprogressBtn.binding.textView.text =
                            getString(com.techcubics.style.R.string.next)
                    }
                }
            }

        }
    }

    private fun setNextBtnConstraints() {
        when (stepper.currentStep) {
            0 -> {
                checkFacilityInfo()
            }
            1 -> {
                checkFacilityLocation()
            }
            2 -> {
                if (checkPersonalDataValidity() && checkTermsAndConditions()) { signUp() }
            }
        }
    }

    private fun checkFacilityLocation() {
        if (facilityLocationInfo?.country != null &&
            facilityLocationInfo?.governorate != null &&
            facilityLocationInfo?.region != null &&
            facilityLocationInfo?.address != null &&
            facilityLocationInfo?.lat != null &&
            facilityLocationInfo?.lng != null
        ) {
            stepper.goToNextStep()
        } else {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.complete_facility_location_info))
        }
    }

    private fun checkFacilityInfo() {
        if (!facilityInfo?.facilityType.isNullOrEmpty() &&
            !facilityInfo?.facilityName.isNullOrEmpty()
        ) {
            Helper.loadingAnimationVisibility(View.VISIBLE,signUpBinding.actionLoadingAnimation.root)
            stepper.goToNextStep()
        } else {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.complete_facility_info))
        }
    }

    private fun signUp() {
        nextprogressBtn.btnActivated()
        facilityLocationInfo?.address?.let { sharedPreferencesManager.saveAddress(it) }
        personalData?.mobileCode?.let { sharedPreferencesManager.saveMobileCode(it) }
        signUpViewModel.register(
            RegisterRequest(
                first_name = personalData?.firstName.toString(),
                last_name = personalData?.lastName.toString(),
                phone = personalData?.phone.toString(),
                password = personalData?.password.toString(),
                password_confirmation = personalData?.passwordConfirmation.toString(),
                country_code = personalData?.countryCode.toString(),
                address = facilityLocationInfo?.address.toString(),
                lat =facilityLocationInfo?.lat.toString(),
                lng=facilityLocationInfo?.lng.toString(),
                country_id = facilityLocationInfo?.country?.id!!,
                region_id = facilityLocationInfo?.region?.id!!,
                governorate_id = facilityLocationInfo?.governorate?.id!!,
                facility_name = facilityInfo?.facilityName.toString(),
                branch_type = facilityInfo?.facilityType!!
            )
        )
        sharedPreferencesManager.saveName("${personalData?.firstName.toString()} ${personalData?.lastName}")
        sharedPreferencesManager.savePassword(personalData?.password.toString())
        personalData?.countryCode?.let { sharedPreferencesManager.saveMobileCodeWithoutPlus(it) }

    }

    private fun viewObserver() {
        mainViewModel.facilityTypeLiveData.observe(viewLifecycleOwner) { facilityInfo ->
            if (facilityInfo != null) {
                this.facilityInfo = facilityInfo
                mainViewModel.removeFacilityObserver()
            }
        }
        mainViewModel.facilityLocationLiveData.observe(viewLifecycleOwner) { facilityLocationInfo ->
            if (facilityLocationInfo != null) {
                this.facilityLocationInfo = facilityLocationInfo
                mainViewModel.removeFacilityLocationObserver()
            }
        }
        mainViewModel.personalDataLiveData.observe(viewLifecycleOwner) { personalData ->
            if (personalData != null) {
                this.personalData = personalData
                mainViewModel.removePersonalDataObserver()
            }
        }
        signUpViewModel.registerMutableLiveData.observe(viewLifecycleOwner) {
            if (it?.status!!) {
                sharedPreferencesManager.loggedIn("true")
                it.data?.let {  it1 ->
                    it1.facilityName?.let { it2 -> sharedPreferencesManager.saveShopName(it2) }
                    sharedPreferencesManager.saveName(it1.name)
                    sharedPreferencesManager.saveEmail(it1.phone)
                    sharedPreferencesManager.saveObject(Constants.USER, it1)
                    sharedPreferencesManager.saveObject(Constants.CHOOSEN_LATLNG,
                        it1.lat?.let { it2 -> it1.lng?.let { it3 -> Latlng(it2, it3) } })
                    sharedPreferencesManager.saveUserPhoto(it1.avatar!!)
                }
                nextprogressBtn.btnFinishedSuccessfully(
                    getString(com.techcubics.style.R.string.sign_up),
                    null
                )
                CoroutineScope(Dispatchers.Main).launch {
                    bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.signin_succeeded))
                    delay(1500)
                    bottomSheetAlertDialog.onDismiss()
                    findNavController().apply {
                        navigate(R.id.action_signupFragment_to_homeFragment)
                    }
                }
            } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                nextprogressBtn.btnFinishedFailed(
                    getString(com.techcubics.style.R.string.sign_up),
                    null
                )
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(com.techcubics.style.R.string.server_error)
                )
            } else {
                nextprogressBtn.btnFinishedFailed(
                    getString(com.techcubics.style.R.string.sign_up),
                    null
                )
                sharedPreferencesManager.loggedIn("false")
                bottomSheetAlertDialog.showDialog(it.message!!)
            }

        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    private fun checkTermsAndConditions():Boolean {
        if (!personalData?.termsAndConditions!!) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.accept_terms_and_conditions))
        }
        return personalData?.termsAndConditions==true
    }

    private fun checkPersonalDataValidity(): Boolean {
        var check = true
        if (personalData?.firstName.isNullOrEmpty()||personalData?.lastName.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.enter_valid_name))
            check = false
        } else if (personalData?.password.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.check_your_password_and_try_again))
            check = false
        } else if (personalData?.password.toString() != personalData?.passwordConfirmation.toString()
            ||personalData?.passwordConfirmation.isNullOrEmpty()
        ) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.confirm_password_isincorrect))
            check = false

        } else if (personalData?.phone.isNullOrEmpty() || !AuthUtils.validatePhone(
                personalData?.phone.toString(),
                personalData?.countryCode.toString()
            )
        ) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.phone_not_correct))
            check = false

        } else if (personalData?.countryCode.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.choose_country_code))
            check = false
        }
        return check
    }
}