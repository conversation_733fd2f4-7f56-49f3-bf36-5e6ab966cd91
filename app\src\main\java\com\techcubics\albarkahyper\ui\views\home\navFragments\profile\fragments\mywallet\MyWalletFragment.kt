package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mywallet

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentMyWalletBinding
import com.techcubics.albarkahyper.common.ProgressButton

class MyWalletFragment : Fragment() {


    lateinit var binding : FragmentMyWalletBinding
    lateinit var progressButton: ProgressButton
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMyWalletBinding.inflate(layoutInflater,container,false)
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.signBtnProgress)
        initViews()
        return binding.root
    }

    private fun initViews() {
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.my_wallet)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }
        progressButton.binding.textView.text = getString(com.techcubics.style.R.string.change)

        progressButton.binding.constraintsLayout.setOnClickListener {
            if(binding.credit.isChecked){
                findNavController().navigate(R.id.action_myWalletFragment_to_creditCardFragment)
            }else{
                findNavController().navigate(R.id.action_myWalletFragment_to_vodafonCashFragment)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}