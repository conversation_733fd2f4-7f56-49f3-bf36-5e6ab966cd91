<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/orderdetails_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.views.home.navFragments.profile.fragments.addresses.AddAddressFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:id="@+id/order_detail_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="75dp"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/order_success"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/ic_correct" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center_horizontal"
                        android:text="@string/order_done"
                        android:textAppearance="@style/label_orderdetails"
                        android:textColor="#4BAE4F"
                        android:textSize="15dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:text="@string/thetime"
                        android:textAppearance="@style/label_orderdetails"
                        android:textSize="15dp" />

                    <TextView
                        android:id="@+id/specified_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:gravity="center_horizontal"
                        android:singleLine="true"
                        android:text="20:45"
                        android:textAppearance="@style/label_orderdetails" />

                    <TextView
                        android:id="@+id/specified_day"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:gravity="center_horizontal"
                        android:singleLine="true"
                        android:text=""
                        android:textAppearance="@style/label_orderdetails" />
                </LinearLayout>


            </LinearLayout>

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="10dp">

                <ImageView
                    android:id="@+id/gray_img"
                    android:layout_width="match_parent"
                    android:layout_height="160dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="50dp"
                    android:layout_marginEnd="50dp"
                    android:layout_marginBottom="5dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/portrait_order_details_img" />

                <ImageView
                    android:id="@+id/chat_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|left"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_chating_light"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/chat_on"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|left"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/circle_oval_ripple"
                    android:src="@drawable/ic_chating_dark"
                    android:visibility="gone" />
            </FrameLayout>


            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/et_style_with_borders"
                android:orientation="vertical"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="5dp"
                    android:singleLine="true"
                    android:text="@string/order_details"
                    android:textAppearance="@style/label_orderdetails" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:text="@string/order_no"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/order_no"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:text="1234566"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_orderdetails" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/order_from_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:text="@string/order_from"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/order_from"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:maxLines="3"
                        android:singleLine="false"
                        android:text="name"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/order_address_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:text="@string/order_address"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/order_address"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:maxLines="3"
                        android:singleLine="false"
                        android:text="Address"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="10dp"-->
<!--                    android:orientation="horizontal"-->
<!--                    android:weightSum="2">-->

<!--                    <TextView-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginStart="16dp"-->
<!--                        android:layout_weight="1"-->
<!--                        android:singleLine="true"-->
<!--                        android:text="@string/product_name"-->
<!--                        android:textAppearance="@style/label_auth_edittext"-->
<!--                        android:textColor="#9D9D9D" />-->

<!--                    <TextView-->
<!--                        android:id="@+id/product_name"-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginStart="16dp"-->
<!--                        android:layout_marginEnd="16dp"-->
<!--                        android:layout_weight="1"-->
<!--                        android:gravity="end"-->
<!--                        android:maxLines="2"-->
<!--                        android:singleLine="false"-->
<!--                        android:textAlignment="viewEnd"-->
<!--                        android:textAppearance="@style/label_value_orderdetails"-->
<!--                        tools:text="-&#45;&#45;&#45;&#45;" />-->
<!--                </LinearLayout>-->

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:text="@string/order_status"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/order_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:text="Status"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:singleLine="true"
                        android:text="@string/order_notes"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/order_notes"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:text="Notes"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>
                <!--            <LinearLayout-->
                <!--                android:layout_width="match_parent"-->
                <!--                android:layout_height="wrap_content"-->
                <!--                android:layout_marginStart="16dp"-->
                <!--                android:layout_marginTop="16dp"-->
                <!--                android:layout_marginEnd="16dp"-->
                <!--                android:orientation="horizontal">-->

                <!--                <de.hdodenhof.circleimageview.CircleImageView-->
                <!--                    android:id="@+id/clinic_image_view"-->
                <!--                    android:layout_width="75dp"-->
                <!--                    android:layout_height="75dp"-->
                <!--                    android:src="@drawable/portrait_placeholder" />-->

                <!--                <TextView-->
                <!--                    android:id="@+id/service_name"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_gravity="center_vertical"-->
                <!--                    android:layout_marginStart="10dp"-->
                <!--                    android:layout_marginTop="8dp"-->
                <!--                    android:layout_marginEnd="10dp"-->
                <!--                    android:text="service"-->
                <!--                    android:textAppearance="@style/label_orderdetails" />-->
                <!--            </LinearLayout>-->


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:text="@string/coupon_price"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/coupon_price"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="3"
                        android:singleLine="false"
                        android:text="-----"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:text="@string/delivery_fees_title"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/delivery_fees"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="3"
                        android:singleLine="false"
                        android:text="-----"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:text="@string/instant_discount"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/instant_discount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="3"
                        android:singleLine="false"
                        android:text="-----"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:text="@string/products_total_only"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/product_total_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails"
                        tools:text="-----" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:maxLines="2"
                        android:singleLine="false"
                        android:text="@string/total_amount_title"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="#9D9D9D" />

                    <TextView
                        android:id="@+id/total_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textAlignment="viewEnd"
                        android:textAppearance="@style/label_value_orderdetails"
                        tools:text="-----" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ad_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <TableLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp">

                        <TableRow
                            android:id="@+id/table_row1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:weightSum="3">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:background="@drawable/et_style_with_borders"
                                android:gravity="center"
                                android:padding="5dp"
                                android:text="@string/product_name"
                                android:textAppearance="@style/label_auth_edittext"
                                android:textColor="#9D9D9D"/>

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="0.5"
                                android:background="@drawable/et_style_with_borders"
                                android:gravity="center"
                                android:padding="5dp"
                                android:text="@string/price"
                                android:textAppearance="@style/label_auth_edittext"
                                android:textColor="#9D9D9D"/>

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="0.5"
                                android:background="@drawable/et_style_with_borders"
                                android:gravity="center"
                                android:padding="5dp"
                                android:text="@string/quantity"
                                android:textAppearance="@style/label_auth_edittext"
                                android:textColor="#9D9D9D"/>
                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:background="@drawable/et_style_with_borders"
                                android:gravity="center"
                                android:padding="5dp"
                                android:text="@string/product_img"
                                android:textAppearance="@style/label_auth_edittext"
                                android:textColor="#9D9D9D"/>
                        </TableRow>

                        <TableRow
                            android:id="@+id/table_row2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/products_RV"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:listitem="@layout/item_product_details" />

                        </TableRow>


                    </TableLayout>

                </LinearLayout>

            </androidx.appcompat.widget.LinearLayoutCompat>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/error_layout"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
