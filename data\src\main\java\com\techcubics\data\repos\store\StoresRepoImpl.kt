package com.techcubics.data.repos.store

import android.util.Log
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.*
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import retrofit2.Response


class StoresRepoImpl(private val retrofitBuilder: RetrofitBuilder): RepositoryResponse, StoresRepo {

    private val TAG = "StoresRepo"
    override suspend fun getStoresByCategory(request: StoresByCategoryRequest): BaseResponse<MutableList<StoreByCategoryData>>? {

        return try {
            val result = retrofitBuilder.getBindObject()?.getStoresByCategory(
                branch_type_id = request.typeID,

                page = request.page
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getStoresByCategoryAndWord(request: StoresByCategoryRequest): BaseResponse<MutableList<StoreByCategoryData>>? {

        //(@Query("country_id") country_id:Int, @Query("branch_type_id") branch_type_id:Int, @Query("search") key_search:String?=null, @Query("page") page:Int

        //(@Query("country_id") country_id:Int, @Query("branch_type_id") branch_type_id:Int, @Query("search") key_search:String?=null, @Query("page") page:Int
        /* val data: MutableMap<String, String> = HashMap()
         data["country_id"] = request.countryID.toString()
         data["branch_type_id"] = request.typeID.toString()
         data["search"] = request.word.toString()
         data["page"] = request.page.toString()*/
        return try {
            val result = retrofitBuilder.getBindObject()?.getStoresByCategory(
                branch_type_id = request.typeID,

                key_search = request.word,
                page = request.page
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }

    }

    override suspend fun getNearby(request: NearbyStoresCallRequest): BaseResponse<MutableList<StoresNearbyData>>? {

        return try {
            val result = retrofitBuilder.getBindObject()
                ?.nearbyStoresCall( request = request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getNearbyAndWord(request: NearbyStoresByWordCallRequest): BaseResponse<StoreNearbyByWordData>? {

        return try {
            var result: Response<BaseResponse<StoreNearbyByWordData>>? = null
            result = retrofitBuilder.getBindObject()
                ?.nearbyStoresByWordCall( request)
            Log.d(TAG, "getNearbyAndWord: 1")
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getStoreDetails(request: StoreDetailsRequest): BaseResponse<StoreDetailsData>? {

        return try {
            val result = retrofitBuilder.getBindObject()
                ?.storeDetailsCall( id = request.id!!)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getStoreDetailsByQR(request: StoreDetailsByQRCallRequest): BaseResponse<StoreDetailsData>? {

        return try {
            val result = retrofitBuilder.getBindObject()
                ?.storeDetailsByQRCall( request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun addRate(request: AddRateRequest): BaseResponse<String>? {

        return try {
            val result = retrofitBuilder.getBindObject()
                ?.addRateCall( request = request)

            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }


    }

    override suspend fun getLatestRate(request: RatesRequest): BaseResponse<RatesData>? {

        return try {
            val result=retrofitBuilder.getBindObject()?.getRates( type = request.type.value, id = request.id)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getShareLink(request: GetShareLinkCallRequest): BaseResponse<String>? {
        return try {
            val result= retrofitBuilder.getBindObject()?.getShareLinkCall(request=request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

   override  suspend fun getProductsBySubCategory(shopID: Int, subCategoryId: Int): BaseResponse<ArrayList<StoreDetailsMenuProductData>>? {
        return try {
            val result = retrofitBuilder.getBindObject()
                ?.getProductsBySubCategory(shopID, subCategoryId)
            baseResponse(result)
        }catch (ex:Exception){
            handleServerExceptions(ex)
        }
    }

    override suspend fun prescriptionAddCall(addPrescriptionRequest: AddPrescriptionRequest): BaseResponse<Nothing>? {

        val shopIDBody: RequestBody = addPrescriptionRequest.shopID.toString().toRequestBody("text/plain;charset=utf-8".toMediaType())

        //photo
        val reqFile: RequestBody? =
            addPrescriptionRequest.photo?.let { RequestBody.create("image/*".toMediaType(), it) }
        val body: MultipartBody.Part? = reqFile?.let {
            MultipartBody.Part.createFormData("image", addPrescriptionRequest.photo?.name,
                it
            )
        }

        try {
            val result = retrofitBuilder.getBindObject()?.prescriptionAddCall( shop_id = shopIDBody, avatar = body)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

}