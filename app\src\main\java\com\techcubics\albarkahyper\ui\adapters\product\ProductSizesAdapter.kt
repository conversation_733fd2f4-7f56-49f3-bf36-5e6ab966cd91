package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemSizeBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductSizeViewHolder
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.data.model.pojo.Size


class ProductSizesAdapter(var items: List<Size>, private val onSizeSelected: OnItemsChangedListener) :
    RecyclerView.Adapter<ProductSizeViewHolder>() {

    var selectedSizeId: Int = if (items.isNotEmpty()) items[0].sizeID else 0

    fun setItemsList(_items: List<Size>) {
        notifyItemRangeRemoved(0, itemCount)
        items = _items
        selectedSizeId = if (items.isNotEmpty()) items[0].sizeID else 0
        notifyItemRangeInserted(0, itemCount)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductSizeViewHolder {
        val itemBinding =
            ItemSizeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductSizeViewHolder(
            itemBinding,
            parent.context,
            onSizeSelected,
            ::setSelectedSize,
            ::getSelectedSize,
            ::updateSelectedSize
        )
    }

    override fun onBindViewHolder(holder: ProductSizeViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int {
        return items.size
    }

    private fun updateSelectedSize() {
        notifyItemRangeChanged(0, itemCount)
    }

    private fun setSelectedSize(sizeId: Int) {
        selectedSizeId = sizeId
    }

    private fun getSelectedSize(): Int {
        return selectedSizeId
    }
}


