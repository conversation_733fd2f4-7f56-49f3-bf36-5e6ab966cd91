package com.techcubics.albarkahyper.ui.views.chat.viewmodels

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.OrderSendCallRequest
import com.techcubics.data.model.requests.TicketSendCallRequest
import com.techcubics.data.model.requests.chat.*

import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.chat.*

import com.techcubics.data.repos.product.ProductsRepo
import kotlinx.coroutines.launch

class ChatViewModel(
    private val orderChatRepo: OrderChatRepo,
    private val supportChatRepo: SupportChatRepo,
    private val liveChatRepo: LiveChatRepo,
    private val sharedPreferencesManager: SharedPreferencesManager
) : ViewModel() {

    private  val TAG = "ChatViewModel"
    val supportTicketResponse =  MutableLiveData< BaseResponse<MutableList<SupportTicketData>>>()
    val sendSupportResponse =  MutableLiveData<BaseResponse<SendSupportData>>()
    val orderRoomsResponse =  MutableLiveData<BaseResponse<MutableList<OrderRoomsData>>>()
    val orderMessagesResponse=MutableLiveData<BaseResponse<OrderMessageData>>()
    val orderSendResponse=MutableLiveData<BaseResponse<SendOrderData>>()
    val liveMessagesResponse =  MutableLiveData< BaseResponse<MutableList<LiveMessageData>>>()
    val sendLiveResponse =  MutableLiveData<BaseResponse<Any>>()
    val liveChatRoomsResponse =  MutableLiveData<BaseResponse<MutableList<LiveChatRoomData>>>()

    fun getSupportChatHistory(){
        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {

            val request= SupportTicketMessagesRequest( country_id = countryID)
            val rs=supportChatRepo.getHistory(request)
            supportTicketResponse.postValue(rs!!)
        }
    }

    fun sendToSupport(message:String){
        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            Log.d(TAG, "sendToSupport: ${sharedPreferencesManager.getToken()}")
            val request= TicketSendCallRequest(message=message, country_id = countryID)
            val rs=supportChatRepo.send(request)
            sendSupportResponse.postValue(rs!!)
        }

    }

    fun getOrderRoomsHistory(){
        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= OrderRoomsRequest( country_id = countryID)
            val rs=orderChatRepo.getRooms(request)
            orderRoomsResponse.postValue(rs!!)
        }
    }

    fun getRoomMessagesHistory(orderID:Int){
        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {

            val request= OrderMessagesRequest( country_id = countryID,orderID=orderID)
            val rs=orderChatRepo.getRoomHistory(request)
            orderMessagesResponse.postValue(rs!!)
        }
    }
    fun sendToOrder(message:String,orderID: Int){
        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= OrderSendCallRequest(message=message, country_id = countryID, order_id = orderID)
            val rs=orderChatRepo.send(request)
            orderSendResponse.postValue(rs!!)
        }

    }

    fun getLiveChatHistory(storeID:Int){
        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request =
                LiveMessagesRequest(country_id = countryID,storeID=storeID)
            val rs=liveChatRepo.getHistory(request)
            liveMessagesResponse.postValue(rs!!)
        }
    }

    fun sendToLive(message:String,store:Int){

        val countryID:Int=sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {

            val request= SendLiveRequest(country_id = countryID,message=message, storeID = store)
            val rs=liveChatRepo.send(request)
            sendLiveResponse.postValue(rs!!)
        }

    }

    fun getAllLiveChatRooms(){
        viewModelScope.launch {
            val rs=liveChatRepo.liveGetAllChats()
            liveChatRoomsResponse.postValue(rs!!)
        }
    }

}