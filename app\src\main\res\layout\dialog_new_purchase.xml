<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp"
    android:orientation="vertical"
    android:background="@drawable/bg_dialog">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView

            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/mypurchases"
            android:textAppearance="@style/label_updateprofile" />

        <EditText
            android:id="@+id/newPurchasesText"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/et_style_with_borders"
            android:padding="3dp"
            android:textAppearance="@style/label_edittext_updateprofile"
            android:textSize="14sp" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/date"
            android:textAppearance="@style/label_updateprofile" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/et_style_with_borders"
            android:drawableEnd="@drawable/ic_calender"
            android:orientation="horizontal"
            android:paddingEnd="24dp">

            <EditText
                android:id="@+id/date"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/et_style_with_borders"
                android:paddingStart="3dp"
                android:singleLine="true"
                android:inputType="number"
                android:clickable="true"
                android:cursorVisible="false"
                android:focusableInTouchMode="false"
                android:textAppearance="@style/label_edittext_updateprofile"
                android:textSize="14sp" />

            <ImageButton
                android:id="@+id/date_img"
                style="?android:buttonBarButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="5dp"
                android:src="@drawable/ic_calender" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/choose_calender"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:visibility="gone"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            >
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAlignment="viewStart"
                android:text="@string/choose_from_calender"
                android:textAppearance="@style/label_auth"
                android:textColor="@color/red"
                />
        </LinearLayout>

    </LinearLayout>
    <include
        android:id="@+id/sign_btn_progress"
        layout="@layout/btn_progress"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentEnd="true"
        android:layout_below="@id/newPurchasesText"
        android:layout_gravity="right"
        android:layout_marginTop="20dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        />


</LinearLayout>