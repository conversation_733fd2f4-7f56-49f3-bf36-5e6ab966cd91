package com.techcubics.albarkahyper.ui.views.stores

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.*
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.model.requests.profile.UpdateProfileRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.favourite.FavoritesRepo
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.data.repos.store.StoresRepo
import com.techcubics.shared.enums.FavoriteTypesEnum
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.shared.enums.ShareEnum
import kotlinx.coroutines.launch
import java.io.File

class StoresViewModel(private val storesRepo: StoresRepo, private val favoritesRepo: FavoritesRepo, private val productsRepo: ProductsRepo,private val sharedPreferencesManager: SharedPreferencesManager): ViewModel() {

    private  val TAG = "StoresViewModel"
    val storesByCategoryResponse= MutableLiveData<BaseResponse<MutableList<StoreByCategoryData>>>()
    val addRemoveFavoriteForStoreResponse= MutableLiveData<BaseResponse<String>>()
    val addRemoveFavoriteForProductResponse= MutableLiveData<BaseResponse<String>>()
    val nearByStoresResponse= MutableLiveData<BaseResponse<StoreNearbyByWordData>>()
    val storeDetailsResponse= MutableLiveData<BaseResponse<StoreDetailsData>>()
    val addRateResponse= MutableLiveData< BaseResponse<String>>()
    val ratesResponse= MutableLiveData<BaseResponse<RatesData>>()
    val addPrescriptionMutableLiveData = MutableLiveData<BaseResponse<Nothing>?>()
    val shareLinkResponse= MutableLiveData< BaseResponse<String>>()
    val addCartResponse : MutableLiveData<BaseResponse<CartData>> by lazy {
        MutableLiveData()
    }
    val productsBySubCategoryResponse:MutableLiveData<BaseResponse<ArrayList<StoreDetailsMenuProductData>>> by lazy{
        MutableLiveData()
    }




    fun getStoresByCategory(category:Int,word:String?=null,page:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            if(word==null){
                val request= StoresByCategoryRequest( countryID = countryID,page=page,typeID=category)
                val rs=storesRepo.getStoresByCategory(request)
                storesByCategoryResponse.postValue(rs!!)
            }else{
                Log.d(TAG, "getStoresByCategory: ${word}")

                val request= StoresByCategoryRequest( countryID = countryID,page=page,typeID=category, word = word)
                val rs=storesRepo.getStoresByCategoryAndWord(request)
                storesByCategoryResponse.postValue(rs!!)
            }


        }

    }
    fun addRemoveFavStore(id:Int){
        viewModelScope.launch {
            val request= AddRemoveFavoriteRequest( type = FavoriteTypesEnum.Furniture.value , id=id)
            val rs=favoritesRepo.addRemoveFav(request)
            addRemoveFavoriteForStoreResponse.postValue(rs!!)
        }

    }
    fun getStoresNearBy(latitude:String,longitude:String,word:String?=null,page:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= NearbyStoresByWordCallRequest( countryID = countryID,page=page,latitude=latitude, longitude = longitude,key_search=word)
            val rs=storesRepo.getNearbyAndWord(request)
            nearByStoresResponse.postValue(rs!!)

        }

    }
    fun getStoreDetails(id:String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {

            val request= StoreDetailsRequest( countryID = countryID, id = id)
            val rs=storesRepo.getStoreDetails(request)

            storeDetailsResponse.postValue(rs!!)


        }

    }
    fun getStoreDetailsByQR(code:String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {

            val request= StoreDetailsByQRCallRequest( country_id =countryID, code=code)
            val rs=storesRepo.getStoreDetailsByQR(request)
            storeDetailsResponse.postValue(rs!!)


        }

    }
    fun addRate(id:Int,value:Int,comment:String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            Log.d(TAG, "addRate: ${id},${value},${comment}")
            val request= AddRateRequest(countryID =countryID, type = RateTypesEnum.Store.value , rateID = id,degree=value,comment=comment)
            val rs=storesRepo.addRate(request)
            addRateResponse.postValue(rs!!)
        }

    }
    fun getLatestRate(id:Int){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {

            val request= RatesRequest( countryID =countryID, type = RateTypesEnum.Store,id=id)
            val rs=storesRepo.getLatestRate(request)
            ratesResponse.postValue(rs!!)

        }

    }
    fun addRemoveFavProduct(id:Int){
        viewModelScope.launch {
            val request= AddRemoveFavoriteRequest( type = FavoriteTypesEnum.Product.value , id=id)
            val rs=favoritesRepo.addRemoveFav(request)
            addRemoveFavoriteForProductResponse.postValue(rs!!)
        }

    }
    fun addToCart(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId,modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint,request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }

    fun getShareLink(url:String,id:String){
        val countryID:Int= sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request= GetShareLinkCallRequest( type = ShareEnum.Furniture.value, url = url, item_id =id)
            val rs=storesRepo.getShareLink(request)
            shareLinkResponse.postValue(rs!!)
        }

    }

    fun getProductsBySubCategory(shopID:Int,subCategoryId:Int){
       // val countryID:Int= sharedPreferencesManager.getCountryID().toInt()
        viewModelScope.launch {
            val rs=storesRepo.getProductsBySubCategory(shopID,subCategoryId)
            productsBySubCategoryResponse.postValue(rs!!)
        }
    }


    fun addPrescription(shopID: Int,photo: File) {

        viewModelScope.launch {
            val countryID:Int= sharedPreferencesManager.getCountryID().toInt()
            val request=AddPrescriptionRequest(countryID =countryID ,shopID=shopID,photo=photo)
            val updateProfileResponse = storesRepo.prescriptionAddCall(request)
            updateProfileResponse?.let { addPrescriptionMutableLiveData.postValue(it) }

        }
    }

    val cartItemRemovedResponse = MutableLiveData<BaseResponse<CartData>>()

    fun removeCartItem(modelType: String?, modelId: Int?) {
        viewModelScope.launch {
            val request = ItemRemovingRequest(modelId, modelType)
            val rs = productsRepo.removeCartItem( request)
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
    }

}