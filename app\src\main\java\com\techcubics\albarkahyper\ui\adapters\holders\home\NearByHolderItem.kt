package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.R

import com.techcubics.data.model.pojo.StoresNearbyData
import com.techcubics.albarkahyper.databinding.ItemNearbyBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.shared.constants.Constants

class NearByHolderItem (val binding: ItemNearbyBinding, val context: Context): RecyclerView.ViewHolder(binding.root) {

    private lateinit var intent: Intent

    fun bind(nearBy: StoresNearbyData){

        binding.tvTitle.text=nearBy.name
        Helper.loadImage(context,nearBy.logo,binding.icon)

        binding.root.setOnClickListener {view->
            val bundle= Bundle()
            bundle.putInt(Constants.INTENT_ID, nearBy.id)








        }
    }

}