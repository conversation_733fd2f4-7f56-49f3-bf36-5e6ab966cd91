<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:id="@+id/orders_layout">

    <include
        android:id="@+id/orderToolbar"
        layout="@layout/toolbar_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/orders_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/orderToolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/viewpagertab"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:background="@null"
            android:theme="@style/tab_theme_3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/orderToolbar"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed"
            app:tabRippleColor="@null"
            app:tabSelectedTextColor="@color/white"
            app:tabTextAppearance="@style/tab_theme_2" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/viewpagertab"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="80dp">
        </androidx.viewpager.widget.ViewPager>
    </LinearLayout>
    <include
        android:id="@+id/includeNetwork"
        android:visibility="gone"
        layout="@layout/include_no_network_layout"/>
</androidx.constraintlayout.widget.ConstraintLayout>