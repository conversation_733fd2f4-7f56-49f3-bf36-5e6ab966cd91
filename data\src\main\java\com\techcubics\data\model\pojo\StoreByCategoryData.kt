package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class StoreByCategoryData(

    @SerializedName("country")
    val country: Country? = null,

    @SerializedName("address")
    val address: String? = null,

    @SerializedName("lng")
    val lng: Any? = null,

    @SerializedName("districts")
    val districts: List<Districts?>? = null,

    @SerializedName("is_fav")
    var isFav: Boolean? = null,

    @SerializedName("chat_enable")
    val chatEnable: Boolean? = null,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("link_apple_store")
    val linkAppleStore: String? = null,

    @SerializedName("shop_id")
    val shopId: Int? = null,

    @SerializedName("rate_count")
    val rateCount: Int? = null,

    @SerializedName("branch_types")
    val branchTypes: List<BranchType?>? = null,

    @SerializedName("rate")
    val rate: Float? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("logo")
    val logo: String? = null,

    @SerializedName("link_google_play")
    val linkGooglePlay: String? = null,

    @SerializedName("id")
    val id: Int? = null,

    @SerializedName("slug")
    val slug: String? = null,

    @SerializedName("open")
    val open: Boolean? = null,

    @SerializedName("lat")
    val lat: String? = null,

    @SerializedName("qr_image")
    val qrImage: String? = null


    )




