<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_branch_type_container"
    android:background="@drawable/bg_facility_type_selector"
    android:layout_width="match_parent"
    android:padding="6dp"
    android:clickable="true"
    android:focusable="true"
    android:layout_marginHorizontal="20dp"
    android:layout_marginVertical="10dp"
    android:addStatesFromChildren="true"
    android:layout_gravity="center_horizontal"
    android:layout_height="wrap_content">

    <RadioButton
        android:id="@+id/rd_btn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:buttonTint="@drawable/title_selector"
        android:text="بازار"
        android:textAppearance="@style/textView_item_facility_type"
        android:textAlignment="viewEnd"
        android:textColor="@drawable/title_selector"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/img"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:scaleType="centerInside"
        android:layout_margin="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rd_btn"
        tools:srcCompat="@tools:sample/avatars" />
</androidx.constraintlayout.widget.ConstraintLayout>