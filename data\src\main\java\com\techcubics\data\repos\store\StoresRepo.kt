package com.techcubics.data.repos.store

import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.*
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.model.requests.profile.UpdateProfileRequest
import com.techcubics.data.remote.BaseResponse

interface StoresRepo {
    suspend fun getStoresByCategory(request: StoresByCategoryRequest): BaseResponse<MutableList<StoreByCategoryData>>?
    suspend fun getStoresByCategoryAndWord(request: StoresByCategoryRequest): BaseResponse<MutableList<StoreByCategoryData>>?
    suspend fun getNearby(request: NearbyStoresCallRequest): BaseResponse<MutableList<StoresNearbyData>>?
    suspend fun getNearbyAndWord(request: NearbyStoresByWordCallRequest): BaseResponse<StoreNearbyByWordData>?
    suspend fun getStoreDetails(request: StoreDetailsRequest): BaseResponse<StoreDetailsData>?
    suspend fun getStoreDetailsByQR(request: StoreDetailsByQRCallRequest): BaseResponse<StoreDetailsData>?
    suspend fun addRate(request: AddRateRequest): BaseResponse<String>?
    suspend fun getLatestRate(request: RatesRequest): BaseResponse<RatesData>?
    suspend fun getShareLink(request: GetShareLinkCallRequest): BaseResponse<String>?
    suspend fun getProductsBySubCategory(shopID: Int, subCategoryId: Int): BaseResponse<ArrayList<StoreDetailsMenuProductData>>?

    suspend fun prescriptionAddCall(addPrescriptionRequest: AddPrescriptionRequest):BaseResponse<Nothing>?




}