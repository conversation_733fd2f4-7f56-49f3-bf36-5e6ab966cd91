package com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.fragments

import android.content.res.Resources
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentFavoritesBinding
import com.techcubics.albarkahyper.common.NetworkChangeReceiver
import com.techcubics.albarkahyper.ui.views.auth.fragments.LoginFragment
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.shared.constants.EndPointConstants.furniture
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.java.KoinJavaComponent

class FavoritesFragment : Fragment() {

    private  val TAG = "HomeFragment"
    lateinit var binding: FragmentFavoritesBinding
    private lateinit var  mSectionsPagerAdapter:SectionsPagerAdapter
    private val authViewModel by viewModel<AuthViewModel>()
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        binding=FragmentFavoritesBinding.inflate(inflater,container,false)

        tabBar()

        binding.toolbarFav.tvTitle.text=resources.getText(com.techcubics.style.R.string.bottom_nav_favorite)

        if(NetworkChangeReceiver.isOnline(requireContext())){
            binding.favView.visibility = View.VISIBLE
            binding.includeNetwork.networkView.visibility = View.GONE
            init()
            observers()
        }else{
            binding.favView.visibility = View.GONE
            binding.includeNetwork.networkView.visibility = View.VISIBLE
        }
        return binding.root
    }


    fun  tabBar(){

        binding.includeNetwork.refresh.setOnClickListener {

            findNavController().run {
                popBackStack()
                navigate(com.techcubics.albarkahyper.R.id.favoritesFragment)
            }
        }

        /////////////////////////
        mSectionsPagerAdapter = SectionsPagerAdapter(childFragmentManager,resources)

        // Set up the ViewPager with the sections adapter.
        binding.container.setAdapter(mSectionsPagerAdapter);
         binding.tabs.setupWithViewPager(binding.container);
       //binding.tabs.getTabAt(0)!!.setCustomView(R.layout.tab_item_product)

        /* View tab = ((ViewGroup) tabLayout.getChildAt(0)).getChildAt(i);
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) tab.getLayoutParams();
            p.setMargins(0, 0, 16, 0);
            tab.requestLayout();*/

       /* for (i in 0 until binding.tabs.getTabCount()) {
            val tab = (binding.tabs.getChildAt(0) as ViewGroup).getChildAt(i)
            val p = tab.layoutParams as MarginLayoutParams
            p.setMargins(0, 0, 16, 0)
            tab.requestLayout()
        }*/

        for (i in 0 until binding.tabs.getTabCount()) {
            val tab = (binding.tabs.getChildAt(0) as ViewGroup).getChildAt(i)
            when(i){
                0-> tab.setBackgroundResource(com.techcubics.style.R.drawable.tab_shape_right)
                1-> tab.setBackgroundResource(com.techcubics.style.R.drawable.tab_shape_left)
            }

            tab.requestLayout()
        }



    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.VISIBLE)
    }

    fun init(){
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }

    }

    fun observers(){
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.favLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.fav_to_login)
                    }
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }
    }



}


class SectionsPagerAdapter(val fm: FragmentManager,val rs:Resources): FragmentStatePagerAdapter(fm){


    override fun getCount(): Int {
        return 2
    }

    override fun getItem(position: Int): Fragment {

        var fragment:Fragment?=null
        when(position) {
            0 -> fragment = FavoritesProductsFragment.instance
            1 -> fragment = FavoritesStoresFragment.instance
        }

        return fragment!!
    }

    override fun getPageTitle(position: Int): CharSequence? {

        when(position){
            0-> return rs.getString(com.techcubics.style.R.string.fav_tab_products_title)
            1-> return rs.getString(com.techcubics.style.R.string.fav_tab_stores_title)
        }
        return null
    }


}
