<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/constraints_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/btn_progress_ripple"
        android:clickable="true"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center_vertical">
            <ProgressBar
                android:id="@+id/progressBar2"
                style="?android:attr/progressBarStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="16dp"
                android:indeterminate="true"
                android:indeterminateTint="@color/white"
                android:indeterminateTintMode="src_atop"
                android:visibility="gone"
                tools:targetApi="lollipop" />

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/signin"
                android:textAppearance="@style/label_button" />

        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>

