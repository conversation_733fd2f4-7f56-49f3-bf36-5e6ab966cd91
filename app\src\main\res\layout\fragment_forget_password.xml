<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center_horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/portrait_forget_pass_img"
                    android:layout_marginTop="109.5dp"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/forget_password2"
                    android:layout_marginTop="19.43dp"
                    android:textAppearance="@style/header_auth"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/please_eneter_phone_email"
                    android:layout_marginTop="13dp"
                    android:textAppearance="@style/label_auth"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style"
                    android:weightSum="4"
                    android:layoutDirection="ltr">
                    <com.hbb20.CountryCodePicker
                        app:ccp_defaultNameCode="EG"
                        android:id="@+id/country_picker"
                        android:layout_width="wrap_content"
                        app:ccp_showNameCode="false"
                        android:layout_height="wrap_content"
                        />
                    <EditText
                        android:id="@+id/email_or_phone_edt"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/et_style"
                        android:textAppearance="@style/label_auth_edittext"
                        android:hint="@string/email_or_phone"
                        android:singleLine="false"
                        android:inputType="textNoSuggestions"
                        />
                </LinearLayout>
                <include
                    android:id="@+id/sign_btn_progress"
                    android:layout_width="match_parent"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_height="50dp"
                    android:layout_marginTop="32dp"
                    layout="@layout/btn_progress"
                    />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/navto_signin_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:text="@string/navto_signin_from_forgetpass"
                    android:layout_marginBottom="57dp"
                    android:textAppearance="@style/label_auth"
                    android:clickable="true"
                    android:background="?attr/selectableItemBackground"                    />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
        <include
            android:id="@+id/action_loading_animation"
            layout="@layout/include_action_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>