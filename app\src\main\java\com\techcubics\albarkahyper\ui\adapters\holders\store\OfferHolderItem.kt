package com.techcubics.albarkahyper.ui.adapters.holders.store

import android.content.Context
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.StoreDetailsOffersData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.databinding.ItemStoreOffersBinding
import com.techcubics.albarkahyper.ui.adapters.product.OfferProductsAdapter

import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.common.QuantityButtonsController
import org.koin.android.ext.android.get

class OfferHolderItem<T>(
    val binding: ItemStoreOffersBinding,
    val context: Context,
    val margin: Int = 0,
    val clickHandler: IOnAdapterItemClickHandler
) : RecyclerView.ViewHolder(binding.root) {
    private val TAG = "OfferHolderItem"
    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    private lateinit var quantityButtonsController: QuantityButtonsController
    private lateinit var modelType: String
    private var offerId = -1
    private var offerPrice=0f
//    private lateinit var progressButton: CircleProgressButton
    private var qtyCart=0
    private var maxQty=0
    fun bind(data: T) {

        when (SharedPreferencesManager.getLanguage()) {
            "ar" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "eg" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "sa" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            else -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
        }
//        progressButton = CircleProgressButton(context)
//        progressButton.initRoundButton(binding.btnOrder)

        when (data) {

            is StoreDetailsOffersData -> {
                Log.d(TAG, "bind: 1")
                fillByStore(data)
            }
        }
        quantityButtonsController = QuantityButtonsController(
            binding.quantityController.root,
            null,
            context,
            maxQty = Int.MAX_VALUE,
            minQty = 1
        )
        quantityButtonsController.setIsItem(updateQuantity = ::updateQuantity,
            removeCartItem=::removeCartItem)
        quantityButtonsController.updateQuantity(qtyCart)
        quantityButtonsController.isBtnDelete()

        if (qtyCart>0){
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
        }else{
            binding.btnOrder.root.visibility = View.VISIBLE
            binding.quantityCont.visibility = View.INVISIBLE
        }

    }

    private fun fillByStore(offer: StoreDetailsOffersData) {
        modelType = RateTypesEnum.Offer.value
        offerId=offer.offerID
        offerPrice=offer.price
        qtyCart = offer.quantityCart
        maxQty = 1

        // binding.logo.visibility= View.INVISIBLE
        // binding.tvBranchName.visibility= View.INVISIBLE


        binding.tvTitle.text = offer.name

        binding.tvCurrentPrice.text =
            "${offer.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        if(offer.Images.size>0) {
            Helper.loadImage(context, offer.Images[0].path, binding.imgThumb)
        }
        //

        var productsAdapter: OfferProductsAdapter = OfferProductsAdapter()
        productsAdapter.items = offer.products
        binding.rvOfferProducts.adapter = productsAdapter

//        binding.rvOfferProducts.layoutManager =GridLayoutManager(context,2)
        val spanCount = if(offer.products.size>3) 3 else offer.products.size
        binding.rvOfferProducts.layoutManager = GridLayoutManager(context, spanCount)

        itemView.setOnClickListener {
            clickHandler.onItemClicked(offer.id, RateTypesEnum.Offer.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
//            if(SharedPreferencesManager.isLoggedIn() == "true"){
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility=View.VISIBLE
            quantityButtonsController.updateQuantity(1)
            clickHandler.addToCart(
                EndPointConstants.add_offer,
                -1,
                offer.modelType,
                offer.offerID,
                1,
                offerPrice,
                maxQty,
                1,
                0f,
                bindingAdapterPosition
            )
        }
    }

    private fun updateQuantity(
        qty: Int,
    ) {
        clickHandler.addToCart(
            EndPointConstants.add_offer,
            -1,
            modelType,
            offerId,
            qty,
            offerPrice,
            maxQty,
            1,
            0f,
            bindingAdapterPosition
        )
    }
    private fun removeCartItem(){
        binding.btnOrder.root.visibility = View.VISIBLE
        binding.quantityCont.visibility=View.INVISIBLE
        clickHandler.removeItemFromCart(
            modelType,
            offerId,
            0f,
            bindingAdapterPosition
        )
    }
}