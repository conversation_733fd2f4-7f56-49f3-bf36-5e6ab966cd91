package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class LiveMessageData(
    @SerializedName("direction"       ) var direction      : String? = null,
    @SerializedName("sender_id"       ) var senderId       : Int?    = null,
    @SerializedName("seen"            ) var seen           : Int?    = null,
    @SerializedName("message"         ) var message        : String? = null,
    @SerializedName("conversation_id" ) var conversationId : Int?    = null,
    @SerializedName("created_at"      ) var createdAt      : String? = null

)
