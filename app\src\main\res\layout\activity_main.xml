<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">
<LinearLayout
    android:id="@+id/home_main_banner_cont"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:elevation="16dp"
    android:visibility="gone"
    android:background="#85000000">
    <include
        android:id="@+id/home_main_banner"
        layout="@layout/main_banner"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="25dp"
        app:layout_constraintBottom_toBottomOf="@id/fragmentContainerView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/fragmentContainerView" />

</LinearLayout>

    <ImageButton
        android:id="@+id/btnOpenCart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center"
        android:background="@null"
        android:src="@drawable/ic_navbar_cart"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/frameLayout"

        app:layout_constraintStart_toStartOf="@+id/frameLayout"
        app:layout_constraintTop_toBottomOf="@+id/fragmentContainerView" />

    <fragment
        android:id="@+id/fragmentContainerView"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toTopOf="@id/frameLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navGraph="@navigation/main_nav_graph" />

    <FrameLayout
        android:id="@+id/frameLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottomNavigationView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:elevation="0dp"
            android:focusable="true"
            android:padding="2dp"
            android:theme="@style/bottomBarStyle"
            app:elevation="0dp"
            app:itemIconTint="@drawable/bottom_nav_effect"
            app:itemTextColor="@android:color/darker_gray"
            app:labelVisibilityMode="labeled"
            app:menu="@menu/bottom_menu">


        </com.google.android.material.bottomnavigation.BottomNavigationView>

    </FrameLayout>

    <include
        android:id="@+id/includeNetwork"
        layout="@layout/include_no_network_layout"
        android:visibility="gone" />


    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--  app:itemIconTint="@drawable/behaviour_bottom_nav"-->
</androidx.constraintlayout.widget.ConstraintLayout>