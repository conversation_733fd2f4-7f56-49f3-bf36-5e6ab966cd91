<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <TextView
        android:id="@+id/textView5"
        style="@style/store_details_rating_value_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/your_rate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RatingBar
        android:id="@+id/rate_bar"
        style="?android:attr/ratingBarStyleSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:isIndicator="false"
        android:numStars="5"
        android:stepSize="1"
        android:theme="@style/ratingbar"
        app:layout_constraintBottom_toBottomOf="@+id/textView5"
        app:layout_constraintStart_toEndOf="@+id/textView5"
        app:layout_constraintTop_toTopOf="@+id/textView5" />

    <EditText
        android:id="@+id/etComment"
        style="@style/store_details_rating_comment_box"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/et_style_with_borders"
        android:gravity="top|start"
        android:hint="@string/rate_hint"
        android:lines="5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView5" />

    <include
        android:id="@+id/btnRate"
        layout="@layout/btn_progress"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etComment" />
</androidx.constraintlayout.widget.ConstraintLayout>