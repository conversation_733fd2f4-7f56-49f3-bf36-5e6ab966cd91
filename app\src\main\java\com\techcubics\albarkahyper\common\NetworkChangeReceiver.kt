package com.techcubics.albarkahyper.common

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import com.techcubics.shared.utils.NetworkUtil
import com.techcubics.shared.utils.NetworkUtil.getConnectivityStatusString

class NetworkChangeReceiver(context: Context?) : BroadcastReceiver() {
    var networkState: Boolean
    init {
        val status = getConnectivityStatusString(context!!)
        networkState = status != NetworkUtil.NETWORK_STATUS_NOT_CONNECTED
        isOnline = networkState
    }

    companion object {
        var isOnline = false
        fun isOnline(context: Context): Boolean {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (connectivityManager != null) {
                val capabilities =
                    connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
                if (capabilities != null) {
                    if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                        Log.i("Internet", "NetworkCapabilities.TRANSPORT_CELLULAR")
                        return true
                    } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                        Log.i("Internet", "NetworkCapabilities.TRANSPORT_WIFI")
                        return true
                    } else if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                        Log.i("Internet", "NetworkCapabilities.TRANSPORT_ETHERNET")
                        return true
                    }
                }
            }
            return false
        }
    }

    @SuppressLint("LogNotTimber", "ResourceAsColor")
    override fun onReceive(context: Context?, intent: Intent) {
        val status = getConnectivityStatusString(context!!)
        if ("android.net.conn.CONNECTIVITY_CHANGE" == intent.action) {
            if (status == NetworkUtil.NETWORK_STATUS_NOT_CONNECTED){
                isOnline=false
            }else {
                isOnline=true
            }

        }
    }
}