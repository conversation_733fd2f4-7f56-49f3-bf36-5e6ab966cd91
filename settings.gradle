pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()


    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }



    }
}
rootProject.name = "Albarka<PERSON><PERSON>"
include ':app'
include ':shared'
include ':data'
include ':style'
