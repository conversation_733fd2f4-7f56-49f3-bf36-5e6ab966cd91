package com.techcubics.data.model.pojo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


@Parcelize
data class ProductDetailsDto(

    @SerializedName("id"          ) var id          : Int?              = null,
    @SerializedName("product_id"  ) var productId   : Int?              = null,
    @SerializedName("name"        ) var name        : String?           = null,
    @SerializedName("slug"        ) var slug        : String?           = null,
    @SerializedName("description" ) var description : String?           = null,
    @SerializedName("price"       ) var price       : Float?              = null,
    @SerializedName("video"       ) var video       : String?           = null,
    @SerializedName("icon"        ) var icon        : String?           = null,
    @SerializedName("images"      ) var images      : ArrayList<Image> = arrayListOf(Image("",-1,"")),
    @SerializedName("sizes"       ) var sizes       : ArrayList<Size>  = arrayListOf(),
    @SerializedName("colors"      ) var colors      : ArrayList<ColorObject> = arrayListOf(),
    @SerializedName("category"    ) var category    : Category?         = Category(),
    @SerializedName("shop"        ) var shop        : ProductDetailsStore?             = ProductDetailsStore(),
    @SerializedName("is_fav"      ) var isFav       : Boolean?          = null,
    @SerializedName("rate"        ) var rate        : Float?              = null,
    @SerializedName("rate_count"  ) var rateCount   : Int?              = null,
    @SerializedName("model_type"  ) var modelType   : String?           = null,
    @SerializedName("qty_cart"    ) var qtyCart     : Int?           = null,
    @SerializedName("minimum_order_number"   ) var minQty     : Int=1,
    @SerializedName("maximum_order_number"   ) var maxQty     : Int=1,
    @SerializedName("subCategory"    ) var subCategory    : SubCategory?         = SubCategory(),
    @SerializedName("is_discount"          ) var isDiscount         : Boolean?          = null,
//    @SerializedName("discount_id"          ) var discountId         : Int?              = null,
//    @SerializedName("price_before"         ) var priceBefore        : Float?              = null,
//    @SerializedName("price_after"          ) var priceAfter         : Float?           = null,
//    @SerializedName("percent"              ) var percent            : Double?           = null,
    @SerializedName("discounts") var discount: ArrayList<DiscountDto>? = null,

//    var position:Int?=0,
//    var showCartInfo: Boolean?=false,
//



):Parcelable


@Parcelize
data class ProductDetailsStore(
    @SerializedName("id") var id: Int? = null,
    @SerializedName("shop_id") var shopID: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("slug") var slug: String? = null,
    @SerializedName("description") var description: String? = null,
    @SerializedName("logo") var logo: String? = null,
    @SerializedName("address") var address: String? = null,
    @SerializedName("is_fav") var isFav: Boolean? = null,
    @SerializedName("rate") var rate: Float? = null,
    @SerializedName("rate_count") var rateCount: String? = null,
    @SerializedName("open") var isOpen: Boolean? = null,
    @SerializedName("link_google_play") var linkGooglePlay: String? = null,
    @SerializedName("link_apple_store") var linkAppleStore: String? = null,
    @SerializedName("qr_image") var qrImage: String? = null,
    @SerializedName("lat") var lat: String? = null,
    @SerializedName("lng") var lng: String? = null,
    @SerializedName("share_link") var shareLink: String? = null,
    @SerializedName("minimum_order_price") var minOrder: Float = 0f,
    @SerializedName("districts") var districts: ArrayList<Districts> = arrayListOf(),

    ):Parcelable

data class Ranking(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("category_id") var categoryId: Int? = null,
    @SerializedName("code") var code: String? = null,
    @SerializedName("image") var image: String? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("count") var count: String? = null

)
