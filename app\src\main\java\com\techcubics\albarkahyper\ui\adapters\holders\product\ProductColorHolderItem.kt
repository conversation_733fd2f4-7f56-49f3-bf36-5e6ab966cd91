package com.techcubics.albarkahyper.ui.adapters.holders.product


import android.content.Context
import android.graphics.Color
import android.util.Log
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.ColorObject
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IColorsClickListener
import com.techcubics.albarkahyper.databinding.ItemColorBinding


class ProductColorHolderItem(
    val binding: ItemColorBinding,
    val context: Context,
    val handler: IColorsClickListener?
): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "ProductColorHolderItem"
    private  var isSelected:Boolean=false
    fun bind(colorObject: ColorObject, check: Boolean){

        try {
            Helper.customView(
                binding.colorCircle,
                Color.parseColor(colorObject.code),
                Color.parseColor(colorObject.code),
                1
            )

            binding.root.setOnClickListener{

                if(handler == null){
                }else{
                    if(check){
                        isSelected = false
                    }

                    if(isSelected==false){
                        Log.i("here","select")
                        isSelected=true
                        Helper.customView(
                            binding.root,
                            com.techcubics.style.R.color.app_color,
                            com.techcubics.style.R.color.app_color,
                            1
                        )
                        handler?.onAddColorClick(colorObject)
                    }else{
                        Log.i("here","not select")
                        isSelected=false
                        binding.root.background=null
                        handler?.onRemoveColor(colorObject)
                    }
                }


            }
        }
        catch (ex:Exception){
            Helper.customView(
                binding.colorCircle,
                com.techcubics.style.R.color.app_color,
                com.techcubics.style.R.color.app_color,
                1
            )
        }

    }





}