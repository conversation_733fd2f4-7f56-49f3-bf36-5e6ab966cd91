<?xml version="1.0" encoding="utf-8"?>

<androidx.appcompat.widget.LinearLayoutCompat
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/et_style_with_borders">

        <EditText
            android:id="@+id/txtSearchWord"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="@string/search_for_orders"
            android:inputType="text"
            android:imeOptions="actionSearch"
            android:padding="13dp"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@null"
            style="@style/label_searchbox_order"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.appcompat.widget.LinearLayoutCompat>












