package com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.fragments


import android.os.Bundle
import android.text.Html
import android.text.Spanned
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.CartData
import com.techcubics.data.model.pojo.FavouriteProduct
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentFavoritesProductsBinding
import com.techcubics.albarkahyper.ui.adapters.product.ProductsPagingAdapter
import com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.viewmodels.FavoritesViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.shared.enums.RateTypesEnum
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*
import kotlin.collections.ArrayList


class FavoritesProductsFragment : Fragment(), IPagePagedRowset<FavouriteProduct>,
    IFavClickListener, IOnAdapterItemClickHandler {

    private lateinit var binding: FragmentFavoritesProductsBinding

    //    private  val binding get()=_binding!!
    private lateinit var productsPagingAdapter: ProductsPagingAdapter<FavouriteProduct>
    private val sharedFragmentViewModel: FavoritesViewModel by viewModel<FavoritesViewModel>()
    private var isLoading: Boolean = false
    private lateinit var resultList: ArrayList<FavouriteProduct>

    private var _operation: Int = -1
    private var _position: Int? = null

    //    private lateinit var favProductProgressButton : CircleProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private var totalPrice = 0f

    //    private val authViewModel: AuthViewModel by lazy {
//        ViewModelProvider(requireActivity())[AuthViewModel::class.java]
//    }
    private val authViewModel by viewModel<AuthViewModel>()
    private val SharedPreferencesManager: SharedPreferencesManager by inject()

    private val TAG = "FavoritesProductsFragment"

    companion object {
        val instance: FavoritesProductsFragment = FavoritesProductsFragment()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment

//        if (_binding == null) {
        binding = FragmentFavoritesProductsBinding.inflate(inflater, container, false)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        init()
//        }
        observers()
        events()
        return binding.root
    }

    override fun init() {
        setProductAdapter()
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.pagingLoadingImg.visibility = View.GONE
        binding.includeQtyCartInfo.root.visibility = View.GONE
        binding.rvProducts.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        sharedFragmentViewModel.getCart()
        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            showHidePlaceHolder(
                show = true,
                type = LottieIconEnum.Error,
                message = getString(com.techcubics.style.R.string.message_error_loading_login_required)
            )
        } else {
            sharedFragmentViewModel.getProducts(1)
        }
        calcPrice(minOrder, totalPrice)

    }

    override fun observers() {

        sharedFragmentViewModel.getCartResponse.observe(viewLifecycleOwner) { cartRes ->
            if (cartRes != null) {
                if (cartRes.status == true && cartRes.data != null && cartRes.data?.size!! > 0) {
                    minOrder = cartRes.data!![0].shop?.districts?.get(0)?.minOrderPrice ?: 0f
                    totalPrice = cartRes.data!![0].totalPrice ?: 0f
                    calcPrice(minOrder, totalPrice)
                }
                sharedFragmentViewModel.getCartResponse.value = null
            }
        }
        sharedFragmentViewModel.productsResponse.observe(viewLifecycleOwner, Observer { it ->

            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)

            try {
                if (it.status!!) {

                    if (it.data != null) {

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.includeQtyCartInfo.root.visibility = View.VISIBLE
                            binding.rvProducts.visibility = View.VISIBLE
                            showHidePlaceHolder(
                                show = false,
                                type = null,
                                message = null
                            )
                        } else {
                            binding.pagingLoadingImg.visibility = View.GONE
                            binding.includeQtyCartInfo.root.visibility = View.GONE
                            binding.rvProducts.visibility = View.GONE
                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    } else {
                        binding.pagingLoadingImg.visibility = View.GONE
                        binding.includeQtyCartInfo.root.visibility = View.GONE
                        binding.rvProducts.visibility = View.GONE
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                binding.pagingLoadingImg.visibility = View.GONE
                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }
        })

        sharedFragmentViewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner, Observer {


            try {

                if (it.status!!) {

                    resultList.removeAt(_position!!)

                    if (resultList.size > 0) {
                        productsPagingAdapter.notifyDataSetChanged()
                    } else {
                        binding.pagingLoadingImg.visibility = View.GONE
                        binding.includeQtyCartInfo.root.visibility = View.GONE
                        binding.rvProducts.visibility = View.GONE
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = getString(com.techcubics.style.R.string.message_empty_list_general)
                        )
                    }

                } else {

                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {


                binding.pagingLoadingImg.visibility = View.GONE
                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }


        })

        sharedFragmentViewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null) {
                val offset = if (qty == minQty) qty else 1
                val p = ((totalPrice * 100) - (offset * (itemPrice * 100))) / 100
                totalPrice = p
                if (it.message.toString() != Constants.SERVER_ERROR) {
                    if (it.status == true) {
                        totalPrice = it.data?.totalPrice ?: 0f
                    } else {
                        productsPagingAdapter.notifyItemChanged(itemPosition)
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }
                } else if (it.message.toString().contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                calcPrice(minOrder, totalPrice)
                sharedFragmentViewModel.addCartResponse.value = null
            }
        }

        sharedFragmentViewModel.cartItemRemovedResponse.observe(context as LifecycleOwner) { cart ->
            if (cart != null) {
                if (!cart.status!!) {
                    val p = ((totalPrice * 100) + (qty * (itemPrice * 100))) / 100
                    totalPrice = p
                    bottomSheetAlertDialog.showDialog(cart.message.toString())

                } else {
                    totalPrice = cart.data?.totalPrice ?: 0f
                    if (resultList[itemPosition].isDiscount==true){
                        resultList[itemPosition].discount?.get(0)?.quantityCart = 0
                    }else{
                        resultList[itemPosition].qtyCart = 0
                    }
                }
                productsPagingAdapter.items = resultList
                productsPagingAdapter.notifyItemChanged(itemPosition)
                sharedFragmentViewModel.cartItemRemovedResponse.value = null
            }
            calcPrice(minOrder, totalPrice)
        }

    }

    private fun updateProductItem(data: CartData?) {
        val newQtyCart =
            data?.cartDetails?.find { p ->
                p.modelId == resultList[itemPosition].productID
            }?.qty
        if (newQtyCart != null) {
            resultList[itemPosition].qtyCart = newQtyCart
        }
//        var currQty = resultList[itemPosition].qtyCart
//        currQty+=offset
//        resultList[itemPosition].qtyCart = currQty

    }

    //    private var itemPage = 1
//    private fun getCurrentPage() {
//        val offset = 10
//        val numPages = ceil(resultList.size.toFloat() / offset.toFloat()).toInt()
//        for (i in 0 until numPages) {
//            val start = (offset * i)
//            val end = offset * (i + 1)
//            if (itemPosition in start until end) {
//                itemPage = i + 1
//                break
//            }
//        }
//    }
//    override fun onResume() {
//        super.onResume()
//        if(SharedPreferencesManager.isLoggedIn().equals("false")){
//            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
//            showHidePlaceHolder(show=true,type= LottieIconEnum.Error ,message=getString(com.techcubics.style.R.string.message_error_loading_login_required))
//        }else{
//            sharedFragmentViewModel.getProducts(1)
//        }
//    }
    override fun events() {
        binding.includeQtyCartInfo.openCartBtn.setOnClickListener {
            val b = Bundle()
            b.putString(Constants.INTENT_PAGE_TYPE, "store_details")
            findNavController().navigate(R.id.view_cart, b)
        }

    }

    private fun setProductAdapter() {

        productsPagingAdapter =
            ProductsPagingAdapter(onFavClickListener = this, onClickHandler = this)
        binding.rvProducts.adapter = productsPagingAdapter
        val manager = GridLayoutManager(requireContext(), 2)
        binding.rvProducts.layoutManager = manager
        var pageDelegate =
            PagingDelegate.Builder(productsPagingAdapter).attachTo(binding.rvProducts)
                .listenWith(this).build()
    }

    override fun showData(items: List<FavouriteProduct>) {

        if (sharedFragmentViewModel.productsResponse.value!!.pagingator?.currentPage == 1) {
            resultList = items as ArrayList<FavouriteProduct>
//            productsPagingAdapter = ProductsPagingAdapter<FavouriteProduct>(onFavClickListener=this, onClickHandler = this)
            productsPagingAdapter.setItemsList(resultList)
//            binding.rvProducts.adapter = productsPagingAdapter
//            binding.rvProducts.layoutManager =
//                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

//            var pageDelegate =
//                PagingDelegate.Builder(productsPagingAdapter).attachTo(binding.rvProducts)
//                    .listenWith(this).build()
//            productsAdapter.notifyDataSetChanged()


        } else {

            items.forEach {
                val p = resultList.find { p -> p.id == it.id }
                val index = resultList.indexOf(p)
                if (p == null) {
                    resultList.add(it)
                } else {
                    resultList[index] = it
                }
            }
            onDonePaging()
        }
        productsPagingAdapter.notifyDataSetChanged()

        Log.d(TAG, "showData: ${resultList.size}")

    }

    override fun onPage(p0: Int) {


        if (!isLoading) {
            Log.d(TAG, "onPage: ${p0}")
            if (sharedFragmentViewModel.productsResponse.value!!.pagingator!!.hasMorePages) {
                isLoading = true
                binding.pagingLoadingImg.visibility = View.VISIBLE
                sharedFragmentViewModel.getProducts(sharedFragmentViewModel.productsResponse.value!!.pagingator!!.currentPage + 1)
            }
        }
    }

    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility = View.GONE
        isLoading = false
    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {
            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                }
                else -> throw IllegalStateException("error")
            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onFavClick(parent: Int, position: Int, operation: Int) {

        _operation = operation
        _position = position
        sharedFragmentViewModel.addRemoveProductFav(resultList[position].productID)
    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle = Bundle()
        bundle.putInt(Constants.INTENT_ID, itemId ?: -1)
        findNavController().navigate(R.id.view_productDetails, bundle)

    }

    private var qty = 0
    private var maxQty = 0
    private var minQty = 0
    private var minOrder = 0f
    private var itemPrice = 0f
    private var itemPosition = -1

    override fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
        this.qty = qty ?: 0
        this.itemPrice = itemPrice
        this.maxQty = maxQty
        this.minQty = minQty
        this.minOrder = minOrder
        this.itemPosition = itemPosition
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            val offset = if (qty == minQty) qty else 1
            val p = ((totalPrice * 100) + (offset * (itemPrice * 100))) / 100
            totalPrice = p
            calcPrice(minOrder, totalPrice)
//            favProductProgressButton = progressButton
            sharedFragmentViewModel.addToCart(pathEndPoint, shopId, modelType, modelId, qty)

        } else {
            findNavController().navigate(R.id.go_to_login)
        }

    }

    override fun removeItemFromCart(
        modelType: String?,
        modelId: Int?,
        productPrice: Float,
        itemPosition: Int,
    ) {
        this.itemPosition = itemPosition
        this.itemPrice = productPrice
        var p = ((totalPrice * 100) - (qty * (productPrice * 100))) / 100
        totalPrice = p
        if (SharedPreferencesManager.isLoggedIn() == "true") {

            calcPrice(minOrder, totalPrice)
            sharedFragmentViewModel.removeCartItem(modelType, modelId)

        } else {
            findNavController().navigate(R.id.go_to_login)
        }
    }

    private fun calcPrice(minOrder: Float, totalPrice: Float) {
        binding.includeQtyCartInfo.progressBar.max = 100
        binding.includeQtyCartInfo.progressBar.progress = if (minOrder > 0) {
            (((totalPrice * 100) / (minOrder * 100)) * 100).toInt()
        } else {
            0
        }
        //min order
        val numFormatMinOrder =
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(minOrder) + "&#8200;" +
                    context?.getString(com.techcubics.style.R.string.currency_name)
        val minOrderStr: String? =
            context?.getString(com.techcubics.style.R.string.min_order, numFormatMinOrder)
        val minOrderStyledText: Spanned? =
            minOrderStr?.let { Html.fromHtml(it, Html.FROM_HTML_MODE_LEGACY) }
        binding.includeQtyCartInfo.minOrder.text = minOrderStyledText
        //total amount
        val numFormatPrice = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH)
            .format(totalPrice) + "&#8200;" +
                context?.getString(com.techcubics.style.R.string.currency_name)
        val totalAmountStr =
            context?.getString(
                com.techcubics.style.R.string.product_order_total_amount,
                numFormatPrice
            )
        val totalAmountStyledText: Spanned = Html.fromHtml(
            totalAmountStr,
            Html.FROM_HTML_MODE_LEGACY
        )
        binding.includeQtyCartInfo.totalAmount.text = totalAmountStyledText

    }
}