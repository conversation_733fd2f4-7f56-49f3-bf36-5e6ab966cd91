package com.techcubics.data.model.requests.home

import com.google.gson.annotations.SerializedName

data class ProductSearchRequest(


    val country_id: Int,
    val key_search: String? = null,
    val colors: Array<String>? = null,
    val sizes: Array<String>? = null,
    val category_id: Array<Int>? = null,
    val price_start: Int? = null,
    val price_end: Int? = null,
    @SerializedName("shop_id")
    val shop_id:Int?=null,
    val page: Int,
    @SerializedName("governorate_id")
    var governorateId:Int?=null,
    @SerializedName("region_id")
    var regionId:Int?=null,
    @SerializedName("lat")
    var lat:String?=null,
    @SerializedName("lng")
    var lng:String?=null,
    var price : String? = null
)
