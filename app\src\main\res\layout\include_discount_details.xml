<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/discount_details_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingTop="24dp"
    android:paddingBottom="16dp">

    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
        android:id="@+id/dots_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:dotsColor="@color/color_gray_22"
        app:dotsCornerRadius="8dp"
        app:dotsSize="16dp"
        app:dotsSpacing="4dp"
        app:dotsWidthFactor="2.5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progressMode="false"
        app:selectedDotColor="@color/black" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="5dp"
        android:textAlignment="viewStart"
        android:theme="@style/product_details_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dots_indicator" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/branch_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/gallery_img"
            android:layout_width="@dimen/icons_size_height_weight_6"
            android:layout_height="@dimen/icons_size_height_weight_6"
            android:layout_gravity="center_vertical"
            android:src="@drawable/portrait_welcome_img"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/gallery_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:theme="@style/product_details_store_name"
            app:layout_constraintBottom_toBottomOf="@+id/gallery_img"
            app:layout_constraintStart_toEndOf="@id/gallery_img"
            app:layout_constraintTop_toTopOf="@id/gallery_img"
            tools:text="Branch name" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/products_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/branch_container" />

    <LinearLayout
        android:id="@+id/price_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/products_divider">

        <TextView
            android:id="@+id/discount_percentage"
            style="@style/discount_details_percentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            app:drawableStartCompat="@drawable/ic_red_label"
            tools:text="10%" />

        <TextView
            android:id="@+id/discount_price_after"
            style="@style/discount_details_price_after"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:textColor="@color/color_59"
            tools:text="40 EGP" />

        <TextView
            android:id="@+id/discount_price_before"
            style="@style/discount_details_price_before"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:background="@drawable/line_diagonal2"
            tools:text="44 EGP" />
    </LinearLayout>

    <!--    <TextView-->
    <!--        android:id="@+id/qty_title"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="20dp"-->
    <!--        android:text="@string/quantity"-->
    <!--        android:theme="@style/product_details_quantity_title"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/price_container" />-->

    <!--    <include-->
    <!--        android:id="@+id/product_qty_cart_container"-->
    <!--        layout="@layout/include_input_quantity"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginStart="10dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/qty_title"-->
    <!--        app:layout_constraintStart_toEndOf="@id/qty_title"-->
    <!--        app:layout_constraintTop_toTopOf="@id/qty_title" />-->

    <TextView
        android:id="@+id/discount_details_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/product_details"
        android:theme="@style/discount_details_description_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/price_container" />

    <TextView
        android:id="@+id/discount_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="10dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:theme="@style/product_details_description"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/discount_details_title" />

    <!--    <TextView-->
    <!--        android:id="@+id/rate_count"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginVertical="10dp"-->
    <!--        android:theme="@style/product_details_rating_count"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/discount_details" />-->

    <View
        android:id="@+id/cart_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/discount_details" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quantity_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_cart_quantity"
        android:padding="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cart_divider">

        <TextView
            android:id="@+id/total_price_after"
            style="@style/product_details_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="66 EGP" />

        <androidx.cardview.widget.CardView
            android:id="@+id/qty_cont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="6dp"
            android:layout_marginStart="16dp"
            app:layout_constraintBottom_toBottomOf="@id/total_price_after"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/total_price_after"
            app:layout_constraintTop_toTopOf="@id/total_price_after">

            <include
                android:id="@+id/product_qty_cart_container"
                layout="@layout/include_input_quantity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/min_order_note"
        style="@style/label_item_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/min_order_number"
        android:textColor="@color/color_red_3"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/quantity_cont" />

    <include
        android:id="@+id/btn_add_to_cart"
        layout="@layout/btn_progress"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="20dp"
        android:theme="@style/label_add_to_cart_button"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/min_order_note" />

</androidx.constraintlayout.widget.ConstraintLayout>
