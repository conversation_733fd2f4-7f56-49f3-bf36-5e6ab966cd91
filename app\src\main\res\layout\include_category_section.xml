<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:orientation="vertical"
        android:background="@color/white">
        <TextView
            android:id="@+id/header"
            style="@style/label_section_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="@string/available_categories" />

        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/line_divder_item"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />
        <LinearLayout
            android:visibility="visible"
            android:id="@+id/category_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp">
            <TextView
                android:id="@+id/tv"
                android:text="@string/select_product_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textAppearance="@style/label_filter_start_end_amount"
                />
            <ImageView
                android:id="@+id/open_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_back_btn"
                android:background="?attr/selectableItemBackgroundBorderless"                />
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/catRV"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:paddingStart="10dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            tools:listitem="@layout/item_category_selected_infilter"
            tools:itemCount="5"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>