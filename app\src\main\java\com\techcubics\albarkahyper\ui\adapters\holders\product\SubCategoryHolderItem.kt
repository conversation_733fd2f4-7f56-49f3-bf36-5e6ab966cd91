package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.pojo.SubCategory
import com.techcubics.albarkahyper.databinding.ItemBranchTypeBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemMainCategoryBinding
import com.techcubics.albarkahyper.databinding.ItemSliderBinding
import com.techcubics.albarkahyper.databinding.ItemSubCategoryBinding
import com.techcubics.shared.constants.Constants


class SubCategoryHolderItem (val binding: ItemSubCategoryBinding, val context: Context): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "SubCategoryHolderItem"
    private lateinit var intent:Intent

    fun bind(data: SubCategory){

         binding.tvTitle.text=data.name
        data.image.let {
            if(data.id==-1){
                binding.icon.setImageDrawable(context.getDrawable(com.techcubics.style.R.drawable.ic_all_window))
            }else {
                if (it.isNullOrBlank() == false) {

                    Helper.loadImage(context, it!!, binding.icon)
                }
            }
        }

    }

}