<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.views.stores.details.FindInStoreFragment">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/linearLayoutCompat2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include

            android:id="@+id/toolbarSearchByStore"
            layout="@layout/toolbar_fragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <include

            android:id="@+id/searchToolbar"
            layout="@layout/toolbar_product_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvProducts"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="2"
            tools:itemCount="40"
            tools:listitem="@layout/item_store_product" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/pagingLoadingImg"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="bottom"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            android:visibility="gone"
            app:lottie_rawRes="@raw/lottie_pager_loading" />

        <include
            android:id="@+id/include_qty_cart_info"
            layout="@layout/include_qty_cart_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent" />



    </androidx.appcompat.widget.LinearLayoutCompat>


    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />

    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_centerInParent="true"/>

</RelativeLayout>