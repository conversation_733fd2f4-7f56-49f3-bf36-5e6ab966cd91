<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/stepper_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:text="@string/create_account"
            android:textAppearance="@style/header_auth"
            android:textSize="20dp" />

        <com.aceinteract.android.stepper.StepperNavigationView
            android:id="@+id/stepper"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:stepperIconSize="35dp"
            app:stepperItems="@menu/register_stepps_menu"
            app:stepperTextAppearance="@style/label_auth"
            app:stepperTextSize="12.5sp"
            app:stepperType="tab_numbered"
            app:stepperWidgetColor="@color/app_color" />
    </LinearLayout>


    <fragment

        android:id="@+id/frame_stepper"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/btns"
        app:layout_constraintTop_toBottomOf="@id/stepper_layout"
        app:navGraph="@navigation/register_stepps_nav_graph" />


    <LinearLayout
        android:id="@+id/btns"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintBottom_toBottomOf="parent">

        <include
            android:id="@+id/previous_progress"
            layout="@layout/btn_progress"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:layout_weight="1" />

        <include
            android:id="@+id/next_progress"
            layout="@layout/btn_progress"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:layout_weight="1" />
    </LinearLayout>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>


