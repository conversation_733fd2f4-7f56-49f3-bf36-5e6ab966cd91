<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/product_img"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvDiscountTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_round_offer"
        style="@style/label_item_discount"
        android:text="@string/discount"
        android:textSize="24sp"
        android:elevation="16dp"
        android:layout_margin="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingStart="29dp"
        android:paddingEnd="29dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"/>
        <com.github.ivbaranov.mfb.MaterialFavoriteButton
            android:id="@+id/is_fav"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/ic_triangle_round_img"
            app:layout_constraintEnd_toEndOf="parent"
            app:mfb_favorite_image="@drawable/ic_remove_favorite"
            app:layout_constraintBottom_toBottomOf="@id/btnShare"
            app:layout_constraintTop_toTopOf="@id/btnShare"
            app:mfb_not_favorite_image="@drawable/ic_add_favorite"
            app:mfb_type="heart"
            tools:ignore="RelativeOverlap" />

        <ImageButton
            android:id="@+id/btnShare"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:padding="15dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="50dp"
            android:background="@drawable/bg_ripple"
            android:clickable="true"
            android:src="@drawable/ic_share_outline"
            app:layout_constraintEnd_toStartOf="@+id/is_fav"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black" />

        <ImageView
            android:id="@+id/navigate_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="20dp"
            android:background="@drawable/bg_ripple"
            android:clickable="true"
            android:contentDescription="@string/content_desc_back_button"
            android:padding="15dp"
            android:src="@drawable/ic_back_left"
            app:layout_constraintTop_toTopOf="@id/btnShare"
            app:layout_constraintBottom_toBottomOf="@id/btnShare"
            app:layout_constraintStart_toStartOf="parent"
            app:tint="@color/black"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


