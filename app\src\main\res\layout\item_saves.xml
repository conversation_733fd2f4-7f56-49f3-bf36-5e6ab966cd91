<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="20dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:background="?attr/selectableItemBackground"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        >

        <ImageView
            android:id="@+id/imgThumb"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@null"
            android:scaleType="centerCrop"
            tools:srcCompat="@tools:sample/avatars"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <include
            android:id="@+id/includeTimerSection"
            layout="@layout/include_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/imgThumb"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imgThumb"
            app:layout_constraintVertical_bias="0.86" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgThumb">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/label_item_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:maxLines="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Title" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOfferProducts"
                android:layout_width="0dp"
                android:layout_height="90dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle"
                tools:itemCount="3"
                tools:listitem="@layout/item_offer_product" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/linearLayoutCompat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rvOfferProducts">

                <TextView
                    style="@style/label_item_price_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/offer_price" />

                <TextView
                    android:id="@+id/tvCurrentPrice"

                    style="@style/label_item_price_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textStyle="bold"
                    android:textColor="@color/app_color"
                    tools:text="41.99 جنيه" />


            </androidx.appcompat.widget.LinearLayoutCompat>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/branch_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearLayoutCompat">

                <com.mikhaellopez.circularimageview.CircularImageView
                    android:id="@+id/logo"
                    android:layout_width="@dimen/icons_size_height_weight_6"
                    android:layout_height="@dimen/icons_size_height_weight_6"
                    android:scaleType="centerInside"
                    app:civ_border_color="@color/color_gray_1"
                    app:civ_border_width="1dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvBranchName"
                    style="@style/label_item_store_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="4dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    app:layout_constraintBottom_toBottomOf="@+id/logo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/logo"
                    app:layout_constraintTop_toTopOf="@+id/logo"
                    app:layout_constraintVertical_bias="0.49"
                    tools:text="branch name" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <include
                android:id="@+id/btnOrder"
                layout="@layout/btn_progress_circle"
                android:layout_width="wrap_content"
                android:layout_height="45dp"
                android:layout_marginTop="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/branch_container" />

        </androidx.constraintlayout.widget.ConstraintLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>






</androidx.cardview.widget.CardView>