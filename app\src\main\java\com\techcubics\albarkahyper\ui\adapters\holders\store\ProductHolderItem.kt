package com.techcubics.albarkahyper.ui.adapters.holders.store

import android.content.Context
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.ProductSearchData
import com.techcubics.data.model.pojo.StoreDetailsMenuProductData
import com.techcubics.data.model.pojo.StoreDetailsMostWantedData
import com.techcubics.albarkahyper.databinding.ItemStoreProductBinding
import com.techcubics.data.model.pojo.ProductDetailsDto
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import org.koin.android.ext.android.get

class ProductHolderItem<T>(
    val binding: ItemStoreProductBinding,
    val context: Context,
    val margin: Int = 0,
    val onFavClickListener: IFavClickListener,
    val onClickHandler: IOnAdapterItemClickHandler
) : RecyclerView.ViewHolder(binding.root) {


    private val TAG = "ProductHolderItem"

    //    lateinit var progressButton: CircleProgressButton
    private val SharedPreferencesManager: SharedPreferencesManager by lazy {
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    private lateinit var quantityButtonsController: QuantityButtonsController
    private lateinit var modelType: String
    private var productId = -1
    private var productPrice = 0f
    private var qtyCart = 0
    private var maxQty = 0
    private var minQty = 0
    private var minOrder = 0f
    private var endpoint = EndPointConstants.add_product
    fun bind(data: T, minOrder: Float) {
        this.minOrder = minOrder
        binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
//        progressButton = CircleProgressButton(context)
//        progressButton.initRoundButton(binding.btnOrder)
        when (data) {


            is StoreDetailsMostWantedData -> {
                fillByStore(data)
            }
            is StoreDetailsMenuProductData -> {
                fillByStore(data)
            }
            is ProductSearchData -> {
                fillByStore(data)
            }
            is ProductDetailsDto -> {
                fillByCategory(data)
            }
        }
        quantityButtonsController = QuantityButtonsController(
            binding.quantityController.root,
            null,
            context,
            maxQty = maxQty,
            minQty = minQty
        )
        quantityButtonsController.setIsItem(
            updateQuantity = ::updateQuantity,
            removeCartItem = ::removeCartItem
        )
        quantityButtonsController.updateQuantity(qtyCart)
        quantityButtonsController.isBtnDelete()
        quantityButtonsController.isBtnIncreaseEnabled()

        if (qtyCart > 0) {
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
        } else {
            binding.btnOrder.root.visibility = View.VISIBLE
            binding.quantityCont.visibility = View.INVISIBLE
        }
//        val minNumStr=context.getString(com.techcubics.style.R.string.max_num1, maxQty.toString())
//        val numFormatPrice = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(minOrder) +"&#8200;"+
//                context.getString(com.techcubics.style.R.string.currency_name)
//        val minOrderStr =  context.getString(com.techcubics.style.R.string.min_order1, numFormatPrice)
//        val str =  "$minNumStr$minOrderStr"
//        val styledText: Spanned = Html.fromHtml(str, Html.FROM_HTML_MODE_LEGACY)
//        binding.tvDescription.text = styledText
        binding.maxQty.text = maxQty.toString()
        binding.minQty.text = minQty.toString()
    }
    private fun setEndPoint(discount: Boolean?) {
        endpoint = if (discount == true) {
            EndPointConstants.add_discount
        } else {
            EndPointConstants.add_product
        }
    }

    private fun fillByStore(data: StoreDetailsMostWantedData) {
        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productID
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter!! else data.price
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart?:0
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        setEndPoint(data.isDiscount)

        binding.tvTitle.text = data.name
//        binding.tvRating.text = data.rate_count.toString()
//        binding.rateBar.rating = data.rate
        binding.tvDescription.text = data.description

        if (data.Images.size > 0) {
            Helper.loadImage(context, data.Images[0].path, binding.imgThumb)
        }
        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }
        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
            }
            else -> {}
        }
        binding.btnAddFav.setOnClickListener {
            when (data.isFav ?: false) {
                true -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
                false -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
        }
//        when (data.isFav) {
//            true -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
//            }
//            false -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
//            }
//        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productID, RateTypesEnum.Product.value)
        }

        binding.btnOrder.constraintsLayout.setOnClickListener {
            Log.i("here", "store 1")
//            if(SharedPreferencesManager.isLoggedIn() == "true"){
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                -1,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                0f,
                bindingAdapterPosition
            )
        }

        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }

    }

    private fun fillByStore(data: StoreDetailsMenuProductData) {
        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productID
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter!! else data.price
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart?:0
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        setEndPoint(data.isDiscount)

        binding.tvTitle.text = data.name
//        binding.tvRating.text = data.rate_count.toString()
//        binding.rateBar.rating = data.rate
        binding.tvDescription.text = data.description


        if (data.images.size > 0) {
            Helper.loadImage(context, data.images[0].path, binding.imgThumb)
        }

        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }

        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
            }
            else -> {}
        }
        binding.btnAddFav.setOnClickListener {
            when (data.isFav ?: false) {
                true -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
                false -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
        }
//        when (data.isFav) {
//            true -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
//            }
//            false -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
//            }
//        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productID, RateTypesEnum.Product.value)
        }

        binding.btnOrder.constraintsLayout.setOnClickListener {
            Log.i("here", "store 2")
//            if(SharedPreferencesManager.isLoggedIn() == "true"){
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                -1,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                0f,
                bindingAdapterPosition
            )
        }

        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }
    }

    private fun fillByStore(data: ProductSearchData) {
        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productID ?: -1
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter!! else data.price!!
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart?:0
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        setEndPoint(data.isDiscount)

        binding.tvTitle.text = data.name
//        binding.tvRating.text = data.rate_count.toString()
//        binding.rateBar.rating = data.rate
        binding.tvDescription.text = data.description

        if (data.images!!.size > 0) {
            Helper.loadImage(context, data.images?.get(0)!!.path, binding.imgThumb)
        }
        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }
        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
            }
            else -> {}
        }
        binding.btnAddFav.setOnClickListener {
            when (data.isFav ?: false) {
                true -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
                false -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
        }
//        when (data.isFav) {
//            true -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
//            }
//            false -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
//            }
//        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productID, RateTypesEnum.Product.value)
        }

        binding.btnOrder.constraintsLayout.setOnClickListener {
            Log.i("here", "store 1")
//            if(SharedPreferencesManager.isLoggedIn() == "true"){
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                -1,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                0f,
                bindingAdapterPosition
            )
        }

        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }

    }

    private fun fillByCategory(data: ProductDetailsDto) {

        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productId ?: -1
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter!! else data.price!!
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart?:0
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        setEndPoint(data.isDiscount)

        binding.tvTitle.text = data.name
//        binding.tvRating.text = data.rate_count.toString()
//        binding.rateBar.rating = data.rate
        binding.tvDescription.text = data.description
        if (data.images!!.size > 0) {
            Helper.loadImage(context, data.images?.get(0)!!.path, binding.imgThumb)
        }
        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }
        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
            }
            else -> {}
        }
        binding.btnAddFav.setOnClickListener {
            when (data.isFav ?: false) {
                true -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
                false -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
        }
//        when (data.isFav) {
//            true -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
//            }
//            false -> {
//                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
//            }
//        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productId, RateTypesEnum.Product.value)
        }

        binding.btnOrder.constraintsLayout.setOnClickListener {
            Log.i("here", "store 1")
//            if(SharedPreferencesManager.isLoggedIn() == "true"){
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                -1,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                0f,
                bindingAdapterPosition
            )
        }


        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }

    }

    private fun updateQuantity(
        qty: Int,
    ) {
        onClickHandler.addToCart(
            endpoint,
            -1,
            modelType,
            productId,
            qty,
            productPrice,
            maxQty,
            minQty,
            0f,
            bindingAdapterPosition
        )
    }

    private fun removeCartItem() {
        binding.btnOrder.root.visibility = View.VISIBLE
        binding.quantityCont.visibility = View.INVISIBLE
        onClickHandler.removeItemFromCart(
            modelType,
            productId,
            productPrice,
            bindingAdapterPosition
        )
    }
}