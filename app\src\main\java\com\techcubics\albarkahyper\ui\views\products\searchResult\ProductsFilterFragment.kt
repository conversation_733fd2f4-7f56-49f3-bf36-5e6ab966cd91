package com.techcubics.albarkahyper.ui.views.products.searchResult


import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.lottie.LottieAnimationView
import com.google.android.material.slider.RangeSlider
import com.techcubics.data.model.pojo.*
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentProductsFilterBinding
import com.techcubics.albarkahyper.ui.adapters.home.ColorsAdapter
import com.techcubics.albarkahyper.ui.adapters.home.SizesAdapter
import com.techcubics.data.model.requests.SearchAreaRequest
import com.techcubics.albarkahyper.ui.adapters.product.CategorySelectedInFilterAdapter
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.GenderEnum
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.shared.enums.PriceEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.*

class ProductsFilterFragment : Fragment(),
    IPageDetails<ProductColorsSizesData>, IColorsClickListener {

    private lateinit var binding: FragmentProductsFilterBinding

    //    private lateinit var categoryAdapter: FilterCategoryAdapter
    private lateinit var selectedCategoryAdapter: CategorySelectedInFilterAdapter
    private lateinit var selectedColorAdapter: ColorsAdapter
    private lateinit var selectedSizesAdapter: SizesAdapter
    private val mainViewModel: MainViewModel by activityViewModels()
    private var from: Int = 1
    private var to: Int = 300
    private lateinit var rangeSlider: RangeSlider
    private lateinit var progressButton: ProgressButton
    private var selectedCategories: ArrayList<Int> = arrayListOf()
    private var distinctSelectedCategories: ArrayList<Int> = arrayListOf()
    private var selectedColors: ArrayList<ColorObject> = arrayListOf()
    private var selectedSizes: ArrayList<Size> = arrayListOf()
    private var filteredColors: ArrayList<String> = arrayListOf()
    private var filteredSizes: ArrayList<String> = arrayListOf()
    private var distinctFilteredColors: ArrayList<String> = arrayListOf()
    private var distinctFilteredSizes: ArrayList<String> = arrayListOf()
    private lateinit var filter: IFilterClickListener

    private val TAG = "Filter_Fragment"

    private var searchArea: String =""
    private var searchAreaRequest: SearchAreaRequest?=null
    private var latlng: Latlng?=null
    companion object {
        var instance: ProductsFilterFragment? = null
        fun getInstance(f: IFilterClickListener): ProductsFilterFragment? {
            instance = ProductsFilterFragment()
            return instance

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentProductsFilterBinding.inflate(inflater, container, false)
        Log.i("conflictContext", "FragmentProductsFilter")
        searchArea = getString(com.techcubics.style.R.string.all_cities)
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.btnFilter)
        init()
        observers()
        events()
        return binding.root
    }

    @SuppressLint("SetTextI18n")
    override fun init() {
        binding.includeColorSection.header.text = getString(com.techcubics.style.R.string.color)
        binding.includeSizeSection.header.text =
            getString(com.techcubics.style.R.string.available_sizes)
        binding.includeColorSection.tv.text = getString(com.techcubics.style.R.string.select_color)
        binding.includeSizeSection.tv.text = getString(com.techcubics.style.R.string.select_size)
        binding.btnFilter.textView.text = getString(com.techcubics.style.R.string.accept)
        binding.includePlacesSearchSection.tv.text = searchArea
        rangeSlider = binding.includeFilterPriceSection.rangeSlider
        binding.includeFilterPriceSection.tvStart.text = "${
            String.format(
                Locale.US,
                "%,d",
                1
            )
        } ${getString(com.techcubics.style.R.string.currency_name)}"
        binding.includeFilterPriceSection.tvEnd.text = "${
            String.format(
                Locale.US,
                "%,d",
                300
            )
        } ${getString(com.techcubics.style.R.string.currency_name)}"

        binding.toolbarFilter.tvTitle.text = resources.getText(com.techcubics.style.R.string.filter)

    }

    override fun observers() {

        mainViewModel.selectedCategories.observe(viewLifecycleOwner) {
            if (it != null) {
                showCategoryRV(it)
                for (cat in it) {
                    selectedCategories.add(cat.id!!)

                }
//                mainViewModel.selectedCategories.value = null
            }

        }

        mainViewModel.selectedColor.observe(viewLifecycleOwner) {
            if (it != null) {
                showColorRV(it)
                for (color in it) {
                    selectedColors.add(color)
                }
                //                mainViewModel.selectedColor.value = null
            }

        }

        mainViewModel.selectedSize.observe(viewLifecycleOwner) {
            if (it != null) {
                showSizeRV(it)
                for (size in it) {
                    selectedSizes.add(size)
                }
//                mainViewModel.selectedSize.value = null
            }

        }

        mainViewModel.refrence.observe(viewLifecycleOwner) {
            if (it != null) {
                filter = it
                mainViewModel.refrence.value = null
            }

        }


        mainViewModel.latLngLiveData.observe(viewLifecycleOwner) { latLng ->
            if (latLng != null) {
                this.latlng = latLng
                searchAreaRequest = null
                searchArea = getString(com.techcubics.style.R.string.detect_location)
                binding.includePlacesSearchSection.tv.text = searchArea
                mainViewModel.removeLatLngObserver()
            }
        }
        mainViewModel.allCitiesLiveData.observe(viewLifecycleOwner) { allCities ->
            if (allCities != null) {
                searchAreaRequest = null
                latlng = null
                searchArea = allCities
                binding.includePlacesSearchSection.tv.text = searchArea

                mainViewModel.removeAllCitiesObserver()
            }
        }
        mainViewModel.govRegionLiveData.observe(viewLifecycleOwner) { searchAreaRequest ->
            if (searchAreaRequest != null) {
                latlng = null
                this.searchAreaRequest = searchAreaRequest
                searchArea = searchAreaRequest.areaName
                binding.includePlacesSearchSection.tv.text = searchArea
                mainViewModel.removeGovRegionObserver()
            }
        }
    }

    private fun showCategoryRV(arrayList: ArrayList<Category>) {
        if (arrayList.isNullOrEmpty()) {
            binding.includeCategorySection.categoryLayout.visibility = View.VISIBLE
            binding.includeCategorySection.catRV.visibility = View.GONE
        } else {
            val localArray = arrayListOf<String>()
            for (cat in arrayList) {
                cat.name?.let { localArray.add(it) }
            }
            binding.includeCategorySection.categoryLayout.visibility = View.GONE
            binding.includeCategorySection.catRV.visibility = View.VISIBLE
            val linearLayoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.HORIZONTAL, false
            )
            binding.includeCategorySection.catRV.layoutManager = linearLayoutManager
            selectedCategoryAdapter = CategorySelectedInFilterAdapter(requireContext(), localArray)
            binding.includeCategorySection.catRV.adapter = selectedCategoryAdapter
        }


    }

    private fun showColorRV(arrayList: ArrayList<ColorObject>) {
        if (arrayList.isNullOrEmpty()) {
            binding.includeColorSection.categoryLayout.visibility = View.VISIBLE
            binding.includeColorSection.colorRV.visibility = View.GONE
        } else {
            binding.includeColorSection.categoryLayout.visibility = View.GONE
            binding.includeColorSection.colorRV.visibility = View.VISIBLE
            val linearLayoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.HORIZONTAL, false
            )
            binding.includeColorSection.colorRV.layoutManager = linearLayoutManager
            selectedColorAdapter = ColorsAdapter(null)
            selectedColorAdapter.setItemsList(arrayList)
            binding.includeColorSection.colorRV.adapter = selectedColorAdapter
        }
    }

    private fun showSizeRV(arrayList: ArrayList<Size>) {
        if (arrayList.isNullOrEmpty()) {
            binding.includeSizeSection.categoryLayout.visibility = View.VISIBLE
            binding.includeSizeSection.sizeRV.visibility = View.GONE
        } else {
            val localArray = arrayListOf<String>()
            for (cat in arrayList) {
                localArray.add(cat.name)
            }
            binding.includeSizeSection.categoryLayout.visibility = View.GONE
            binding.includeSizeSection.sizeRV.visibility = View.VISIBLE
            val linearLayoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.HORIZONTAL, false
            )
            binding.includeSizeSection.sizeRV.layoutManager = linearLayoutManager
            selectedCategoryAdapter = CategorySelectedInFilterAdapter(requireContext(), localArray)
            binding.includeSizeSection.sizeRV.adapter = selectedCategoryAdapter
        }


    }

    @SuppressLint("SetTextI18n")
    override fun events() {

        binding.toolbarFilter.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }

        rangeSlider.addOnChangeListener { rangeSlider, value, fromUser ->
            // Responds to when slider's value is changed
            Log.d(TAG, "events: 1")
            from = rangeSlider.values[0].toInt()
            to = rangeSlider.values[1].toInt()
            binding.includeFilterPriceSection.tvStart.text = "${
                String.format(
                    Locale.US,
                    "%,d",
                    from
                )
            } ${getString(com.techcubics.style.R.string.currency_name)}"
            binding.includeFilterPriceSection.tvEnd.text = "${
                String.format(
                    Locale.US,
                    "%,d",
                    to
                )
            } ${getString(com.techcubics.style.R.string.currency_name)}"
        }

        binding.includeCategorySection.catRV.setOnClickListener {
            findNavController().navigate(R.id.productsFilterFragment_selectCategory)
        }

        binding.includeCategorySection.root.setOnClickListener {

            findNavController().navigate(R.id.productsFilterFragment_selectCategory)
        }

//        binding.includeColorSection.root.setOnClickListener {
//            findNavController().navigate(R.id.productsFilterFragment_selectColor)
//        }
//
//        binding.includeColorSection.colorRV.setOnClickListener {
//            findNavController().navigate(R.id.productsFilterFragment_selectColor)
//        }

//        binding.includeSizeSection.root.setOnClickListener {
//            findNavController().navigate(R.id.productsFilterFragment_selectSize)
//        }
//
//        binding.includeSizeSection.sizeRV.setOnClickListener {
//            findNavController().navigate(R.id.productsFilterFragment_selectSize)
//        }

        binding.btnFilter.constraintsLayout.setOnClickListener {
            distinctSelectedCategories =
                selectedCategories.toSet().toMutableList() as ArrayList<Int>
            for (item in selectedColors)
                filteredColors.add(item.code)
            distinctFilteredColors = filteredColors.toSet().toMutableList() as ArrayList<String>
            for (item in selectedSizes)
                filteredSizes.add(item.name)
            distinctFilteredSizes = filteredSizes.toSet().toMutableList() as ArrayList<String>

            mainViewModel.selectedCategories.value = null
            mainViewModel.selectedColor.value = null
            mainViewModel.selectedSize.value = null
            val price = initPrice()

            filter.onFilterClick(
                from,
                to,
                distinctFilteredColors,
                distinctFilteredSizes,
                distinctSelectedCategories,
                latlng,
                searchAreaRequest,
                price
            )
            CoroutineScope(Dispatchers.Main).launch {
                progressButton.btnActivated()
                delay(1000)
                progressButton.btnFinishedSuccessfully(
                    getString(com.techcubics.style.R.string.accept),
                    null
                )
                delay(300)
                findNavController().popBackStack()
            }

        }

        binding.btnClear.setOnClickListener {
            binding.includeFilterPriceSection.fromLessMore.isChecked = false
            binding.includeFilterPriceSection.fromMoreLess.isChecked = false

            rangeSlider.setValues(1F, 300F)
            //cat
            binding.includeCategorySection.catRV.adapter =
                CategorySelectedInFilterAdapter(requireContext(), arrayListOf())
//            selectedCategoryAdapter.notifyDataSetChanged()
            binding.includeCategorySection.categoryLayout.visibility = View.VISIBLE
            binding.includeCategorySection.catRV.visibility = View.GONE
            //color
            selectedColorAdapter = ColorsAdapter(null)
            selectedColorAdapter.setItemsList(arrayListOf())
            // selectedColorAdapter.notifyDataSetChanged()
            binding.includeColorSection.categoryLayout.visibility = View.VISIBLE
            binding.includeColorSection.colorRV.visibility = View.GONE
            //size
            selectedSizesAdapter = SizesAdapter(null)
            selectedSizesAdapter.setItemsList(arrayListOf())
            //  selectedSizesAdapter.notifyDataSetChanged()
            binding.includeSizeSection.categoryLayout.visibility = View.VISIBLE
            binding.includeSizeSection.sizeRV.visibility = View.GONE

            mainViewModel.selectedCategories.value = null
            mainViewModel.selectedColor.value = null
            mainViewModel.selectedSize.value = null
        }

        binding.includePlacesSearchSection.openAreaSearch.setOnClickListener {
            findNavController().navigate(R.id.view_areaFilter)
        }
    }

    private fun initPrice(): String? {
        return if (binding.includeFilterPriceSection.fromLessMore.isChecked) {
            PriceEnum.ASC.value
        } else if (binding.includeFilterPriceSection.fromMoreLess.isChecked)
            PriceEnum.DESC.value
        else
            null
    }
    override fun showData(item: ProductColorsSizesData) {
        Log.d(TAG, "showData: ${item.sizes!!.size}")
    }


    override fun showHidePlaceHolder(
        show: Boolean,
        type: LottieIconEnum?,
        message: String?,
        container: View?
    ) {

        if (show) {
            container?.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    container?.findViewById<LottieAnimationView>(R.id.icon)
                        ?.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    container?.findViewById<TextView>(R.id.tvMessage)?.text = message
                }
                LottieIconEnum.Error -> {
                    container?.findViewById<LottieAnimationView>(R.id.icon)
                        ?.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    if (message == Constants.SERVER_ERROR) {
                        container?.findViewById<TextView>(R.id.tvMessage)?.text =
                            getString(com.techcubics.style.R.string.server_error)

                    } else {
                        container?.findViewById<TextView>(R.id.tvMessage)?.text = message

                    }
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            container?.visibility = View.GONE
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    override fun onAddColorClick(color: ColorObject) {
        TODO("Not yet implemented")
    }

    override fun onRemoveColor(color: ColorObject) {
        TODO("Not yet implemented")
    }


}