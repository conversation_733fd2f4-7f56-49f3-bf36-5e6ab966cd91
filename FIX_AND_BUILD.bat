@echo off
title AlBarkaHyper APK Builder - Fixed Version
color 0A

echo.
echo ==========================================
echo    AlBarkaHyper APK Builder - FIXED
echo ==========================================
echo.

REM Set Java path (try common locations)
set "JAVA_HOME=C:\Program Files\Java\jdk-17"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\OpenJDK\jdk-17"

REM Set Android SDK path (try common locations)
set "ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk"
if not exist "%ANDROID_HOME%" set "ANDROID_HOME=C:\Android\Sdk"
if not exist "%ANDROID_HOME%" set "ANDROID_HOME=C:\Users\<USER>\Android\Sdk"

REM Update PATH
set "PATH=%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%"

echo Current directory: %CD%
echo Java Home: %JAVA_HOME%
echo Android Home: %ANDROID_HOME%
echo.

REM Check if we're in the right directory
if not exist "gradlew.bat" (
    echo ERROR: gradlew.bat not found!
    echo Please make sure you're running this from the project root directory.
    pause
    exit /b 1
)

echo ✓ Project files found
echo.

echo ==========================================
echo    Fixing Dependencies Issues
echo ==========================================
echo.

echo Step 1: Cleaning Gradle cache...
call gradlew.bat clean --no-daemon
if %ERRORLEVEL% neq 0 (
    echo Warning: Clean had issues, continuing...
)

echo.
echo Step 2: Refreshing dependencies...
call gradlew.bat --refresh-dependencies --no-daemon
if %ERRORLEVEL% neq 0 (
    echo Warning: Refresh dependencies had issues, continuing...
)

echo.
echo Step 3: Stopping Gradle daemon...
call gradlew.bat --stop

echo.
echo ==========================================
echo    Building APK (Attempt 1)
echo ==========================================
echo.

echo Building APK with offline mode disabled...
call gradlew.bat assembleRelease --no-daemon --stacktrace --refresh-dependencies
if %ERRORLEVEL% equ 0 (
    echo ✓ APK build completed successfully!
    goto :check_files
)

echo.
echo ==========================================
echo    Building APK (Attempt 2 - Debug Mode)
echo ==========================================
echo.

echo APK Release build failed, trying Debug build...
call gradlew.bat assembleDebug --no-daemon --stacktrace
if %ERRORLEVEL% equ 0 (
    echo ✓ Debug APK build completed successfully!
    echo Note: This is a DEBUG version, not suitable for Google Play Store
    echo You can use it for testing purposes.
    goto :check_debug_files
)

echo.
echo ==========================================
echo    All Build Attempts Failed
echo ==========================================
echo.
echo Both Release and Debug builds failed.
echo Please check the error messages above.
echo.
echo Common solutions:
echo 1. Make sure you have stable internet connection
echo 2. Try running Android Studio once to download missing SDKs
echo 3. Check if antivirus is blocking Gradle
echo 4. Try deleting .gradle folder and retry
echo.
pause
exit /b 1

:check_files
echo.
echo ==========================================
echo    Checking Release Build Results
echo ==========================================
echo.

if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ SUCCESS: Release APK created!
    echo   Location: %CD%\app\build\outputs\apk\release\app-release.apk
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo   Size: %%~zI bytes
    set "APK_CREATED=1"
) else (
    echo ✗ ERROR: Release APK was not created!
    set "APK_CREATED=0"
)

echo.
echo Attempting to build AAB (Android App Bundle)...
call gradlew.bat bundleRelease --no-daemon --stacktrace
if %ERRORLEVEL% equ 0 (
    if exist "app\build\outputs\bundle\release\app-release.aab" (
        echo ✓ SUCCESS: AAB created!
        echo   Location: %CD%\app\build\outputs\bundle\release\app-release.aab
        for %%I in ("app\build\outputs\bundle\release\app-release.aab") do echo   Size: %%~zI bytes
    )
) else (
    echo ✗ AAB build failed, but APK should work for Google Play Store
)

goto :final_results

:check_debug_files
echo.
echo ==========================================
echo    Checking Debug Build Results
echo ==========================================
echo.

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✓ SUCCESS: Debug APK created!
    echo   Location: %CD%\app\build\outputs\apk\debug\app-debug.apk
    for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do echo   Size: %%~zI bytes
    echo.
    echo ⚠️  WARNING: This is a DEBUG version!
    echo    - Not suitable for Google Play Store
    echo    - Use only for testing
    echo    - You need to fix the Release build for store submission
    set "APK_CREATED=1"
) else (
    echo ✗ ERROR: Debug APK was not created!
    set "APK_CREATED=0"
)

:final_results
echo.
echo ==========================================
echo    Final Results
echo ==========================================
echo.

if "%APK_CREATED%"=="1" (
    echo ✅ SUCCESS: APK file has been created!
    echo.
    echo Next steps:
    echo 1. Test the APK on an Android device
    echo 2. If it's a debug APK, fix the release build issues
    echo 3. Check google-play-assets folder for store materials
    echo 4. Upload to Google Play Store (use Release APK/AAB only)
    echo.
    echo File locations:
    if exist "app\build\outputs\apk\release\app-release.apk" (
        echo Release APK: %CD%\app\build\outputs\apk\release\app-release.apk
    )
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo Debug APK: %CD%\app\build\outputs\apk\debug\app-debug.apk
    )
    if exist "app\build\outputs\bundle\release\app-release.aab" (
        echo Release AAB: %CD%\app\build\outputs\bundle\release\app-release.aab
    )
) else (
    echo ❌ FAILED: No APK files were created!
    echo.
    echo Troubleshooting steps:
    echo 1. Check internet connection
    echo 2. Run Android Studio and sync project
    echo 3. Install missing SDK components
    echo 4. Check antivirus settings
    echo 5. Try deleting .gradle folder and retry
)

echo.
pause
