<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.views.auth.fragments.register.FacilityTypeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="80dp">
        <EditText
            android:id="@+id/name_edt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="28dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/et_style"
            android:hint="@string/facility_name"
            android:inputType="textNoSuggestions"
            android:padding="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textAppearance="@style/label_auth_edittext" />
        <EditText
            android:id="@+id/facility_type_edt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="28dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/et_style"
            android:hint="@string/facility_type_"
            android:inputType="textNoSuggestions"
            android:padding="10dp"
            app:layout_constraintTop_toBottomOf="@id/name_edt"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textAppearance="@style/label_auth_edittext" />
    </LinearLayout>


<!--    <androidx.recyclerview.widget.RecyclerView-->
<!--        android:id="@+id/facilityTypeRV"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/name_edt"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        android:layout_marginTop="10dp"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="0dp"-->
<!--        tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"-->
<!--        tools:spanCount="2"-->
<!--        tools:listitem="@layout/item_facility_type"/>-->



    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />

</androidx.constraintlayout.widget.ConstraintLayout>