package com.techcubics.albarkahyper.ui.adapters.product

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.navigation.Navigation.findNavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Category
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.ItemCategorySelectedInfilterBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.CategorySelectedInfilterHolderItem


class CategorySelectedInFilterAdapter(
    private val context: Context,
    private val categorySelectedList : MutableList<String>
    ) : RecyclerView.Adapter<CategorySelectedInfilterHolderItem>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategorySelectedInfilterHolderItem {

        val itemBinding = ItemCategorySelectedInfilterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return CategorySelectedInfilterHolderItem(itemBinding)
    }


    override fun onBindViewHolder(holder: CategorySelectedInfilterHolderItem, position: Int) {
        holder.itemName.text = categorySelectedList.get(position)
        holder.itemView.setOnClickListener {
            findNavController(it).navigate(R.id.productsFilterFragment_selectCategory)
        }


    }

    override fun getItemCount() = categorySelectedList.size
}