package com.techcubics.albarkahyper.ui.views.stores.searchResult

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageButton
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.StoresNearbyByWordFurnitureData
import com.techcubics.albarkahyper.databinding.FragmentStoreSearchNearByResultBinding
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.common.IPagePagedRowset
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.ui.adapters.home.StoresAdapter
import com.techcubics.albarkahyper.ui.views.stores.StoresViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class StoreSearchNearByResultFragment : Fragment(),
    IPagePagedRowset<StoresNearbyByWordFurnitureData>, IFavClickListener {

    private lateinit var binding: FragmentStoreSearchNearByResultBinding
    private lateinit var storesAdapter: StoresAdapter<StoresNearbyByWordFurnitureData>
    private  val storesFragmentViewModel: StoresViewModel by viewModel<StoresViewModel>()
    private lateinit var resultList:ArrayList<StoresNearbyByWordFurnitureData>
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private var isLoading:Boolean=false
    private  var latitdue:String?=null
    private  var longitude:String?=null
    private lateinit var name:String
    private lateinit var  etSearch: EditText
    private  var isGeneral:Boolean=true
    private var word:String=""

    private var _operation:Int=-1
    private var _position: Int?=null

    private  val TAG = "StoreSearchResultFrag"




    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        binding= FragmentStoreSearchNearByResultBinding.inflate(inflater, container, false)



        init()
        observers()
        events()

        return binding.root
    }


    override fun init() {
       // SharedPreferencesManager.init(requireContext())
        arguments?.let {
            latitdue = it.getString(Constants.INTENT_LATITUDE)
            longitude = it.getString(Constants.INTENT_LATITUDE)
            name = getString(com.techcubics.style.R.string.section_nearby_title)
            resultList=ArrayList<StoresNearbyByWordFurnitureData>()

            binding.toolbarSearchByNearBy.tvTitle.text = name
            showHidePlaceHolder(show = false, type = null, message = null)
            binding.pagingLoadingImg.visibility = View.GONE
            binding.rvStores.visibility = View.GONE

            etSearch=  binding.searchToolbar.txtSearchWord
            etSearch.hint=getString(com.techcubics.style.R.string.hint_write_store_name)
            var x: ImageButton = binding.searchToolbar.btnFilter
            x.visibility=View.GONE


            if(!latitdue.equals(null) && !longitude.equals(null)){
                storesFragmentViewModel.getStoresNearBy(latitude=latitdue!!,longitude=longitude!!, page = 1)
            }else{
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = getString(com.techcubics.style.R.string.message_error_loading_location))
            }

            //


        }
    }
    override  fun observers(){

        storesFragmentViewModel.nearByStoresResponse.observe(viewLifecycleOwner) {


           Helper.loadingAnimationVisibility(View.GONE,binding.pageLoadingAnimation.root)

            try {
                if (it.status!!) {

                    if(it.data !=null){

                        if (!it.data!!.furnitures.isEmpty()) {
                            showData(it.data!!.furnitures)
                            binding.rvStores.visibility = View.VISIBLE

                        }else{

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }
                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Error,
                            message = it.message
                        )

                    }


                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }
        }

        storesFragmentViewModel.addRemoveFavoriteForStoreResponse.observe(viewLifecycleOwner,Observer{




            try {

                if(it.status!!){

                    when(_operation){
                        1->   storesFragmentViewModel.nearByStoresResponse.value?.data!!.furnitures[_position!!].isFav=false
                        2->   storesFragmentViewModel.nearByStoresResponse.value?.data!!.furnitures[_position!!].isFav=true
                    }

                    storesAdapter.notifyDataSetChanged()

                }else{

                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvStores.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            }catch (ex:Exception){


                binding.pagingLoadingImg.visibility = View.GONE
                binding.rvStores.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }

        })

    }
    override fun events(){

        binding.toolbarSearchByNearBy.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
        etSearch.setOnEditorActionListener { v, actionId, event ->
            when(actionId){
                EditorInfo.IME_ACTION_SEARCH -> {
                    word=v.text.toString().trim()
                    v.clearFocus()
                    v.hideKeyboard()
                    resultList.clear()
                    binding.rvStores.adapter=null
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvStores.visibility = View.GONE
                    Helper.loadingAnimationVisibility(View.VISIBLE,binding.pageLoadingAnimation.root)

                    showHidePlaceHolder(show = false, type = null, message = null)
                    isGeneral=false
                    if(!latitdue.equals(null) && !longitude.equals(null)){
                        storesFragmentViewModel.getStoresNearBy(latitude=latitdue!!,longitude=longitude!!,word=word,page=1)
                    }else{
                        showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = getString(com.techcubics.style.R.string.message_error_loading_location))
                    }

                    true
                }

                else -> false
            }
        }
    }
    fun View.hideKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)
    }

    override fun showData( items:List<StoresNearbyByWordFurnitureData>){

        if( storesFragmentViewModel.nearByStoresResponse.value?.pagingator?.currentPage==1) {
            resultList= items as ArrayList<StoresNearbyByWordFurnitureData>
            storesAdapter = StoresAdapter<StoresNearbyByWordFurnitureData>(this)
            storesAdapter.setItemsList(resultList)
            binding.rvStores.adapter = storesAdapter
            binding.rvStores.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            //
            var pageDelegate =
                PagingDelegate.Builder(storesAdapter).attachTo(binding.rvStores)
                    .listenWith(this).build()


        }else{

            items.forEach {
                resultList.add(it)
            }
            storesAdapter.notifyDataSetChanged()
            onDonePaging()
        }

        Log.d(TAG, "showDiscounts: ${resultList.size}")

    }
    override fun onPage(p0: Int) {


        if(!isLoading){
            Log.d(TAG, "onPage: ${p0}")
            if(storesFragmentViewModel.nearByStoresResponse.value?.pagingator?.hasMorePages!!) {
                isLoading=true
                binding.pagingLoadingImg.visibility=View.VISIBLE
                when(isGeneral){
                    true->storesFragmentViewModel.getStoresNearBy(latitude=latitdue!!,longitude=longitude!!,page=storesFragmentViewModel.nearByStoresResponse.value?.pagingator?.currentPage!!+1)
                    false->storesFragmentViewModel.getStoresNearBy(latitude=latitdue!!,longitude=longitude!!,page=storesFragmentViewModel.nearByStoresResponse.value?.pagingator?.currentPage!!+1,word=word)
                }

            }
        }
    }
    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility=View.GONE
        isLoading=false
    }
    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                } else -> throw IllegalStateException("error")
            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }

    override fun onFavClick(parent:Int,position: Int, operation: Int) {

        _operation=operation
        _position=position
        storesFragmentViewModel.addRemoveFavStore(storesFragmentViewModel.nearByStoresResponse.value?.data!!.furnitures!![position].id)
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}