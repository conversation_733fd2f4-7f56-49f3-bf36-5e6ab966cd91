<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    tools:context=".ui.views.stores.searchResult.ProductAreasFilterFragment">

    <include
        android:id="@+id/toolbar_fragment"
        layout="@layout/toolbar_fragment"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
<LinearLayout
    android:id="@+id/search_cont"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="16dp"
    app:layout_constraintTop_toBottomOf="@+id/toolbar_fragment"
    android:background="@color/white">
    <include
        android:id="@+id/search_bar"
        layout="@layout/toolbar_product_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp" />
</LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_cont"
        tools:listitem="@layout/item_area" />

</androidx.constraintlayout.widget.ConstraintLayout>