package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class SelectedLocationData(
    @SerializedName("delivery_price" ) var deliveryPrice : Float? = null,
    @SerializedName("total_price"    ) var totalPrice    : Float? = null,
    @SerializedName("coupon_price"   ) var couponPrice   : Float? = null,
    @SerializedName("amount"         ) var amount        : Float? = null
)
