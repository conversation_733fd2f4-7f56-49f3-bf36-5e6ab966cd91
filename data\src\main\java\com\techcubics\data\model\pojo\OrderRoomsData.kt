package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class OrderRoomsData(

    @SerializedName("conversation_id")
    val conversation_id:Int,
    @SerializedName("sender_id")
    val sender_id:Int,
    @SerializedName("receiver_id")
    val receiver_id:Int,
    @SerializedName("order_id")
    val order_id:Int,
    @SerializedName("order_code")
    val order_code:String,
    @SerializedName("shop")
    val furniture:OrderRoomsFurnitureData,
    @SerializedName("customer")
    val customer:OrderRoomsCustomerData,
    @SerializedName("last_time_message")
    val last_time_message:String,
    @SerializedName("last_time")
    val last_time:String,
    @SerializedName("last_message")
    val last_message:String,
    @SerializedName("count_unread")
    val count_unread:Int,
    @SerializedName("date_format") var lastMessageTime : String? = null

)