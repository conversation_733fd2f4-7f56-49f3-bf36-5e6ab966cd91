@echo off
echo ========================================
echo   System Requirements Check
echo ========================================
echo.

echo Checking current directory...
echo Current directory: %CD%
echo.

echo Checking project files...
if exist "gradlew.bat" (
    echo ✓ gradlew.bat found
) else (
    echo ✗ gradlew.bat NOT found!
)

if exist "build.gradle" (
    echo ✓ build.gradle found
) else (
    echo ✗ build.gradle NOT found!
)

if exist "app\build.gradle" (
    echo ✓ app\build.gradle found
) else (
    echo ✗ app\build.gradle NOT found!
)

if exist "app\signature\albarkahypersecretkeystore.jks" (
    echo ✓ Keystore file found
) else (
    echo ✗ Keystore file NOT found!
)
echo.

echo Checking Java...
java -version 2>nul
if %ERRORLEVEL% neq 0 (
    echo ✗ Java not found in PATH
    echo.
    echo Please install Java JDK 17 or higher:
    echo 1. Download from: https://adoptium.net/
    echo 2. Install it
    echo 3. Add to PATH or set JAVA_HOME
    echo.
    echo Common Java locations:
    echo - C:\Program Files\Java\jdk-17
    echo - C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot
    echo - C:\Program Files\OpenJDK\jdk-17
) else (
    echo ✓ Java found
    java -version
)
echo.

echo Checking Android SDK...
if defined ANDROID_HOME (
    echo ✓ ANDROID_HOME is set: %ANDROID_HOME%
    if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
        echo ✓ Android SDK tools found
    ) else (
        echo ✗ Android SDK tools NOT found in ANDROID_HOME
    )
) else (
    echo ✗ ANDROID_HOME not set
    echo.
    echo Please set ANDROID_HOME to your Android SDK location:
    echo Common locations:
    echo - C:\Users\<USER>\AppData\Local\Android\Sdk
    echo - C:\Android\Sdk
    echo.
    echo To set ANDROID_HOME:
    echo 1. Right-click "This PC" → Properties
    echo 2. Advanced system settings
    echo 3. Environment Variables
    echo 4. Add ANDROID_HOME variable
)
echo.

echo Checking Gradle Wrapper permissions...
if exist "gradlew.bat" (
    echo ✓ Gradle wrapper exists
    echo Trying to run gradle wrapper...
    call gradlew.bat --version 2>nul
    if %ERRORLEVEL% neq 0 (
        echo ✗ Gradle wrapper failed to run
        echo This might be due to missing Java or Android SDK
    ) else (
        echo ✓ Gradle wrapper works
    )
) else (
    echo ✗ Gradle wrapper not found
)
echo.

echo ========================================
echo   Summary
echo ========================================
echo.
echo If all items above show ✓, you can run: simple_build.bat
echo If any item shows ✗, please fix it first.
echo.
echo For help with setup:
echo 1. Install Java JDK 17: https://adoptium.net/
echo 2. Install Android Studio: https://developer.android.com/studio
echo 3. Set environment variables (JAVA_HOME, ANDROID_HOME)
echo.
pause
