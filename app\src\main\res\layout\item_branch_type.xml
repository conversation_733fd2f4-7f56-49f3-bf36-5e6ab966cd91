<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="270dp"
    android:id="@+id/item_branch_type_container"
    android:layout_marginStart="0dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="1dp"

    android:foreground="?android:attr/selectableItemBackground"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true"
    android:clickable="true"

    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >


        <ImageView
            android:id="@+id/photo"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@null"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@tools:sample/avatars"


            />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/label_item_stores_category_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="8dp"
            android:background="@null"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/photo"
            tools:text="testtesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest" />



    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>