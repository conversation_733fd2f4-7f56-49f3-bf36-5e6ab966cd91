package com.techcubics.data.repos.home.order

import com.techcubics.data.model.pojo.Order
import com.techcubics.data.model.requests.CheckoutRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

class OrderRepoImpl(private val retrofitBuilder: RetrofitBuilder) : OrderRepo, RepositoryResponse {

    override suspend fun getOrderDetails(order_id: Int): BaseResponse<Order>? {
        try {
            val result = retrofitBuilder.getBindObject()?.getOrderDetails(order_id)
            return baseResponse(result)

        }  catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getCurrentOrders(): BaseResponse<ArrayList<Order>>? {

        try {
            val result = retrofitBuilder.getBindObject()?.getCurrentOrders()
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun getPastOrders(): BaseResponse<ArrayList<Order>>? {

        try {
            val result = retrofitBuilder.getBindObject()?.getPastOrders()
            return baseResponse(result)

        }  catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun checkout(request:CheckoutRequest): BaseResponse<Order>? {

        return try {
            val result = retrofitBuilder.getBindObject()?.checkout(request = request)
            baseResponse(result)

        }  catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
}