buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.android.tools.build:gradle:8.11.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath 'androidx.navigation:navigation-safe-args-gradle-plugin:2.8.4'
    }
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.11.1' apply false
    id 'com.android.library' version '8.11.1' apply false
    id 'org.jetbrains.kotlin.android' version '2.0.21' apply false
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin' version '2.0.1' apply false
}