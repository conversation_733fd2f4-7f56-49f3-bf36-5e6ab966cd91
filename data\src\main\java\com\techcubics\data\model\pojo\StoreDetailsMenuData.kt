package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class StoreDetailsMenuData(
    @SerializedName("id")
    val id: Int,
    @SerializedName("menu_id")
    val menuID: Int,
    @SerializedName("code")
    var code: String? = null,
    @SerializedName("image")
    val image: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("subcategories")
    var subcategories: ArrayList<SubCategory> = arrayListOf(),
    @SerializedName("products")
    val products: MutableList<StoreDetailsMenuProductData>
)

