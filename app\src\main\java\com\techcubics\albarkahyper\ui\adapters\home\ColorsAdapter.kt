package com.techcubics.albarkahyper.ui.adapters.home

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.ColorObject
import com.techcubics.albarkahyper.databinding.ItemColorBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductColorHolderItem
import com.techcubics.albarkahyper.ui.views.products.searchResult.SelectColorInFilterFragment


class ColorsAdapter(val handler: SelectColorInFilterFragment?) :
    RecyclerView.Adapter<ProductColorHolderItem>() {
    private val TAG = "ColorsAdapter"
    lateinit var items: List<ColorObject>
    lateinit var itemBinding: ItemColorBinding
    var check = false

    fun setItemsList(_items: List<ColorObject>) {
        items = _items

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductColorHolderItem {
        itemBinding = ItemColorBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductColorHolderItem(itemBinding,parent.context,handler)


    }

    override fun onBindViewHolder(holder: ProductColorHolderItem, position: Int) {

        if(check){
            holder.binding.root.background = null
        }
        holder.bind(items.get(position),check)

    }

    override fun getItemCount(): Int {
        return items.size
    }

    fun resetColors() {
        check = true
        notifyDataSetChanged()
    }
}