<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_popup_dialog"
    android:minWidth="300dp">

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/confirm"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginTop="20dp"
                    android:textAppearance="@style/header_confirmdeleteedialog"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/are_you_sure"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="8dp"
                    android:textAppearance="@style/sub_header_confirmdeleteedialog"
                    android:layout_marginStart="8dp" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp">
                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:text="@string/no"
                        android:id="@+id/no"
                        android:layout_marginEnd="5dp"
                        style="@style/label_cancel_confirmdeleteedialog"
                        android:backgroundTint="@color/app_color"/>
                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/et_style_with_borders"
                        android:text="@string/yes"
                        android:id="@+id/yes"
                        style="@style/label_ok_confirmdeleteedialog"
                        android:layout_marginStart="5dp" />
                </LinearLayout>
            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
