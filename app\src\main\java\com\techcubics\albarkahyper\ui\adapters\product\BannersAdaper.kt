package com.techcubics.albarkahyper.ui.adapters.product

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.databinding.ItemBranchTypeBinding
import com.techcubics.albarkahyper.databinding.ItemSliderBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.BranchTypeHolderItem
import com.techcubics.albarkahyper.ui.adapters.holders.product.BannerHolderItem


class BannersAdaper (): RecyclerView.Adapter<BannerHolderItem>() {

    lateinit var items:List<BannerData>

    fun setItemsList(_items:List<BannerData>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BannerHolderItem {
        val itemBinding = ItemSliderBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BannerHolderItem(itemBinding,parent.context)
    }

    override fun getItemViewType(position: Int): Int {

        if (position % 2 == 0)
          return 1
        else
            return 2

    }

    override fun onBindViewHolder(holder: BannerHolderItem, position: Int) {
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return items.size
    }




}