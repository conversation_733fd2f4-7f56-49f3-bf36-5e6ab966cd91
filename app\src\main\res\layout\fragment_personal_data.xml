<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/personal_information"
        android:textSize="18dp"
        android:textAppearance="@style/header_auth"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/info">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="10dp">
                <EditText
                    android:id="@+id/name_edt"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style"
                    android:hint="@string/first_name"
                    android:inputType="textNoSuggestions"
                    android:padding="10dp"
                    android:textAppearance="@style/label_auth_edittext" />
                <EditText
                    android:id="@+id/second_name_edt"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style"
                    android:hint="@string/last_name"
                    android:inputType="textNoSuggestions"
                    android:padding="10dp"
                    android:textAppearance="@style/label_auth_edittext" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/et_style"
                android:gravity="right"
                android:layoutDirection="ltr"
                android:orientation="horizontal"
                android:weightSum="4">

                <com.hbb20.CountryCodePicker
                    android:id="@+id/country_picker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:ccp_showNameCode="false"
                    app:ccp_defaultNameCode="EG" />

                <EditText
                    android:id="@+id/phone_edt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/et_style"
                    android:hint="@string/phone_number"
                    android:inputType="number"
                    android:singleLine="false"
                    android:textAppearance="@style/label_auth_edittext" />
            </LinearLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:boxStrokeWidth="0dp"
                app:hintEnabled="false"
                app:boxStrokeWidthFocused="0dp"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/password_edt"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:inputType="textPassword"
                    android:hint="@string/password"
                    android:background="@drawable/et_style"
                    android:padding="10dp"
                    android:textAppearance="@style/label_auth_edittext" />
            </com.google.android.material.textfield.TextInputLayout>

<!--            <include-->
<!--                android:id="@+id/validate_pass_group"-->
<!--                layout="@layout/include_password_validation_layout"/>-->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:boxStrokeWidth="0dp"
                app:hintEnabled="false"
                app:boxStrokeWidthFocused="0dp"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/confirm_password_edt"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:inputType="textPassword"
                    android:hint="@string/confirm_password"
                    android:background="@drawable/et_style"
                    android:padding="10dp"
                    android:textAppearance="@style/label_auth_edittext" />
            </com.google.android.material.textfield.TextInputLayout>


            <include
                android:id="@+id/terms_conditions_layout"
                layout="@layout/include_terms_and_conditions_layout"/>


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />

</androidx.constraintlayout.widget.ConstraintLayout>


