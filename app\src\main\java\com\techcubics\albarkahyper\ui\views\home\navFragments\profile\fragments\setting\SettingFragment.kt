package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.DialogRateApp
import com.techcubics.albarkahyper.databinding.FragmentSettingBinding

class SettingFragment : Fragment() {

    private lateinit var binding: FragmentSettingBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSettingBinding.inflate(layoutInflater, container, false)
        initViews()
        return binding.root
    }


    private fun initViews() {

        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
        
        binding.toolbar.tvTitle.text = getString(com.techcubics.style.R.string.setting)
        binding.callUs.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_settingFragment_to_callUsFragment)
        }
        binding.termsConditions.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_settingFragment_to_termsAndConditionsFragment)
        }
        binding.rateApp.setOnClickListener {
            val rating = DialogRateApp()
            rating.show(childFragmentManager, "rating")
        }
        binding.knowUs.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,binding.actionLoadingAnimation.root)
            findNavController().navigate(R.id.action_settingFragment_to_aboutUsFragment)
        }
        binding.shareApp.setOnClickListener {
            Helper.shareLink(
                requireContext(),
                "https://play.google.com/store/apps/details?id=${requireContext().packageName}"
            )
        }
    }

   

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}