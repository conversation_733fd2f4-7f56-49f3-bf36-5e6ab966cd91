# تعليمات بناء التطبيق - AlBarkaHyper Build Instructions

## متطلبات البناء - Build Requirements

### 1. متطلبات النظام - System Requirements
- **Java**: JDK 17 أو أحدث
- **Android Studio**: Arctic Fox أو أحدث
- **Android SDK**: API Level 35 (Android 15)
- **Gradle**: 8.10.2 أو أحدث
- **نظام التشغيل**: Windows 10/11, macOS 10.15+, أو Linux

### 2. إعداد البيئة - Environment Setup

#### Windows:
```batch
# تعيين متغيرات البيئة
set JAVA_HOME=C:\Program Files\Java\jdk-17
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%
```

#### macOS/Linux:
```bash
# إضافة إلى ~/.bashrc أو ~/.zshrc
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

## خطوات البناء - Build Steps

### 1. تحضير المشروع - Project Preparation
```bash
# استنساخ المشروع
git clone [repository-url]
cd AlbarkaHyper-android-development

# تنظيف المشروع
./gradlew clean
```

### 2. بناء APK للتطوير - Debug APK Build
```bash
# بناء APK للتطوير
./gradlew assembleDebug

# الملف سيكون في:
# app/build/outputs/apk/debug/app-debug.apk
```

### 3. بناء APK للإنتاج - Release APK Build
```bash
# بناء APK للإنتاج
./gradlew assembleRelease

# الملف سيكون في:
# app/build/outputs/apk/release/app-release.apk
```

### 4. بناء Android App Bundle (AAB)
```bash
# بناء AAB للإنتاج (مفضل لـ Google Play)
./gradlew bundleRelease

# الملف سيكون في:
# app/build/outputs/bundle/release/app-release.aab
```

## استخدام ملفات البناء المجهزة - Using Prepared Build Scripts

### Windows:
```batch
# تشغيل ملف batch
build_apk.bat

# أو تشغيل PowerShell script
powershell -ExecutionPolicy Bypass -File build_apk.ps1
```

### macOS/Linux:
```bash
# إعطاء صلاحيات التنفيذ
chmod +x gradlew

# تشغيل البناء
./gradlew clean assembleRelease bundleRelease
```

## التحقق من البناء - Build Verification

### 1. فحص ملف APK
```bash
# فحص معلومات APK
aapt dump badging app/build/outputs/apk/release/app-release.apk

# فحص التوقيع
jarsigner -verify -verbose -certs app/build/outputs/apk/release/app-release.apk
```

### 2. اختبار التثبيت
```bash
# تثبيت على جهاز متصل
adb install app/build/outputs/apk/release/app-release.apk

# أو تثبيت مع الاستبدال
adb install -r app/build/outputs/apk/release/app-release.apk
```

## حل المشاكل الشائعة - Troubleshooting

### 1. مشكلة Java Version
```
Error: Unsupported Java version
الحل: تأكد من استخدام JDK 17
```

### 2. مشكلة Android SDK
```
Error: Android SDK not found
الحل: تعيين ANDROID_HOME بشكل صحيح
```

### 3. مشكلة Gradle
```
Error: Gradle version incompatible
الحل: تحديث Gradle إلى الإصدار المطلوب
```

### 4. مشكلة التوقيع
```
Error: Keystore not found
الحل: التأكد من وجود ملف keystore في المسار الصحيح
```

## معلومات التوقيع - Signing Information

### إعدادات Keystore:
- **ملف Keystore**: `app/signature/albarkahypersecretkeystore.jks`
- **Alias**: `albarkahyperkey`
- **كلمة المرور**: محفوظة في `gradle.properties`

### أوامر إنشاء Keystore جديد:
```bash
keytool -genkey -v -keystore albarkahypersecretkeystore.jks \
  -alias albarkahyperkey -keyalg RSA -keysize 2048 -validity 10000
```

## التحقق النهائي - Final Verification

### قائمة التحقق قبل الرفع:
- [ ] APK/AAB تم بناؤه بنجاح
- [ ] التوقيع صحيح
- [ ] اختبار التثبيت نجح
- [ ] Target SDK = 35
- [ ] Version Code محدث
- [ ] جميع الصلاحيات صحيحة
- [ ] لا توجد أخطاء في Lint
- [ ] اختبار على أجهزة مختلفة

## ملاحظات مهمة - Important Notes

1. **استخدم AAB للنشر**: Google Play يفضل Android App Bundle
2. **احتفظ بنسخة من Keystore**: لا يمكن استرداده إذا فُقد
3. **اختبر على أجهزة حقيقية**: المحاكي قد لا يكشف جميع المشاكل
4. **راجع سياسات Google Play**: تأكد من الامتثال لجميع المتطلبات
