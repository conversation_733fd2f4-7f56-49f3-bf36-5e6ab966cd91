package com.techcubics.data.repos.chat

import com.techcubics.data.model.pojo.OrderMessageData
import com.techcubics.data.model.pojo.OrderRoomsData
import com.techcubics.data.model.pojo.SendOrderData
import com.techcubics.data.model.requests.OrderSendCallRequest
import com.techcubics.data.model.requests.chat.OrderMessagesRequest
import com.techcubics.data.model.requests.chat.OrderRoomsRequest
import com.techcubics.data.model.requests.chat.OrderSendRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

class OrderChatRepoImpl(private val retrofitBuilder: RetrofitBuilder): RepositoryResponse,OrderChatRepo {

    override suspend fun getRooms(request: OrderRoomsRequest): BaseResponse<MutableList<OrderRoomsData>>?{

        return try {
            val result = retrofitBuilder.getBindObject()?.roomsCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun send(request: OrderSendCallRequest): BaseResponse<SendOrderData>?{

        return try {

            val result = retrofitBuilder.getBindObject()?.orderSendCall(request=request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getRoomHistory(request: OrderMessagesRequest): BaseResponse<OrderMessageData>?{
        return try {
            val result = retrofitBuilder.getBindObject()?.roomMessagesCall(order_id = request.orderID)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
}