package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoresNearbyData
import com.techcubics.albarkahyper.databinding.ItemNearbyBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.NearByHolderItem


class NearByStoresAdapter (): RecyclerView.Adapter<NearByHolderItem>() {

    lateinit var items:List<StoresNearbyData>

    fun setItemsList(_items:List<StoresNearbyData>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NearByHolderItem {
        val itemBinding = ItemNearbyBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return NearByHolderItem(itemBinding,parent.context)
    }

    override fun onBindViewHolder(holder: NearByHolderItem, position: Int) {

        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
        return items.size
    }
}