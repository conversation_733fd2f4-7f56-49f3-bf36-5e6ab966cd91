package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName


data class LiveChatRoomData (

  @SerializedName("conversation_id" ) var conversationId : Int?          = null,
  @SerializedName("sender_info"     ) var senderInfo     : SenderInfo?   = SenderInfo(),
  @SerializedName("receiver_info"   ) var receiverInfo   : ReceiverInfo? = ReceiverInfo(),
  @SerializedName("chats_un_seen"   ) var chatsUnSeen    : Int?          = null,
  @SerializedName("last_message"    ) var lastMessage    : String?       = null,
  @SerializedName("format_date") var lastMessageTime : String? = null

)

data class ReceiverInfo (

  @SerializedName("id"               ) var id              : Int?     = null,
  @SerializedName("shop_id"        ) var clinicId        : Int?     = null,
  @SerializedName("name"             ) var name            : String?  = null,
  @SerializedName("slug"             ) var slug            : String?  = null,
  @SerializedName("description"      ) var description     : String?  = null,
  @SerializedName("logo"             ) var logo            : String?  = null,
  @SerializedName("address"          ) var address         : String?  = null,
  @SerializedName("favourite"        ) var favourite       : Boolean? = null,
  @SerializedName("is_rate"          ) var isRate          : Boolean? = null,
  @SerializedName("rate"             ) var rate            : Float?     = null,
  @SerializedName("rate_count"       ) var rateCount       : Int?     = null,
  @SerializedName("link_google_play" ) var linkGooglePlay  : String?  = null,
  @SerializedName("link_apple_store" ) var linkAppleStore  : String?  = null,
  @SerializedName("qr_image"         ) var qrImage         : String?  = null,
  @SerializedName("background_image" ) var backgroundImage : String?  = null,
  @SerializedName("lat"              ) var lat             : String?  = null,
  @SerializedName("lng"              ) var lng             : String?  = null,
  @SerializedName("owner"            ) var owner           : Owner?   = Owner(),
  @SerializedName("chat_enable"      ) var chatEnable      : Boolean? = null
)

data class SenderInfo (

  @SerializedName("name"                          ) var name                      : String?  = null,
  @SerializedName("email"                         ) var email                     : String?  = null,
  @SerializedName("phone"                         ) var phone                     : String?  = null,
  @SerializedName("avatar"                        ) var avatar                    : String?  = null,
  @SerializedName("country"                       ) var country                   : Country? = Country(),
  @SerializedName("governorate"                   ) var governorate               : String?  = null,
  @SerializedName("region"                        ) var region                    : String?  = null,
  @SerializedName("address"                       ) var address                   : String?  = null,
  @SerializedName("gender"                        ) var gender                    : String?  = null,
  @SerializedName("birthday"                      ) var birthday                  : String?  = null,
  @SerializedName("age"                           ) var age                       : String?  = null,
  @SerializedName("id"                            ) var id                        : Int?     = null,
  @SerializedName("user_id"                       ) var userId                    : Int?     = null,
  @SerializedName("total_of_unread_orders_chats"  ) var totalOfUnreadOrdersChats  : Int?     = null,
  @SerializedName("total_of_unread_support_chats" ) var totalOfUnreadSupportChats : Int?     = null

)

data class Owner (

  @SerializedName("id"               ) var id              : Int?    = null,
  @SerializedName("name"             ) var name            : String? = null,
  @SerializedName("email"            ) var email           : String? = null,
  @SerializedName("brand_name"       ) var brandName       : String? = null,
  @SerializedName("address"          ) var address         : String? = null,
  @SerializedName("phone"            ) var phone           : String? = null,
  @SerializedName("avatar"           ) var avatar          : String? = null,
  @SerializedName("qr_image"         ) var qrImage         : String? = null,
  @SerializedName("background_image" ) var backgroundImage : String? = null

)