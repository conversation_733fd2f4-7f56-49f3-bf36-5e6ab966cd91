<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraints_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/btn_ripple_circle_gray"
        android:clickable="true"
        android:orientation="vertical"
        android:paddingVertical="2dp"
        android:paddingHorizontal="8dp">

        <ProgressBar
            android:id="@+id/progressBar2"
            style="?android:attr/progressBarStyle"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:indeterminate="true"
            android:indeterminateTint="@color/white"
            android:indeterminateTintMode="src_atop"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:targetApi="lollipop" />
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_marginVertical="10dp"
            android:layout_marginHorizontal="25dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <ImageView
                android:id="@+id/icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="14dp"
                android:scaleType="center"
                android:src="@drawable/ic_outline_cart"
                android:visibility="visible" />

            <TextView
                android:id="@+id/btnAdd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/add"
                android:textColor="@color/white"
                android:textSize="18sp"/>
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

