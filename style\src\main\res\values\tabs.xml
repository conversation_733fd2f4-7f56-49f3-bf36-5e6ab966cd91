<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="tab_theme_5" parent="Widget.MaterialComponents.TabLayout">
        <item name="materialThemeOverlay">@style/tab_theme_1</item>
        <item name="tabTextAppearance">@style/tab_theme_2</item>
    </style>
    <style name="tab_theme_1" parent="">
        <item name="colorPrimary">?attr_color_4</item>
        <item name="colorSurface">?attr_color_2</item>
        <item name="colorOnSurface">?attr_color_13</item>
    </style>
    <style name="tab_theme_2" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">?attr_color_13</item>
        <item name="android:fontFamily">?attr/tablayout_font_family_1</item>
        <item name="android:textSize">@dimen/fontsize_5</item>
    </style>
    <style name="tab_round_textAppearance">
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">?tab_round_textAppearance_text_color</item>
        <item name="android:fontFamily">?tab_round_textAppearance_font_family</item>
        <item name="android:textSize">?tab_round_textAppearance_font_size</item>
    </style>
    <style name="tab_theme_3">
        <item name="android:background">?attr_color_5</item>
    </style>
</resources>