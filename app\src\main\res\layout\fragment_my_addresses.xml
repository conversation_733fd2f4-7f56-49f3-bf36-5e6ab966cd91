<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/myaddresses_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.views.home.navFragments.profile.fragments.updateprofile.UpdateProfileFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar"
            android:layout_marginTop="24dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="50dp">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/addressRV"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
                    tools:itemCount="4"
                    tools:listitem="@layout/item_address"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />
                <include
                    android:id="@+id/server_error_layout"
                    layout="@layout/include_placeholder"
                    android:gravity="center"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>
        <include
            android:id="@+id/placeholder_layout"
            layout="@layout/include_placeholder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:visibility="gone"/>

        <include
            android:id="@+id/action_loading_animation"
            layout="@layout/include_action_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />




    </androidx.constraintlayout.widget.ConstraintLayout>