package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.content.Intent
import androidx.lifecycle.*
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreDetailsRatesData
import com.techcubics.albarkahyper.databinding.ItemRateBinding
import com.techcubics.albarkahyper.common.Helper

class RateHolderItem<T> (val binding: ItemRateBinding, val context: Context, val vm: ViewModel): RecyclerView.ViewHolder(binding.root) {


    private  val TAG = "RateHolderItem"
    private lateinit var intent:Intent
    fun bind(data:T) {

        when(data){

            is StoreDetailsRatesData ->{
                fillByStore(data)
            }

        }

    }

    fun fillByStore(data:StoreDetailsRatesData){

        Helper.loadImage(context,data.avatar,binding.logo)
        binding.tvName.text=data.userName
        binding.rateBar.rating=data.degree
        binding.tvDate.text=data.date
        binding.tvDescription.text=data.comment

    }


}