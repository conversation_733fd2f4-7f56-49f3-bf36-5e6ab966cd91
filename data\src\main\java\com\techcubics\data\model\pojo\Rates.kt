package com.techcubics.data.model.pojo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Rates (

    @SerializedName("id"      ) var id      : Int?    = null,
    @SerializedName("rate_id" ) var rateId  : Int?    = null,
    @SerializedName("degree"  ) var degree  : Int?    = null,
    @SerializedName("comment" ) var comment : String? = null,
    @SerializedName("date"    ) var date    : String? = null,
    @SerializedName("user"    ) var user    : String? = null,
    @SerializedName("avatar"  ) var avatar  : String? = null

):Parcelable