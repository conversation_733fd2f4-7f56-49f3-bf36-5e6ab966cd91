package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.ui.adapters.holders.home.DiscountHolderItem
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.ItemDiscountsBinding
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler


class DiscountsAdapter<T>(val margin:Int=0,private val onClickListener: IOnAdapterItemClickHandler): PagingAdapter() {



    lateinit var items:List<T>
    lateinit var itemBinding: ItemDiscountsBinding

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DiscountHolderItem<T> {
        itemBinding = ItemDiscountsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DiscountHolderItem(itemBinding,parent.context,margin,onClickListener)
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as DiscountHolderItem<T>
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_discounts
    }

    override fun getPagingItemCount(): Int {
        return items.size
    }



}