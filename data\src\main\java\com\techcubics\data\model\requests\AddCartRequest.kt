package com.techcubics.data.model.requests

import com.google.gson.annotations.SerializedName

data class AddCartRequest(
    @SerializedName("shop_id") var shopID: Int? = null,
    @SerializedName("model_type") var modelType: String? = null,
    @SerializedName("model_id") var modelId: Int? = null,
    @SerializedName("qty") var qty: Int? = null,
    @SerializedName("size_id") var sizeId: Int? = null,
    @SerializedName("color_id") var colorId: Int? = null
) {
    constructor(
        shopID: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
    ) : this(shopID,modelType, modelId, qty ,null,null)
}