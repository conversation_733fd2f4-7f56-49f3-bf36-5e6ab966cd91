<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="0.5dp"
    android:background="@color/white"
    >

    <androidx.appcompat.widget.Toolbar

        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
       >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="@dimen/icons_size_height_weight_9"
                android:layout_height="@dimen/icons_size_height_weight_9"
                android:layout_marginEnd="16dp"
                android:background="@null"
                android:scaleType="centerInside"
                android:src="@drawable/ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tvTitle"
                style="@style/toolbar_caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginEnd="8dp"
                android:drawablePadding="10dp"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="@string/offer_price"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btnBack"
                app:layout_constraintTop_toTopOf="parent"
                app:drawableStartCompat="@drawable/ic_support_user" />
        </androidx.constraintlayout.widget.ConstraintLayout>



    </androidx.appcompat.widget.Toolbar>


</com.google.android.material.appbar.AppBarLayout>

