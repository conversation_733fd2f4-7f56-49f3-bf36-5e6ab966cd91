package com.techcubics.albarkahyper.ui.adapters.product

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.ProductDetailsDto
import com.techcubics.albarkahyper.databinding.ItemRateBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.RatesViewHolder
import com.techcubics.data.model.pojo.Rates
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemMainCategoryBinding
import com.techcubics.albarkahyper.databinding.ItemProductBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.MainCategoryHolderItem
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductHolderItem

class ProductsAdapter<T>(val margin:Int=0, val onFavClickListener: IFavClickListener?=null, val onClickHandler: IOnAdapterItemClickHandler) :
    RecyclerView.Adapter<ProductHolderItem<T>>() {


    lateinit var items:List<T>

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductHolderItem<T> {
        val itemBinding = ItemProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductHolderItem(itemBinding,parent.context,margin,onFavClickListener!!, onClickHandler )
    }


    override fun onBindViewHolder(holder: ProductHolderItem<T>, position: Int) {
        holder.bind(items.get(position))

        holder.binding.root.animation =
            AnimationUtils.loadAnimation(holder.itemView.context, com.techcubics.style.R.anim.recycleview)
    }

    override fun getItemCount(): Int {
        return items.size
    }
}
