package com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.fragments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.FavouriteStore
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.common.IPagePagedRowset
import com.techcubics.albarkahyper.databinding.FragmentFavoritesStoresBinding
import com.techcubics.albarkahyper.ui.adapters.home.StoresAdapter
import com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.viewmodels.FavoritesViewModel
import com.techcubics.shared.enums.LottieIconEnum
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class FavoritesStoresFragment : Fragment() , IPagePagedRowset<FavouriteStore>, IFavClickListener {

    private  lateinit var binding: FragmentFavoritesStoresBinding
    private lateinit var storesAdapter: StoresAdapter<FavouriteStore>
    private  val sharedFragmentViewModel: FavoritesViewModel by viewModel<FavoritesViewModel>()
    private var isLoading:Boolean=false
    private lateinit var resultList:ArrayList<FavouriteStore>
    private var _operation:Int=-1
    private var _position: Int?=null
    private  val TAG = "FavoritesStoresFragment"
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    companion object {
        val instance: FavoritesStoresFragment = FavoritesStoresFragment()
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding= FragmentFavoritesStoresBinding.inflate(inflater,container,false)



        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {


        showHidePlaceHolder(show=false,type= null,message=null)
        binding.pagingLoadingImg.visibility=View.GONE
        binding.rvStores.visibility=View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)

        if(SharedPreferencesManager.isLoggedIn().equals("false")){
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            showHidePlaceHolder(show=true,type= LottieIconEnum.Error ,message=getString(com.techcubics.style.R.string.message_error_loading_login_required))
        }else{
           sharedFragmentViewModel.getStores(1)
        }


        //

    }

    override  fun observers(){


        sharedFragmentViewModel.storesResponse.observe(viewLifecycleOwner, Observer { it->


            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)

            try {
                if (it.status!!) {

                    if(it.data!=null){

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvStores.visibility = View.VISIBLE

                        }else{

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }
        })

        sharedFragmentViewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner,Observer{

            try {

                if(it.status!!){

                    sharedFragmentViewModel.storesResponse.value!!.data!!.removeAt(_position!!)

                    if(sharedFragmentViewModel.storesResponse.value!!!!.data!!.size > 0) {
                        storesAdapter.notifyDataSetChanged()
                    }else {
                        binding.pagingLoadingImg.visibility = View.GONE
                        binding.rvStores.visibility = View.GONE
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = getString(com.techcubics.style.R.string.message_empty_list_general)
                        )
                    }

                }else{

                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvStores.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            }catch (ex:Exception){


                binding.pagingLoadingImg.visibility = View.GONE
                binding.rvStores.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }

        })


    }
    override fun events(){



    }


    override fun showData( items:List<FavouriteStore>){

        if(sharedFragmentViewModel.storesResponse.value!!.pagingator?.currentPage==1) {
            resultList= items as ArrayList<FavouriteStore>
            storesAdapter = StoresAdapter<FavouriteStore>(onFavClickListener=this)
            storesAdapter.setItemsList(resultList)
            binding.rvStores.adapter = storesAdapter
            binding.rvStores.layoutManager = GridLayoutManager(context, 2)
            //
            var pageDelegate =
                PagingDelegate.Builder(storesAdapter).attachTo(binding.rvStores)
                    .listenWith(this).build()


        }else{

            items.forEach {
                resultList.add(it)
            }
            storesAdapter.notifyDataSetChanged()
            onDonePaging()
        }

        Log.d(TAG, "showData: ${resultList.size}")

    }

    override fun onPage(p0: Int) {


        if(!isLoading){
            Log.d(TAG, "onPage: ${p0}")
            if(sharedFragmentViewModel.storesResponse.value!!.pagingator?.hasMorePages!!) {
                isLoading=true
                binding.pagingLoadingImg.visibility=View.VISIBLE
                sharedFragmentViewModel.getStores(sharedFragmentViewModel.storesResponse.value!!.pagingator!!.currentPage+1)
            }
        }
    }
    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility=View.GONE
        isLoading=false
    }

    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                } else -> throw IllegalStateException("error")
            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }

    override fun onFavClick(parent:Int,position: Int, operation: Int) {

        _operation=operation
        _position=position
        sharedFragmentViewModel.addRemoveStoreFav(sharedFragmentViewModel.storesResponse.value!!.data?.get(position)!!.shopID)
    }


}