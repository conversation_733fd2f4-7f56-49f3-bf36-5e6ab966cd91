package com.techcubics.data.repos.home
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.NotificationRequest
import com.techcubics.data.model.requests.UpdateFireBaseTokenCallRequest
import com.techcubics.data.model.requests.home.*
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

class HomeRepoImpl(private val retrofitBuilder: RetrofitBuilder): RepositoryResponse, HomeRepo {
    private  val TAG = "HomeRepo"
    override suspend fun getHome(request:HomeRequest):BaseResponse<HomeData>? {
        return try {
            val result =  retrofitBuilder.getBindObject()?.homeCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getCategories():BaseResponse<MutableList<Category>>?{

        return try {
            val result =  retrofitBuilder.getBindObject()?.categoriesCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getOffers(offerRequest:OffersRequest): BaseResponse<MutableList<Offer>>?{

        return try {
            val result =  retrofitBuilder.getBindObject()?.offersCall(price = offerRequest.priceOrder.value,  page = offerRequest.page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getSaves(saveRequest:SavesRequest): BaseResponse<MutableList<Save>>?{

        return try {
            val result =  retrofitBuilder.getBindObject()?.savesCall(price = saveRequest.priceOrder.value,  page = saveRequest.page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    /*override suspend fun getDiscounts(discountsRequest:DiscountRequest): BaseResponse<MutableList<Discount>>?{

        return try {
            val result =  retrofitBuilder.getBindObject()?.discountsCall(price = discountsRequest.priceOrder.value,  page = discountsRequest.page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }*/

    override suspend fun updateFCMToken(request: UpdateFireBaseTokenCallRequest): BaseResponse<String>? {

        return try {
            val result = retrofitBuilder.getBindObject()?.updateFireBaseTokenCall(

                request=request
            )
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun getNotifications(request:NotificationRequest):BaseResponse<MutableList<Notification>>?{

        return try {

            val result =  retrofitBuilder.getBindObject()?.notifications(page=request.page)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }



    override suspend fun getNearStoreTypes(request: NearBranchTypeRequest): BaseResponse<NearStoreTypes>? {

        return try {

            val result =  retrofitBuilder.getBindObject()?.nearStoreTypesCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun readNotifications(): BaseResponse<Any>? {

        return try {

            val result =  retrofitBuilder.getBindObject()?.readNotifications()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
}


