<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
android:background="@drawable/bg_top_shadow"
    xmlns:app="http://schemas.android.com/apk/res-auto"
  android:paddingStart="16dp"
    android:paddingEnd="8dp"
    android:paddingBottom="8dp"
    android:paddingTop="16dp"

    >

    <EditText
        android:id="@+id/txtMessage"
        style="@style/chat_send_editbox"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"

        android:background="@drawable/et_chat_style"
        android:hint="@string/chat_hint"
        android:inputType="textMultiLine|text"
        android:maxLines="6"
        android:gravity="start|top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnSend"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/btnSend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:src="@drawable/ic_messenger_send"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>