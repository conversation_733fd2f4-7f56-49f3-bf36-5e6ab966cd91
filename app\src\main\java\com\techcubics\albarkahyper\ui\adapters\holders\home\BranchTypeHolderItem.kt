package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.databinding.ItemBranchTypeBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.shared.constants.Constants


class BranchTypeHolderItem (val binding: ItemBranchTypeBinding, val context: Context,val onClick: IOnAdapterItemClickHandler): RecyclerView.ViewHolder(binding.root) {

    private  val TAG = "BranchTypeHolderItem"
    private lateinit var intent:Intent

    fun bind(branchType: StoreTypes){

        binding.tvTitle.text=branchType.name
        Helper.loadImage(context,branchType.image,binding.photo)

        binding.root.setOnClickListener {view->
            Log.d(TAG, "bind: ${branchType.id}")
            onClick.onItemClicked(branchType.branchTypeID,branchType.name)
        }

    }

}