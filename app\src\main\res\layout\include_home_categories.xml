<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
   >

    <TextView
        android:id="@+id/textView"
        style="@style/label_section_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/section_categories_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tvMore"
        style="@style/label_section_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/more"
        android:drawablePadding="10dp"
        android:clickable="true"
        android:background="?attr/selectableItemBackground"
        app:layout_constraintBottom_toBottomOf="@+id/textView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/textView"
        app:drawableEndCompat="@drawable/ic_arrow_left" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCategories"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView"
        tools:itemCount="1"
        tools:listitem="@layout/item_category" />
</androidx.constraintlayout.widget.ConstraintLayout>