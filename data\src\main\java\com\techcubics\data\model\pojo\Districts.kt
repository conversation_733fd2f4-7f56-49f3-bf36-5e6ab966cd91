package com.techcubics.data.model.pojo

import android.graphics.Region
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Districts(

    @SerializedName("shop_id")
    val shopId: Int? = null,

    @SerializedName("regions")
    val regions: List<Region?>? = null,

    @SerializedName("min_order_price")
    val minOrderPrice: Float? = null,

    @SerializedName("governorate")
    val governorate: Governerate? = null,

    @SerializedName("id")
    val id: Int? = null
):Parcelable