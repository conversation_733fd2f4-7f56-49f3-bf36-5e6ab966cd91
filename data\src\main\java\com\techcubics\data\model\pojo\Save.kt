package com.techcubics.data.model.pojo

import android.os.CountDownTimer
import com.google.gson.annotations.SerializedName

data class Save(
    @SerializedName("id")
    val id: Int,
    @SerializedName("save_id")
    val saveID: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("slug")
    var slug: String? = null,
    @SerializedName("price")
    val price: Float,
    @SerializedName("video")
    val video: String,
    @SerializedName("start")
    val startDate: String,
    @SerializedName("end")
    val endDate: String,
    @SerializedName("diff_day")
    val diff_day: Int,
    @SerializedName("from")
    val fromTime: String,
    @SerializedName("to")
    val toTime: String,
    @SerializedName("hours")
    val hours: Int,
    @SerializedName("minutes")
    val minutes: Int,
    @SerializedName("images")
    val Images: MutableList<Image>,
    @SerializedName("shop")
    val shop:Store,
    @SerializedName("products")
    val products: MutableList<Product>,
    @SerializedName("rate")
    val rate: Int,
    @SerializedName("rate_count")
    val rate_count: Int,
    @SerializedName("model_type")
    val model_type: String,
    @SerializedName("qty_cart")
    val quantityCart: Int,

    var timer: CountDownTimer
)
