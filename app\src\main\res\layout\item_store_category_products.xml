<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="8dp"
    android:paddingEnd="8dp">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/icons_size_height_weight_9"
        android:layout_height="@dimen/icons_size_height_weight_9"
        android:layout_marginTop="8dp"
        tools:src="@drawable/ic_fire"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        style="@style/label_section_header"
        android:text="@string/section_discounts_title"
        app:layout_constraintBottom_toBottomOf="@+id/icon"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toTopOf="@+id/icon"
        app:layout_constraintVertical_bias="0.82"
        android:maxLines="1"
        android:ellipsize="end"/>



    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvProducts"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:listitem="@layout/item_store_product" />

</androidx.constraintlayout.widget.ConstraintLayout>