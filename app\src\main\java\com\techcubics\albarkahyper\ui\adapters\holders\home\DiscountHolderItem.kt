package com.techcubics.albarkahyper.ui.adapters.holders.home

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.CircleProgressButton
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.databinding.ItemDiscountsBinding
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Discount
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import org.koin.android.ext.android.get


class DiscountHolderItem<T> (val binding: ItemDiscountsBinding, val context: Context,val margin:Int=0,
    val onClickListener: IOnAdapterItemClickHandler
): RecyclerView.ViewHolder(binding.root) {

    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }

    fun bind(data: T){
        when (SharedPreferencesManager.getLanguage()){
            "ar"->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
            "eg"->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
            "sa"->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
            else ->  binding.root.layoutParams= Helper.setMargin(binding.root,margin,0,margin,0)
        }


        when(data){
            is Discount ->{
                fillByGeneral(data)
            }
            /*is STORE_DETAILS_DISCOUNTS_DATA->{
                fillByStore(data)
            }*/
        }

    }

    fun fillByGeneral(discount: Discount){

        val progressButton: CircleProgressButton
        progressButton = CircleProgressButton(context)
        progressButton.initRoundButton(binding.btnOrder)
        binding.tvTitle.text=discount.productName
        binding.tvDescription.text=discount.productDescription
        discount.shop?.logo?.let { Helper.loadImage(context, it,binding.logo) }
        binding.tvBranchName.text=discount.shop?.name

        binding.tvPercentage.text="${discount.percent.toString()} %"
        binding.tvOldPrice.text="${discount.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        binding.tvCurrentPrice.text="${discount.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"

      //  binding.tvOldPrice.paintFlags= Paint.STRIKE_THRU_TEXT_FLAG


        Helper.loadImage(context,discount.images[0].path,binding.imgThumb)
        itemView.setOnClickListener {
            onClickListener.onItemClicked(discount.id, RateTypesEnum.Discount.value)
        }
        binding.branchContainer.setOnClickListener {view->
            onClickListener.onItemClicked(discount.shop?.id, RateTypesEnum.Store.value)
        }
         binding.btnOrder.constraintsLayout.setOnClickListener {
             if(SharedPreferencesManager.isLoggedIn() == "true"){
                 progressButton.btnRoundActivated()
             }
            onClickListener.addToCart(
                EndPointConstants.add_discount,
                discount.shop?.id,
                discount.modelType,
                discount.id,
                1,
                maxQty = 1,
                minQty = 1,
                minOrder = 0f,
                itemPosition = bindingAdapterPosition
            )
        }
    }

 
}