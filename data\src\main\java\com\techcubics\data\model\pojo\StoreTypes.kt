package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName


data class StoreTypesList(
    @SerializedName("branch_type")
    val branchTyoeList : ArrayList<StoreTypes>
)

data class StoreTypes(

    @SerializedName("id")
    val id: Int,
    @SerializedName("branch_type_id")
    val branchTypeID: Int,
    @SerializedName("image")
    val image: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("count")
    val count: Int


)
