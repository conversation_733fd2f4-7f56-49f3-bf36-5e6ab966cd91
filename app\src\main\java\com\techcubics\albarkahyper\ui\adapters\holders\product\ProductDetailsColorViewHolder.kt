package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.graphics.Color
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemColorBinding
import com.techcubics.albarkahyper.databinding.ItemProductColorBinding
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.style.R

class ProductDetailsColorViewHolder(
    private val binding: ItemProductColorBinding,
    private val context: Context,
    private val setColor: (Int) -> Unit,
    private val getColor: () -> Int,
    private val updateAdapter: () -> Unit,
    private val onColorSelected: OnItemsChangedListener
) :
    RecyclerView.ViewHolder(binding.root) {
    fun bind(colorObject: com.techcubics.data.model.pojo.ColorObject) {

        Helper.customView(
            binding.colorCircle,
            Color.parseColor(colorObject.code),
            context.getColor(R.color.color_gray_2),
            borderWidth = 1
        )
        Helper.customView(
            binding.root,
            context.getColor(R.color.transparent),
            context.getColor(R.color.transparent),
            4
        )
        if (colorObject.colorID == getColor()) {
            Helper.customView(
                binding.back,
                context.getColor(R.color.white),
                context.getColor(R.color.app_color),
                4
            )
        }
        itemView.setOnClickListener {
            if (colorObject.colorID != getColor()) {
                setColor(colorObject.colorID)
                onColorSelected.onColorSelected(colorObject.colorID)
                updateAdapter()
            }
        }
    }
}