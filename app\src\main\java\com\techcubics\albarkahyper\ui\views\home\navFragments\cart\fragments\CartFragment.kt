package com.techcubics.albarkahyper.ui.views.home.navFragments.cart.fragments

import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IRefreshListListener
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.NetworkChangeReceiver
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentCartBinding
import com.techcubics.albarkahyper.ui.adapters.cart.CartProductAdapter
import com.techcubics.albarkahyper.ui.adapters.profile.LocationsAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.cart.viewmodels.CartFragmentViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.CartData
import com.techcubics.data.model.pojo.Locations
import com.techcubics.shared.constants.Constants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class CartFragment : Fragment(), IRefreshListListener {
    private val TAG = "CartFragment"
    private var _binding: FragmentCartBinding? = null
    private val binding get() = _binding!!
    private val viewModel by viewModel<CartFragmentViewModel>()
    private lateinit var cartProductAdapter: CartProductAdapter
    private lateinit var adapter: LocationsAdapter
    private var position = 0
    private var locationList: MutableList<Locations> = mutableListOf()
    private var shopId: Int = -1
    private var cartData: CartData? = null
    private val authViewModel by viewModel<AuthViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private lateinit var couponProgressButton: ProgressButton
    private lateinit var popupDialog: PopupDialog
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val mainViewModel: MainViewModel by activityViewModels()
    private var isNewLocationAdded = false
    override var location: Locations? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCartBinding.inflate(
            inflater, container, false
        )
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        couponProgressButton = ProgressButton(requireContext())
        couponProgressButton.init(binding.cartDetails.btnAddCouponCode)
        setToolbar()
        checkNetwork()
        listeners()
    }

    private fun showAlertDialog(message:String,function: () -> Unit) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setMessage(message)
            .setPositiveButton(getString(com.techcubics.style.R.string.ok)) { dialogInterface, _ ->
                dialogInterface.dismiss()
                function()
            }.setNegativeButton(getString(com.techcubics.style.R.string.no)){dialogInterface, _ ->
                dialogInterface.dismiss()
            }.setCancelable(false)
            .create()
            .show()
    }
    private fun checkNetwork() {
        if (NetworkChangeReceiver.isOnline(requireContext())) {
            binding.includeNetwork.networkView.visibility = View.GONE
            checkAuthorization()
        } else {
            binding.includeNetwork.networkView.visibility = View.VISIBLE
        }
    }

    private fun listeners() {
        binding.cartDetails.deleteCart.setOnClickListener {
            showAlertDialog(getString(com.techcubics.style.R.string.delete_warning)){
                viewModel.deleteCart()
            }
        }
        binding.includeNetwork.refresh.setOnClickListener {
            checkNetwork()
        }
        binding.cartDetails.btnBackToShopping.setOnClickListener {
            findNavController().popBackStack()
        }
        couponProgressButton.binding.textView.text =
            getString(com.techcubics.style.R.string.btn_coupon_code)
        binding.cartDetails.btnAddAddress.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            val args = Bundle()
            args.putString("fragTag", "add")
            args.putInt(Constants.SHOP_ID, shopId)
            Log.i("clothes", shopId.toString())
            findNavController().navigate(
                com.techcubics.albarkahyper.R.id.action_cartFragment_to_addAddressFragment2,
                args
            )
        }
        binding.cartDetails.btnOrderNow.setOnClickListener {
            cartData?.let { data ->
                if (data.cartDetails.any { !it.status }) {
                    popupDialog.showCartProductsNotAvailableDialog()
                } else {
                    when {
                        hasItemsExceedingMaxOrder(data) -> {
                            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.min_order_number_error))
                        }

                        isBelowMinOrderAmount(data) -> {
                            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.min_order_amount_error))
                        }

                        cartData?.location == null -> {
                            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.cart_location_required))
                        }

                        else -> {
                            val args = Bundle()
                            args.putString(Constants.INTENT_PAGE_TYPE, Constants.CART)
                            args.putString(Constants.INTENT_NOTE, binding.cartDetails.addNotes.text.toString())
                            findNavController().navigate(R.id.open_payment_methods, args)
                        }
                    }
                }
            }
        }
        binding.cartDetails.btnAddCouponCode.constraintsLayout.setOnClickListener {
            submitCoupon()
        }
        binding.cartToolbarForStandalone.mainToolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun hasItemsExceedingMaxOrder(data: CartData): Boolean {
        return data.cartDetails.any { cd ->
            ((cd.qty ?: 0) > cd.maxOrderNum) || ((cd.qty ?: 0) < cd.minOrderNum)
        }
    }

    private fun isBelowMinOrderAmount(data: CartData): Boolean {
        return (cartData?.shop?.districts?.get(0)?.minOrderPrice ?: 0f) > cartData?.totalPrice!!
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        arguments.let {
            if (it?.getString(Constants.INTENT_PAGE_TYPE) == null) {
                navbarActivity.navbarVisibility(View.VISIBLE)
            } else {
                navbarActivity.navbarVisibility(View.GONE)
            }
        }
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
    }

    private fun observers() {
        viewModel.selectedLocationResponse.observe(viewLifecycleOwner) { locResponse ->
            if (locResponse != null) {
                bottomSheetAlertDialog.showDialog(locResponse.message.toString())
                if (locResponse.status == true) {
//                    sharedPreferencesManager.saveLocationID(location?.locationId ?: 0)
//                    adapter.updateSelectedLocation(location)
                    onRefreshList()
//                    cartData?.deliveryPrice = locResponse.data?.deliveryPrice
//                    cartData?.totalPrice = locResponse.data?.totalPrice
//                    cartData?.couponPrice = locResponse.data?.couponPrice
//                    cartData?.amount = locResponse.data?.amount
//                    cartData?.location = location
//                    cartData?.let { setCartData(it) }
                } else {
                    adapter.updateSelectedLocation(adapter.getSelectedAddressLocation())
                }
                viewModel.selectedLocationResponse.value = null
            }
        }
        mainViewModel.isLocationAdded.observe(viewLifecycleOwner) {
            if (it != null) {
                isNewLocationAdded = it
                getLocationList()
                mainViewModel.removeLocationObserver()
            }
        }
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.cartLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        findNavController().navigate(R.id.carttologin)
                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }
        viewModel.listLocationResponseMutableLiveData.observe(viewLifecycleOwner) { locations ->
            if (locations != null && locations.message.toString() != Constants.SERVER_ERROR) {
                locationList = locations.data ?: mutableListOf()
                locations.data?.let { adapter.updateLocations(it) }
                viewModel.listLocationResponseMutableLiveData.value = null
                if (isNewLocationAdded) {
//                    locationList[0].locationId?.let { sharedPreferencesManager.saveLocationID(it) }
//                    adapter.notifyItemInserted(0)
                    setSelectedLocation(locationList[0])
                }
            } else if (locations?.message.toString().contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(com.techcubics.style.R.string.server_error)
                )
            }
        }

        viewModel.cartResponse.observe(viewLifecycleOwner) { cart ->
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            Helper.loadingAnimationVisibility(View.GONE, binding.actionLoadingAnimation.root)

            if (cart?.message != Constants.SERVER_ERROR) {
                if (cart?.data != null && (cart.data?.size ?: 0) > 0) {
                    binding.cartDetails.root.visibility = View.VISIBLE
                    binding.errorLayout.layout.visibility = View.GONE
                    cart.data?.get(0)?.let { cartData -> setCartData(cartData) }
                } else {
                    sharedPreferencesManager.removeLocationID()
                    binding.cartDetails.root.visibility = View.GONE
                    binding.errorLayout.layout.visibility = View.VISIBLE
                    binding.errorLayout.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.errorLayout.tvMessage.text =
                        getString(com.techcubics.style.R.string.message_empty_list_general)

                }
            } else {
                binding.errorLayout.layout.visibility = View.VISIBLE
                binding.errorLayout.tvMessage.text =
                    getString(com.techcubics.style.R.string.server_error)
                binding.errorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                binding.cartDetails.root.visibility = View.GONE
            }
        }
        viewModel.couponResponse.observe(viewLifecycleOwner) { coupon ->

            if (coupon != null && coupon.message.toString() != Constants.SERVER_ERROR) {
                if (coupon.status!!) {
                    couponProgressButton.btnFinishedSuccessfully(
                        getString(com.techcubics.style.R.string.btn_coupon_code),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(coupon.message.toString())
                } else {
                    couponProgressButton.btnFinishedFailed(
                        getString(com.techcubics.style.R.string.btn_coupon_code),
                        null
                    )
                    bottomSheetAlertDialog.showDialog(coupon.message.toString())
                }
                this.onRefreshList()
                viewModel.couponResponse.value = null
            } else if (coupon?.message.toString().contains(Constants.SERVER_ERROR)) {
                couponProgressButton.btnFinishedFailed(
                    getString(com.techcubics.style.R.string.btn_coupon_code),
                    null
                )
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(com.techcubics.style.R.string.server_error)
                )
            }
        }
        profileViewModel.selectedEditLocationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                val args = Bundle()
                Log.i("edit", "observer" + it.locationId)
                args.putString("location_id", it.locationId.toString())
                args.putString("fragTag", Constants.UPDATE)
                args.putInt(Constants.SHOP_ID, shopId)

                profileViewModel.selectedEditLocationMutableLiveData.value = null
                findNavController().navigate(
                    com.techcubics.albarkahyper.R.id.action_cartFragment_to_addAddressFragment,
                    args
                )
            }

        }
        profileViewModel.selectedDeleteLocationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                showConfirmDeleteDialog(it.locationId!!, it.country?.countryID!!)
                profileViewModel.selectedDeleteLocationMutableLiveData.value = null
            }
        }
        profileViewModel.itemPositionLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                position = it
            }
        }
        profileViewModel.deleteLocationLocationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null && it.message != Constants.SERVER_ERROR) {
//                    myAddressesFragmentBinding.progressbar.visibility = View.GONE
                bottomSheetAlertDialog.showDialog(it.message!!)
                binding.cartDetails.addressRecyclerView.adapter?.notifyItemRemoved(position)
                locationList.removeAt(position)
                binding.cartDetails.addressRecyclerView.adapter?.notifyItemRangeChanged(
                    0,
                    locationList.size
                )
                profileViewModel.deleteLocationLocationMutableLiveData.value = null
            } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                Helper.ShowErrorDialog(
                    requireContext(),
                    getString(com.techcubics.style.R.string.server_error)
                )
            }
        }

    }

    private fun setSelectedLocation(location: Locations) {
        this.location = location
        isNewLocationAdded = false
        viewModel.selectLocation(location.locationId)
    }

    private fun getLocationList() {
        viewModel.listLocations(
            sharedPreferencesManager.getCountryID().toInt(),
            sharedPreferencesManager.getCountryCode()
        )
    }

    private fun showConfirmDeleteDialog(locationId: Int, countryId: Int) {
        val builder = AlertDialog.Builder(requireContext())
            .create()
        val view =
            layoutInflater.inflate(
                com.techcubics.albarkahyper.R.layout.dialog_confirm_action,
                null
            )
        val noButton = view.findViewById<Button>(com.techcubics.albarkahyper.R.id.no)
        val yesButton = view.findViewById<Button>(com.techcubics.albarkahyper.R.id.yes)

        builder.setView(view)
        noButton.setOnClickListener {
            builder.dismiss()
        }
        yesButton.setOnClickListener {
//            myAddressesFragmentBinding.progressbar.visibility = View.VISIBLE
            if (sharedPreferencesManager.getLocationID() == locationId) {
                sharedPreferencesManager.removeLocationID()
            }
            profileViewModel.deleteLocation(
                locationId,
                countryId,
                sharedPreferencesManager.getCountryCode()
            )
            builder.dismiss()
        }
        builder.setCanceledOnTouchOutside(false)
        builder.show()
    }

    private fun setLocationsRecyclerView() {
        adapter = LocationsAdapter(
            requireContext(),
            listOf(),
            profileViewModel.selectedEditLocationMutableLiveData,
            profileViewModel.selectedDeleteLocationMutableLiveData,
            profileViewModel.itemPositionLiveData,
            viewModel,
            this,
            true,
            null
        )
        binding.cartDetails.addressRecyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.cartDetails.addressRecyclerView.adapter = adapter
    }

    private fun setItemsRecyclerView() {
        cartProductAdapter = CartProductAdapter(requireContext(), listOf(), viewModel, this, -1)
        binding.cartDetails.cartProductRecyclerView.layoutManager =
            LinearLayoutManager(requireContext())
        binding.cartDetails.cartProductRecyclerView.adapter = cartProductAdapter
    }

    private fun setToolbar() {
        arguments.let {
            if (it?.getString(Constants.INTENT_PAGE_TYPE) == null) {
                binding.cartToolbarForStandalone.root.visibility = View.GONE
                binding.cartToolbar.tvTitle.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                binding.cartToolbar.tvTitle.text =
                    resources.getText(com.techcubics.style.R.string.cart_title)
            } else {

                binding.cartToolbar.root.visibility = View.GONE
                binding.cartToolbarForStandalone.tvTitle.text =
                    resources.getText(com.techcubics.style.R.string.cart_title)
            }

        }


    }

    private fun enableDisableOrderNowButton(hasItemsMoreThanMaxOrder: Boolean) {
        if (hasItemsMoreThanMaxOrder) {
            binding.cartDetails.btnOrderNow.background =
                ResourcesCompat.getDrawable(
                    resources,
                    com.techcubics.style.R.drawable.cart_order_btn,
                    requireActivity().theme
                )
        } else {
            binding.cartDetails.btnOrderNow.background =
                ResourcesCompat.getDrawable(
                    resources,
                    com.techcubics.style.R.drawable.btn_ripple_normal_gray,
                    requireActivity().theme
                )
        }
    }

    private fun showHideStoreIsOpenNote(isOpen: Boolean) {
//        binding.cartDetails.btnOrderNow.isEnabled = isOpen
//        enableDisableOrderNowButton(isOpen)
        if (isOpen) {
            binding.cartDetails.storeIsOpenNote.visibility = View.GONE
//            enableDisableOrderNowButton(cartData?.location != null)
        } else {
            binding.cartDetails.storeIsOpenNote.visibility = View.VISIBLE
        }
    }

    private fun setCartData(cartData: CartData) {
        this.cartData = cartData
        if (cartData.cartDetails.any { c -> !c.status }) {
            popupDialog.showCartProductsNotAvailableDialog()
        }
//        showHideStoreIsOpenNote(cartData.shop?.isOpen == true)
        val hasItemsMoreThanMaxOrder = cartData.cartDetails.find { cd ->
            ((cd.qty ?: 0) > cd.maxOrderNum) || ((cd.qty ?: 0) < cd.minOrderNum)
        } != null
        enableDisableOrderNowButton(
            hasItemsMoreThanMaxOrder || (cartData.shop?.districts?.get(0)?.minOrderPrice
                ?: 0f) > cartData.totalPrice!! || cartData.location == null
        )
        shopId = cartData.shop?.shopID ?: -1
        cartProductAdapter.updateItems(shopId, cartData.cartDetails)

        sharedPreferencesManager.saveLocationID(cartData.location?.locationId)
        adapter.updateSelectedLocation(cartData.location)
        val quantity = cartData.cartDetails.sumOf { c -> c.qty ?: 0 }
        val orderSummeryNote =
            getString(com.techcubics.style.R.string.order_summery_note, quantity.toString())
        binding.cartDetails.ordersSummeryNote.text = orderSummeryNote
        val deliveryPrice = cartData.deliveryPrice
        val totalPriceBeforeDeduction = cartData.totalPrice
        "$totalPriceBeforeDeduction ${getString(com.techcubics.style.R.string.currency_name)}"
            .also { binding.cartDetails.productsTotalAmount.text = it }
        val couponPrice = cartData.couponPrice ?: 0f
        val instantDiscount = cartData.rangeDiscountAmount ?: 0f
        val deductionStr = "$couponPrice ${getString(com.techcubics.style.R.string.currency_name)}"
        val couponNote = getString(com.techcubics.style.R.string.deduction, deductionStr)
        binding.cartDetails.couponNote.text = couponNote
        val totalAmountAfterDeduction = cartData.amount
        "$totalAmountAfterDeduction ${getString(com.techcubics.style.R.string.currency_name)}"
            .also { binding.cartDetails.totalAmount.text = it }
        "$deliveryPrice ${getString(com.techcubics.style.R.string.currency_name)}"
            .also { binding.cartDetails.totalShipping.text = it }
        "$deliveryPrice ${getString(com.techcubics.style.R.string.currency_name)}"
            .also { binding.cartDetails.deliveryFees.text = it }
        "$couponPrice ${getString(com.techcubics.style.R.string.currency_name)}"
            .also { binding.cartDetails.couponAmount.text = it }
        "$instantDiscount ${getString(com.techcubics.style.R.string.currency_name)}"
            .also { binding.cartDetails.instantDiscount.text = it }
    }

    private fun submitCoupon() {
        if (binding.cartDetails.couponCode.text.toString().isNotEmpty()) {
            couponProgressButton.btnActivated()
            viewModel.submitCoupon(
                binding.cartDetails.couponCode.text.toString()
            )
        } else {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.coupon_code_is_missing))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    override fun onRefreshList() {
        showLoading()
        viewModel.getCart()
    }

    private fun showLoading() {
        lifecycleScope.launch(Dispatchers.Main) {
            _binding?.actionLoadingAnimation?.root?.let {
                Helper.loadingAnimationVisibility(
                    View.VISIBLE,
                    it
                )
            }
        }
    }

    private fun checkAuthorization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
            Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
            getLocationList()
            setItemsRecyclerView()
            viewModel.getCart()
            setLocationsRecyclerView()
            observers()
        } else {
            binding.errorLayout.layout.visibility = View.VISIBLE
            binding.errorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
            binding.errorLayout.tvMessage.text =
                getString(com.techcubics.style.R.string.message_error_loading_login_required)
        }
    }


}

