package com.techcubics.albarkahyper.ui.views.products.searchResult

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Html
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.AbsListView.OnScrollListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SnapHelper
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IFavClickListener
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.common.IPageBehaviour
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.albarkahyper.common.Slider
import com.techcubics.albarkahyper.databinding.FragmentMainCategoriesBinding
import com.techcubics.albarkahyper.ui.adapters.product.BannersAdaper
import com.techcubics.albarkahyper.ui.adapters.product.MainCategoryAdatper
import com.techcubics.albarkahyper.ui.adapters.store.ProductsAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.BannerData
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.ProductDetailsDto
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import com.techcubics.style.R
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.Locale


class MainCategoriesFragment : Fragment(), IPageBehaviour, IOnAdapterItemClickHandler,
    IFavClickListener {

    private lateinit var binding: FragmentMainCategoriesBinding
    private lateinit var bannersAdapter: BannersAdaper
    private lateinit var mainCategoryAdapter: MainCategoryAdatper
    private val productsViewModel: ProductsViewModel by viewModel<ProductsViewModel>()

    private val TAG = "BranchTypeMainCategoriesFragment"
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    private var bannersSlider: Slider? = null
    private var parentID: Int? = null
    private var shopID: Int? = null
    private lateinit var name: String

    private lateinit var discountsResultList: ArrayList<ProductDetailsDto>
    lateinit var discountsAdapter: ProductsAdapter<ProductDetailsDto>
    private var totalPrice = 0f
    private var _operation: Int = -1
    private var _position: Int? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentMainCategoriesBinding.inflate(inflater, container, false)

        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {
        discountsResultList = arrayListOf()
        setDiscountsAdapter()
        productsViewModel.getBanners()
        //
        arguments.let {
            binding.toolbarMain.tvTitle.text = it?.getString(Constants.INTENT_NAME)
            parentID = it?.getInt(Constants.INTENT_TYPE_ID)!!
            shopID = it.getInt(Constants.INTENT_PLACE_ID)
            minOrder = it.getFloat(Constants.MIN_ORDER)
            name = it.getString(Constants.INTENT_NAME, "")
            binding.toolbarMain.tvTitle.text = name
            productsViewModel.getMainCategoriesByShopId(shopID!!)

        }
        //
        binding.toolbarSearch.btnFilter.visibility = View.GONE
        productsViewModel.getCart()
        calcPrice(minOrder, totalPrice)
        binding.rvBanners.visibility=View.GONE
        binding.indicator.visibility=View.GONE
        binding.offerLayout.visibility=View.GONE

    }

    override fun observers() {

        productsViewModel.getCartResponse.observe(viewLifecycleOwner) { cartRes ->
            if (cartRes != null) {
                if (cartRes.status == true && cartRes.data != null && cartRes.data?.size!! > 0) {
                    totalPrice = cartRes.data!![0].totalPrice!!
                    calcPrice(minOrder, totalPrice)
                }
                productsViewModel.getCartResponse.value = null
            }
        }
        productsViewModel.bannersResponse.observe(viewLifecycleOwner, Observer {


            try {
                if (it.status!!) {

                    if (it.data != null) {

                        if(it.data!!.size>0) {
                            binding.rvBanners.visibility=View.VISIBLE
                            showBanners(it.data!!)

                        }else{
                            binding.rvBanners.visibility=View.GONE
                        }

                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })
        productsViewModel.mainCategoriesResponse.observe(viewLifecycleOwner, Observer {


            try {
                if (it.status!!) {

                    if (it.data != null) {

                        if (!it.data!!.isEmpty()) {
                            showMainCategories(it.data!!)
                            productsViewModel.getStoreDiscounts(shopID!!, page = 1)


                        } else {

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })


        productsViewModel.storeDiscountsResponse.observe(viewLifecycleOwner, Observer {
            binding.horizontalLoading.visibility=View.GONE

            try {
                if (it?.status!!) {

                    if (it.data != null) {


                        if (it.data!!.size > 0) {
                            showStoreDiscounts(it.data!!)
                            binding.includeQtyCartInfo.root.visibility = View.VISIBLE
                            binding.offerLayout.visibility=View.VISIBLE
                        }


                    } else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {
                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })
        productsViewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner, Observer {

            try {

                if (it.status!!) {

                    when (_operation) {
                        1 -> discountsResultList[_position!!].isFav = false
                        2 -> discountsResultList[_position!!].isFav = true
                    }
                    // productsAdapter.notifyDataSetChanged()

                }

            } catch (ex: Exception) {

            }


        })

        productsViewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null) {
                val offset = if (qty == minQty) qty else 1
                val p = ((totalPrice * 100) - (offset * (itemPrice * 100))) / 100
                totalPrice = p
                if (it.message.toString() != Constants.SERVER_ERROR) {
                    if (it.status == true) {
                        totalPrice = it.data?.totalPrice ?: 0f
                    } else {
                        discountsAdapter.notifyItemChanged(itemPosition)
                        bottomSheetAlertDialog.showDialog(it.message.toString())
                    }
                } else if (it.message.toString().contains(Constants.SERVER_ERROR)) {
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                calcPrice(minOrder, totalPrice)
                productsViewModel.addCartResponse.value = null
            }
        }
        productsViewModel.cartItemRemovedResponse.observe(context as LifecycleOwner) { cart ->
            if (cart != null) {
                if (!cart.status!!) {
                    val p = ((totalPrice * 100) + (qty * (itemPrice * 100))) / 100
                    totalPrice = p
                    bottomSheetAlertDialog.showDialog(cart.message.toString())
                } else {
                    totalPrice = cart.data?.totalPrice ?: 0f
                    if (discountsResultList[itemPosition].isDiscount==true){
                        discountsResultList[itemPosition].discount?.get(0)?.quantityCart = 0
                    }else{
                        discountsResultList[itemPosition].qtyCart = 0
                    }
                }
                discountsAdapter.items = discountsResultList
                discountsAdapter.notifyItemChanged(itemPosition)
                productsViewModel.cartItemRemovedResponse.value = null
            }
            calcPrice(minOrder, totalPrice)
        }
    }

    private var rvListener: RecyclerView.OnScrollListener? = null
    private fun setDiscountsAdapter() {
        discountsAdapter = ProductsAdapter(
            onFavClickListener = this,
            onClickHandler = this,
            isHorizontal = true,
            context = requireContext()
        )
        discountsAdapter.setItemsList(discountsResultList, minOrder)
        binding.rvDiscounts.adapter = discountsAdapter
        val manager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.rvDiscounts.layoutManager = manager
        rvListener = object : RecyclerView.OnScrollListener(){
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()
                val totalItemCount = layoutManager.itemCount
                if (lastVisibleItemPosition == totalItemCount - 1) {
                    val hasMorePage = productsViewModel.storeDiscountsResponse.value?.pagingator?.hasMorePages
                    if (hasMorePage==true) {
                        binding.horizontalLoading.visibility=View.VISIBLE
                        productsViewModel.getStoreDiscounts(
                            shopID!!,
                            productsViewModel.storeDiscountsResponse.value?.pagingator?.currentPage!! + 1
                        )
                    }
                }
            }
        }
        rvListener?.let{binding.rvDiscounts.addOnScrollListener(it)}
    }
    @SuppressLint("NotifyDataSetChanged")
    private fun showStoreDiscounts(data: MutableList<ProductDetailsDto>) {
        discountsResultList = data as ArrayList<ProductDetailsDto>
        discountsAdapter.setItemsList(discountsResultList, minOrder)
        discountsAdapter.notifyDataSetChanged()
    }

    private fun showMainCategories(data: MutableList<Category>) {

        mainCategoryAdapter = MainCategoryAdatper(object : IOnAdapterItemClickHandler {
            override fun onItemClicked(itemId: Int?, type: String) {
                val bundle = Bundle()
                bundle.putInt(Constants.INTENT_ID, itemId!!)
                bundle.putString(Constants.INTENT_NAME, type)
                bundle.putInt(Constants.INTENT_TYPE_ID, parentID!!)
                bundle.putInt(Constants.INTENT_PLACE_ID, shopID!!)
                bundle.putFloat(Constants.MIN_ORDER, minOrder)


                findNavController().navigate(
                    com.techcubics.albarkahyper.R.id.action_mainCategoriesFragment_to_productSearchCategoryResultFragment,
                    bundle
                )
            }
        })
        mainCategoryAdapter.items = data
        binding.rvCategories.adapter = mainCategoryAdapter
        binding.rvCategories.layoutManager = GridLayoutManager(requireContext(), 3)

    }

    private fun showBanners(data: MutableList<BannerData>) {

        bannersAdapter = BannersAdaper()
        bannersAdapter.items = data
        binding.rvBanners.adapter = bannersAdapter
        //////////////////
        var manager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

        val snapHelper: SnapHelper = PagerSnapHelper()
        binding.rvBanners.setLayoutManager(manager)
        binding.rvBanners.setOnFlingListener(null)
        snapHelper.attachToRecyclerView(binding.rvBanners)
        binding.indicator.visibility=View.VISIBLE
        //////////////
        if (data.size > 1) {
            bannersSlider = Slider(binding.rvBanners, 3000)
            bannersSlider?.setAdapter(bannersAdapter)
            bannersSlider?.start()
            bannersSlider?.setSnapHelper()

        }
    }

    override fun events() {

        binding.toolbarMain.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }

        binding.toolbarSearch.txtSearchWord.setOnEditorActionListener { v, actionId, event ->
            when (actionId) {
                EditorInfo.IME_ACTION_SEARCH -> {
                    if (v.text.isNotBlank() && v.text.isNotEmpty()) {
                        moveToSearch(v.text.toString().trim())

                        true
                    } else {
                        false
                    }
                }

                else -> false
            }
        }

        binding.includeQtyCartInfo.openCartBtn.setOnClickListener {
            val b = Bundle()
            b.putString(Constants.INTENT_PAGE_TYPE, "store_details")

            findNavController().navigate(com.techcubics.albarkahyper.R.id.view_cart, b)
        }

    }

    private fun moveToSearch(word: String) {
        val bundle = Bundle()
        bundle.putString(Constants.INTENT_WORD, word)
        bundle.putInt(Constants.INTENT_ID, shopID!!)
        bundle.putFloat(Constants.MIN_ORDER, minOrder)
        bundle.putString(Constants.INTENT_NAME, name)
        bundle.putString(
            Constants.INTENT_NAME,
            getString(R.string.section_search_hint)
        )
        findNavController().navigate(
            com.techcubics.albarkahyper.R.id.view_generalStoreSearch,
            bundle
        )
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    override fun onDestroy() {
        super.onDestroy()
        bannersSlider?.stop()
        rvListener?.let{binding.rvDiscounts.removeOnScrollListener(it)}
    }

    private fun startStopSlider(modelType: String? = null, isStart: Boolean) {
        if (!isStart) {
            bannersSlider?.pause()
        } else {
            bannersSlider?.start()

        }
    }

    override fun onFavClick(parent: Int, position: Int, operation: Int) {

        _operation = operation
        _position = position
        productsViewModel.storeDiscountsResponse.value?.data!![position].id?.let {
            productsViewModel.addRemoveFav(
                it
            )
        }


    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle = Bundle()
        bundle.putInt(Constants.INTENT_ID, itemId ?: -1)
        findNavController().navigate(com.techcubics.albarkahyper.R.id.view_productDetails, bundle)

    }


    private var qty = 0
    private var maxQty = 0
    private var minQty = 0
    private var itemPrice = 0f
    private var minOrder = 0f
    private var itemPosition = -1

    override fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
        this.qty = qty ?: 0
        this.itemPrice = itemPrice
        this.maxQty = maxQty
        this.minQty = minQty
        this.itemPosition = itemPosition
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            val offset = if (qty == minQty) qty else 1
            val p = ((totalPrice * 100) + (offset * (itemPrice * 100))) / 100
            totalPrice = p
            calcPrice(this.minOrder, totalPrice)
            productsViewModel.addToCart(pathEndPoint, shopID, modelType, modelId, qty)

        } else {
            findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
        }

    }

    override fun removeItemFromCart(
        modelType: String?,
        modelId: Int?,
        productPrice: Float,
        itemPosition: Int,
    ) {
        this.itemPosition = itemPosition
        this.itemPrice = productPrice
        val p = ((totalPrice * 100) - (qty * (productPrice * 100))) / 100
        totalPrice = p
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            calcPrice(this.minOrder, totalPrice)
            productsViewModel.removeCartItem(modelType, modelId)

        } else {
            findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
        }
    }

    private fun calcPrice(minOrder: Float, totalPrice: Float) {
        binding.includeQtyCartInfo.progressBar.max = 100
        binding.includeQtyCartInfo.progressBar.progress = if (minOrder > 0) {
            (((totalPrice * 100) / (minOrder * 100)) * 100).toInt()
        } else {
            0
        }
        //min order
        val numFormatMinOrder =
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(minOrder) + "&#8200;" +
                    getString(com.techcubics.style.R.string.currency_name)
        val minOrderStr = getString(com.techcubics.style.R.string.min_order, numFormatMinOrder)
        val minOrderStyledText: Spanned = Html.fromHtml(minOrderStr, Html.FROM_HTML_MODE_LEGACY)
        binding.includeQtyCartInfo.minOrder.text = minOrderStyledText
        //total amount
        val numFormatPrice = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH)
            .format(totalPrice) + "&#8200;" +
                getString(com.techcubics.style.R.string.currency_name)
        val totalAmountStr =
            getString(com.techcubics.style.R.string.product_order_total_amount, numFormatPrice)
        val totalAmountStyledText: Spanned = Html.fromHtml(
            totalAmountStr,
            Html.FROM_HTML_MODE_LEGACY
        )
        binding.includeQtyCartInfo.totalAmount.text = totalAmountStyledText

    }

}