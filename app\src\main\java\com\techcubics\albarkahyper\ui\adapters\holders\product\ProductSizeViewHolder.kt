package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemSizeBinding
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.data.model.pojo.Size

class ProductSizeViewHolder(
    val binding: ItemSizeBinding,
    val context: Context,
    var onSizeSelected: OnItemsChangedListener,
    val setSize: (Int) -> Unit,
    val getSize: () -> Int,
    val updateList: () -> Unit
) : RecyclerView.ViewHolder(binding.root) {
    fun bind(size: Size) {
        binding.size.text = size.name
        binding.size.isChecked = getSize() == size.sizeID
        binding.size.setOnClickListener {
            if (size.sizeID != getSize()) {
                setSize(size.sizeID)
                onSizeSelected.onSizeSelected(getSize(),bindingAdapterPosition)
                updateList()
            }
        }
    }
}