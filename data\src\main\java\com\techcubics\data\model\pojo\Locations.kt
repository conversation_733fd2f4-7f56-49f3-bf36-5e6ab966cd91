package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class Locations (

    @SerializedName("id"               ) var id              : Int?         = null,
    @SerializedName("location_id"      ) var locationId      : Int?         = null,
    @SerializedName("first_name"       ) var firstName       : String?      = null,
    @SerializedName("last_name"        ) var lastName        : String?      = null,
    @SerializedName("full_name"        ) var fullName        : String?      = null,
    @SerializedName("phone"            ) var phone           : String?      = null,
    @SerializedName("phone_verified"   ) var phoneVerified   : String?      = null,
    @SerializedName("lat"              ) var lat             : Double?      = null,
    @SerializedName("lng"              ) var lng             : Double?      = null,
    @SerializedName("address"          ) var address         : String?      = null,
    @SerializedName("country"          ) var country         : Country?     = null,
    @SerializedName("governorate"      ) var governorate     : Governerate? = null,
    @SerializedName("region"           ) var region          : Regoin?      = null,
    @SerializedName("building_type"    ) var buildingType    : String?      = null,
    @SerializedName("street"           ) var street          : String?      = null,
    @SerializedName("building_number"  ) var buildingNumber  : String?      = null,
    @SerializedName("floor_no"         ) var floorNo         : String?      = null,
    @SerializedName("apartment_number" ) var apartmentNumber : String?      = null,
    @SerializedName("notes"            ) var notes           : String?      = null

)