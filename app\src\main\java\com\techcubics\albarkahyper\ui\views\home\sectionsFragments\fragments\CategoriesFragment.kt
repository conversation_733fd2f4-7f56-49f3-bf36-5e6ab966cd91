package com.techcubics.albarkahyper.ui.views.home.sectionsFragments.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.techcubics.data.model.pojo.Category
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IPageRowset
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.StoreCategoryOnItemClickListener
import com.techcubics.albarkahyper.databinding.FragmentCategoriesBinding
import com.techcubics.albarkahyper.ui.adapters.home.CategoriesAdapter
import com.techcubics.albarkahyper.ui.views.home.sectionsFragments.viewmodels.SectionsViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import org.koin.androidx.viewmodel.ext.android.viewModel

class CategoriesFragment : Fragment() , IPageRowset<Category> {

    private  lateinit var binding: FragmentCategoriesBinding
    private lateinit var categorysAdapter: CategoriesAdapter<Category>
    private  val categorysFragmentViewModel: SectionsViewModel by viewModel<SectionsViewModel>()
    private lateinit var onCategoryListener: StoreCategoryOnItemClickListener

    private  val TAG = "CategoriesFragment"
//    companion object {
//        val instance: CategoriesFragment = CategoriesFragment()
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        setStyle(DialogFragment.STYLE_NORMAL, com.techcubics.style.R.style.bottomsheet_style)
//
//    }
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding= FragmentCategoriesBinding.inflate(inflater,container,false)
//        dialog?.setOnShowListener {
//            val d: BottomSheetDialog = dialog as BottomSheetDialog
//            val bottomSheetInternal: View = d.findViewById(com.google.android.material.R.id.design_bottom_sheet)!!
//            BottomSheetBehavior.from(bottomSheetInternal).setState(BottomSheetBehavior.STATE_EXPANDED)
//        }



        init()
        listeners()
        observers()
        events()
        return binding.root
    }

    override fun init() {

        binding.toolbarCategories.tvTitle.text=resources.getText(com.techcubics.style.R.string.section_categories_title)
        categorysFragmentViewModel.getCategories()
        //
        showHidePlaceHolder(show=false,type= null,message=null)
        binding.rvCategories.visibility=View.INVISIBLE
        Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
    }

    private fun listeners() {
        onCategoryListener = object : StoreCategoryOnItemClickListener {
            override fun <T> onCategoryClick(item: T) {
                val category = item as Category
                val bundle = Bundle()
                bundle.putInt(Constants.INTENT_ID, category.id!!)
                bundle.putString(Constants.INTENT_NAME, category.name)
                findNavController().navigate(com.techcubics.albarkahyper.R.id.display_products_by_category,bundle)
            }

        }
    }

    override  fun observers(){

        categorysFragmentViewModel.categoriesResponse.observe(this, Observer {
            Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            try {
                if (it.status!!) {

                    if(it.data!=null){

                        if (!it.data!!.isEmpty()) {
                            showData(it.data!!)
                            binding.rvCategories.visibility = View.VISIBLE

                        }else{

                            showHidePlaceHolder(
                                show = true,
                                type = LottieIconEnum.Empty,
                                message = getString(com.techcubics.style.R.string.message_empty_list_general)
                            )
                        }

                    }else {
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = it.message
                        )

                    }

                } else {

                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {
                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })



    }
    override fun events(){

        binding.toolbarCategories.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
    }
    override fun showData( items:List<Category>){

        categorysAdapter = CategoriesAdapter(onCategoryListener)
        categorysAdapter.setItemsList(items)
        binding.rvCategories.adapter = categorysAdapter
        binding.rvCategories.layoutManager =
            GridLayoutManager(context, 3)

    }
    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    binding.placeholder.tvMessage.text = message
                } else -> throw IllegalStateException("error")

            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }
    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }


}