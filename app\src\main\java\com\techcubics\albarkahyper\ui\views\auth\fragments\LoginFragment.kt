package com.techcubics.albarkahyper.ui.views.auth.fragments

import android.os.Bundle
import android.util.Log
import android.view.*
import androidx.activity.OnBackPressedCallback
import androidx.core.view.GravityCompat
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.FragmentLoginBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.requests.auth.LoginRequest
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.PagesEnum
import com.techcubics.shared.utils.AuthUtils
import com.techcubics.shared.utils.AuthUtils.Companion.isNumeric
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import kotlinx.coroutines.*
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class LoginFragment : Fragment() {

    companion object {
        private const val SIGN_IN_RESULT_CODE = 123
    }

    private var checkPhoneOrMail = false
    private lateinit var loginBinding: FragmentLoginBinding
    private lateinit var signinProgressBtn: ProgressButton
//    private lateinit var fbSigninProgressBtn: ProgressButton
//    private lateinit var googleProgressBinding: ProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val loginViewModel by viewModel<AuthViewModel>()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private var googleClicked = false
    private lateinit var popupDialog: PopupDialog



    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Helper.exitFullScreen(requireContext())

        loginBinding = FragmentLoginBinding.inflate(inflater, container, false)

        signinProgressBtn = ProgressButton(requireContext())
        signinProgressBtn.init(loginBinding.signBtnProgress)
        initViews()
        viewObserver()
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        return loginBinding.root
    }

    private fun initViews() {

        signinProgressBtn.binding.textView.text = getString(com.techcubics.style.R.string.signin)

        if(sharedPreferencesManager.getLanguage().equals("ar")){
            loginBinding.passwordEdt.gravity = GravityCompat.START
            loginBinding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            loginBinding.emailOrPhoneEdt.gravity = GravityCompat.END
            loginBinding.emailOrPhoneEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_END

        }
        loginBinding.termsConditionsLayout.termsConditionsCheckbox.isChecked = sharedPreferencesManager.isTermsAccepted()
        loginBinding.passwordEdt.addTextChangedListener{
            if(sharedPreferencesManager.getLanguage().equals("ar")) {
                loginBinding.passwordEdt.gravity = GravityCompat.START
                loginBinding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            }else{
                loginBinding.passwordEdt.gravity = GravityCompat.START
                loginBinding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            }
        }
        activity?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                requireActivity().finish()
            }
        })
        loginBinding.countryPicker.setDefaultCountryUsingNameCode(sharedPreferencesManager.getCountryCode())
        loginBinding.countryPicker.resetToDefaultCountry()

        loginBinding.termsConditionsLayout.termsConditionsCheckbox.setOnCheckedChangeListener { _, b ->
            sharedPreferencesManager.setTerms(b)
        }
        loginBinding.termsConditionsLayout.termsConditionsTextview.setOnClickListener {
            Helper.loadingAnimationVisibility(View.VISIBLE,loginBinding.actionLoadingAnimation.root)
            profileViewModel.pagecall(PagesEnum.TermsCondition.value)
        }

        loginBinding.signBtnProgress.constraintsLayout.setOnClickListener {
            if (checkUiValidity()) {
                if (chechTermsAndConditions()) {
                    signinProgressBtn.btnActivated()
                    sharedPreferencesManager.convertCountryCode(loginBinding.countryPicker.selectedCountryName)
                    Log.i("clothes", "from login " + loginBinding.countryPicker.selectedCountryName)
                    sharedPreferencesManager.saveMobileCode(loginBinding.countryPicker.selectedCountryCodeWithPlus)
                    Log.i(
                        "here",
                        "mobile code : " + sharedPreferencesManager.getMobileCode() + " " + sharedPreferencesManager.getCountryCode()
                    )
                    loginViewModel.login(
                        LoginRequest(
                            loginBinding.emailOrPhoneEdt.text.toString(),
                            loginBinding.passwordEdt.text.toString(),
                            sharedPreferencesManager.getCountryCode()
                        )
                    )

                    sharedPreferencesManager.saveName(loginBinding.emailOrPhoneEdt.text.toString())
                    sharedPreferencesManager.savePassword(loginBinding.passwordEdt.text.toString())
                    sharedPreferencesManager.saveMobileCodeWithoutPlus(loginBinding.countryPicker.selectedCountryCode)
                }
            }
        }

//        loginBinding.forgetPassword.setOnClickListener {
//            findNavController().navigate(R.id.action_loginFragment_to_forgetPasswordFragment)
//        }

        loginBinding.navtoSignupBtn.setOnClickListener {
           findNavController().navigate(R.id.action_loginFragment_to_signUpFragment)
        }


    }


    private fun viewObserver() {
        loginViewModel.loginMutableLiveData.observe(viewLifecycleOwner){
            if(it != null){
                if (it.status!!) {
                    sharedPreferencesManager.loggedIn("true")
                    it.data?.let {  it1 ->
                        it1.facilityName?.let { it2 -> sharedPreferencesManager.saveShopName(it2) }
                        sharedPreferencesManager.saveName(it1.name)
                        sharedPreferencesManager.saveEmail(it1.phone)
                        sharedPreferencesManager.saveObject(Constants.USER, it1)
                        sharedPreferencesManager.saveObject(Constants.CHOOSEN_LATLNG,
                            it1.lat?.let { it2 -> it1.lng?.let { it3 -> Latlng(it2, it3) } })
                        sharedPreferencesManager.saveUserPhoto(it1.avatar!!)
                    }
                    signinProgressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.signin),null)
                    CoroutineScope(Dispatchers.Main).launch {
                        bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.signin_succeeded))
                        delay(1500)
                        bottomSheetAlertDialog.onDismiss()
                        findNavController().apply {
                            navigate(R.id.action_loginFragment_to_homeFragment)
                        }
                    }
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    signinProgressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.signin),null)
                    Helper.ShowErrorDialog(requireContext(),getString(com.techcubics.style.R.string.server_error))
                }
                else {
                    signinProgressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.signin),null)
                    sharedPreferencesManager.loggedIn("false")
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                loginViewModel.loginMutableLiveData.value = null
            }

        }

        profileViewModel.pageCallMutableLiveData.observe(viewLifecycleOwner){
            Helper.loadingAnimationVisibility(View.GONE,loginBinding.actionLoadingAnimation.root)
            if(it != null){
                if (!it.data?.description.isNullOrEmpty()) {
                   popupDialog.showTermsAndConditionsDialog(requireContext(),
                       it.data?.description!!,
                       sharedPreferencesManager,
                       loginBinding.termsConditionsLayout.termsConditionsCheckbox)
                }
                profileViewModel.pageCallMutableLiveData.value = null
            }

        }
    }

    private fun checkUiValidity(): Boolean {

        var check = true
        if (isNumeric(loginBinding.emailOrPhoneEdt.text.toString())) {
            checkPhoneOrMail = true
            if (loginBinding.emailOrPhoneEdt.text.isNullOrEmpty() || !(AuthUtils.validatePhone(
                    loginBinding.emailOrPhoneEdt.text.toString(),
                    loginBinding.countryPicker.selectedCountryCode
                ))
            ) {
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.phone_not_correct))
                check = false
            }
        } else if (!isNumeric(loginBinding.emailOrPhoneEdt.text.toString())) {
            checkPhoneOrMail = false
            if (!(AuthUtils.validateEmail(loginBinding.emailOrPhoneEdt.text.toString()))
                || loginBinding.emailOrPhoneEdt.text.isNullOrEmpty()
            ) {
                bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.check_your_email_and_try_again))
                check = false
            }
        } else if (loginBinding.passwordEdt.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.check_your_password_and_try_again))
            check = false
        }

        return check
    }

    private fun chechTermsAndConditions() : Boolean{
        var check = true
        if( ! loginBinding.termsConditionsLayout.termsConditionsCheckbox.isChecked){
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.accept_terms_and_conditions))
            check = false
        }
        return check
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}