package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.callus

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.requests.profile.ContactusRequest
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.databinding.FragmentCallUsBinding
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants.furniture
import com.techcubics.shared.utils.AuthUtils
import com.techcubics.style.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class CallUsFragment : Fragment() {


    private lateinit var binding: FragmentCallUsBinding
    private lateinit var progressBtn: ProgressButton
    private val profileViewModel by viewModel<ProfileViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private lateinit var popupDialog: PopupDialog
    private lateinit var bottomSheetAlertDialog : BottomSheetAlertDialog

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        binding = FragmentCallUsBinding.inflate(layoutInflater, container, false)
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        progressBtn = ProgressButton(requireContext())
        progressBtn.init(binding.signBtnProgress)
        initViews()
        observeViews()
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())

        return binding.root
    }

    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner){

            if(it != null){
                if (it.message!!.contains(getString(R.string.unauthenticated))) {
                    CoroutineScope(Dispatchers.Main).launch {
                        binding.callusLayout.visibility = View.GONE
                        popupDialog.showSessionExpiredDialog(requireContext())
                        delay(1200)
                        popupDialog.onDismiss()
                        Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                    }
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    Helper.ShowErrorDialog(requireContext(),getString(R.string.server_error))
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }
        }
        profileViewModel.callusMutableLiveData.observe(viewLifecycleOwner) {
            if(it != null){
                if (it.status!!) {
                    progressBtn.btnFinishedSuccessfully(getString(R.string.send),null)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                else if(it.message!!.contains(Constants.SERVER_ERROR)){
                    progressBtn.btnFinishedFailed(getString(R.string.send),null)
                    Helper.ShowErrorDialog(requireContext(),getString(R.string.server_error))
                }
                else {
                    progressBtn.btnFinishedFailed(getString(R.string.send),null)
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
            }
        }

    }

    private fun initViews() {
        checkAuthorization()
        binding.toolbar.tvTitle.text = getString(R.string.call_us)
        progressBtn.binding.textView.text = getString(R.string.send)
        binding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }

        binding.countryPicker.setDefaultCountryUsingNameCode(sharedPreferencesManager.getCountryCode())
        binding.countryPicker.resetToDefaultCountry()
        binding.countryPicker.setCcpClickable(false)

        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            if (checkUiValidity()) {
                progressBtn.btnActivated()
                profileViewModel.callus(
                    ContactusRequest(
                        binding.firstName.text.toString(),
                        binding.lastName.text.toString(),
                        binding.subject.text.toString(),
                        binding.phoneEdt.text.toString(),
                        binding.additionalNotes.text.toString(),
                        binding.email.text.toString()
                    )
                )
            }
        }

    }


    private fun checkUiValidity(): Boolean {

        var check = true

        if (binding.phoneEdt.text.isNullOrEmpty() || !(AuthUtils.validatePhone(
                binding.phoneEdt.text.toString(),
                binding.countryPicker.selectedCountryCode
            ))
        ) {
            bottomSheetAlertDialog.showDialog(getString(R.string.phone_not_correct))
            check = false
        } else if (binding.firstName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_check_first_name))
            check = false
        } else if (binding.lastName.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_check_last_name))
            check = false
        } else if (binding.additionalNotes.text.isNullOrEmpty()) {
            bottomSheetAlertDialog.showDialog(getString(R.string.please_Check_notes))
            check = false
        } else if (!(AuthUtils.validateEmail(binding.subject.text.toString()))
            || binding.subject.text.isNullOrEmpty()
        ) {
            bottomSheetAlertDialog.showDialog(getString(R.string.check_your_email_and_try_again))
            check = false
        }


        return check
    }

    private fun checkAuthorization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }
}