package com.techcubics.albarkahyper.ui.adapters.holders.order

import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemOrderBinding

class OrdersHolderItem(itemView: ItemOrderBinding) : RecyclerView.ViewHolder(itemView.root) {
        val productName = itemView.productName
        val orderno = itemView.orderNo
        val ordersliderimg = itemView.imageSlider
        val total = itemView.amount
        val status = itemView.orderStatus
        val currencyName = itemView.currencyName
        val imageView = itemView.imageView
    }