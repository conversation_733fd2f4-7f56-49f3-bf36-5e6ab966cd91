package com.techcubics.albarkahyper.ui.views.home.navFragments.favorites.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.CartData
import com.techcubics.data.model.pojo.FavouriteProduct
import com.techcubics.data.model.pojo.FavouriteStore
import com.techcubics.data.model.requests.AddCartRequest
import com.techcubics.data.model.requests.ItemRemovingRequest
import com.techcubics.data.model.requests.home.AddRemoveFavoriteRequest
import com.techcubics.data.model.requests.home.MyFavoirtesRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.favourite.FavoritesRepo
import com.techcubics.data.repos.home.favourite.FavoritesRepoImpl
import com.techcubics.data.repos.product.ProductsRepo
import com.techcubics.data.repos.product.ProductsRepoImpl
import com.techcubics.shared.enums.FavoriteTypesEnum
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class FavoritesViewModel(private val favResponse: FavoritesRepo,private val productsRepo: ProductsRepo,private val sharedPreferencesManager:SharedPreferencesManager) : ViewModel() {

    val productsResponse = MutableLiveData<BaseResponse<MutableList<FavouriteProduct>>>()
    val storesResponse = MutableLiveData<BaseResponse<MutableList<FavouriteStore>>>()
    val addRemoveFavoriteResponse = MutableLiveData<BaseResponse<String>>()
    val addCartResponse: MutableLiveData<BaseResponse<CartData>> by lazy {
        MutableLiveData()
    }


    fun getProducts(page: Int) {
        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request = MyFavoirtesRequest(
                country_id = countryID,
                type = FavoriteTypesEnum.Product,
                page = page
            )
            val rs = favResponse.getMyFavoirteProducts(request)
            productsResponse.postValue(rs!!)

        }

    }

    fun getStores(page: Int) {
        val countryID: Int = sharedPreferencesManager.getCountryID().toInt()

        viewModelScope.launch {
            val request = MyFavoirtesRequest(
                country_id = countryID,
                type = FavoriteTypesEnum.Furniture,
                page = page
            )
            val rs = favResponse.getMyFavoirteStores(request)
            storesResponse.postValue(rs!!)

        }

    }

    fun addRemoveProductFav(id: Int) {
        viewModelScope.launch {
            val request = AddRemoveFavoriteRequest(type = FavoriteTypesEnum.Product.value, id = id)
            val rs = favResponse.addRemoveFav(request)
            addRemoveFavoriteResponse.postValue(rs!!)
        }

    }

    fun addRemoveStoreFav(id: Int) {
        viewModelScope.launch {
            val request = AddRemoveFavoriteRequest(type = FavoriteTypesEnum.Furniture.value, id = id)
            val rs = favResponse.addRemoveFav(request)
            addRemoveFavoriteResponse.postValue(rs!!)
        }

    }

    fun addToCart(
        pathEndPoint: String,
        furnitureId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?
    ) {
        viewModelScope.launch {
            val request = AddCartRequest(furnitureId, modelType, modelId, qty)
            val rs = productsRepo.addToCart(pathEndPoint, request)
            rs?.let { addCartResponse.postValue(it) }
        }
    }

    val cartItemRemovedResponse = MutableLiveData<BaseResponse<CartData>>()

    fun removeCartItem(modelType: String?, modelId: Int?) {
        viewModelScope.launch {
            val request = ItemRemovingRequest(modelId, modelType)
            val rs = productsRepo.removeCartItem( request)
            rs?.let { cartItemRemovedResponse.postValue(it) }
        }
    }
    val getCartResponse: MutableLiveData<BaseResponse<ArrayList<CartData>>?> by lazy {
        MutableLiveData()
    }
    fun getCart() {
        viewModelScope.launch {
            val rs = productsRepo.getCart()
            rs?.let { getCartResponse.postValue(it) }
        }
    }
}