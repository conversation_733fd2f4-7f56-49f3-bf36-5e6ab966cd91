<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/product_img"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/TransparentBackground"
    android:background="@color/white"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/btnShare"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:padding="15dp"
            android:background="@drawable/bg_ripple"
            android:layout_marginEnd="20dp"
            android:src="@drawable/ic_share_outline"
            app:layout_constraintBottom_toBottomOf="@id/navigate_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/navigate_back"
            app:tint="@color/black" />

        <ImageView
            android:id="@+id/navigate_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="50dp"
            android:layout_marginStart="20dp"
            android:padding="15dp"
            android:background="@drawable/bg_ripple"
            android:clickable="true"
            android:contentDescription="@string/btn_back_to_shopping"
            android:src="@drawable/ic_back_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black" />
    </androidx.constraintlayout.widget.ConstraintLayout>

