package com.techcubics.albarkahyper.ui.adapters.chat

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.databinding.ItemChatRoomBinding
import com.techcubics.albarkahyper.common.IItemClickListener
import com.techcubics.albarkahyper.ui.adapters.holders.chat.ChatRoomHolderItem


class ChatRoomsHistoryAdapter <T>(val onItemClicked: IItemClickListener?=null): RecyclerView.Adapter<ChatRoomHolderItem<T>>() {


    lateinit var items:List<T>

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatRoomHolderItem<T> {

        val itemBinding=ItemChatRoomBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ChatRoomHolderItem(itemBinding!!,parent.context,onItemClicked!!)

    }

    override fun onBindViewHolder(holder: ChatRoomHolderItem<T>, position: Int) {

        holder.bind(items.get(position))

    }


    override fun getItemCount(): Int {
        return items.size
    }
}