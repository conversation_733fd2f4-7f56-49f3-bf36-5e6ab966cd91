package com.techcubics.data.model.pojo
import com.google.gson.annotations.SerializedName

data class ProductByCategoryDataProduct(
    @SerializedName("id"          ) var id          : Int?              = null,
    @SerializedName("product_id"  ) var productId   : Int?              = null,
    @SerializedName("name"        ) var name        : String?           = null,
    @SerializedName("description" ) var description : String?           = null,
    @SerializedName("price"       ) var price       : Float?              = null,
    @SerializedName("video"       ) var video       : String?           = null,
    @SerializedName("icon"        ) var icon        : String?           = null,
    @SerializedName("images"      ) var images      : ArrayList<Image> = arrayListOf(),
    @SerializedName("sizes"       ) var sizes       : ArrayList<Size>  = arrayListOf(),
    @SerializedName("colors"      ) var colors      : ArrayList<ColorObject> = arrayListOf(),
    @SerializedName("category"    ) var category    : Category?         = Category(),
    @SerializedName("shop"        ) var shop        : ProductDetailsStore?             = ProductDetailsStore(),
    @SerializedName("is_fav"      ) var isFav       : Boolean?          = null,
    @SerializedName("rate"        ) var rate        : Float?              = null,
    @SerializedName("rate_count"  ) var rateCount   : Int?              = null,
    @SerializedName("model_type"  ) var modelType   : String?           = null,
    @SerializedName("qty_cart"    ) var qtyCart     : String?           = null,
)