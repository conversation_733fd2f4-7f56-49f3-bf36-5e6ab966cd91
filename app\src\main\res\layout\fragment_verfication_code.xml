<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="32dp"
                android:layout_marginTop="100dp"
                android:layout_marginRight="16dp"
                android:orientation="vertical">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:text="@string/verify_code"
                    android:textAppearance="@style/header_auth" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="10dp"
                    android:text="@string/eneter_phonenum_has_Send_to_you"
                    android:textAppearance="@style/sub_header_auth"
                    android:textColor="@color/project_dark_gray" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="56dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="49dp"
                    android:layoutDirection="ltr"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/input_code1"
                        android:layout_width="40dp"
                        android:layout_height="45dp"
                        android:gravity="center"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="no"
                        android:inputType="number"
                        android:maxLength="1"
                        android:textColor="@color/black"
                        android:textSize="24dp"
                        android:textStyle="bold"
                        tools:ignore="LabelFor" />

                    <EditText
                        android:id="@+id/input_code2"
                        android:layout_width="40dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="4dp"
                        android:gravity="center"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="no"
                        android:inputType="number"
                        android:maxLength="1"
                        android:textColor="@color/black"
                        android:textSize="24dp"
                        android:textStyle="bold"
                        tools:ignore="LabelFor" />

                    <EditText
                        android:id="@+id/input_code3"
                        android:layout_width="40dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="4dp"
                        android:gravity="center"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="no"
                        android:inputType="number"
                        android:maxLength="1"
                        android:textColor="@color/black"
                        android:textSize="24dp"
                        android:textStyle="bold"
                        tools:ignore="LabelFor" />

                    <EditText
                        android:id="@+id/input_code4"
                        android:layout_width="40dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="4dp"
                        android:gravity="center"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="no"
                        android:inputType="number"
                        android:maxLength="1"
                        android:textColor="@color/black"
                        android:textSize="24dp"
                        android:textStyle="bold"
                        tools:ignore="LabelFor" />

                    <EditText
                        android:id="@+id/input_code5"
                        android:layout_width="40dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="4dp"
                        android:gravity="center"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="no"
                        android:inputType="number"
                        android:maxLength="1"
                        android:textColor="@color/black"
                        android:textSize="24dp"
                        android:textStyle="bold"
                        tools:ignore="LabelFor" />

                    <EditText
                        android:id="@+id/input_code6"
                        android:layout_width="40dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="4dp"
                        android:gravity="center"
                        android:imeOptions="actionNext"
                        android:importantForAutofill="no"
                        android:inputType="number"
                        android:maxLength="1"
                        android:textColor="@color/black"
                        android:textSize="24dp"
                        android:textStyle="bold"
                        tools:ignore="LabelFor" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/send"
                        android:layout_width="wrap_content"
                        android:layout_height="24dp"
                        android:layout_marginTop="48dp"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:text="@string/send"
                        android:textAppearance="@style/label_auth_edittext"
                        android:textColor="@color/red"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/count_down"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="48dp"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:text="50"
                        android:textAppearance="@style/label_countdown_verificationcode"
                        android:textColor="@color/red" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="48dp"
                        android:background="@android:color/transparent"
                        android:gravity="center_horizontal"
                        android:text="@string/resend_code"
                        android:textAppearance="@style/label_resendcode_verificationcode" />


                </LinearLayout>

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="32dp"
                   />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>