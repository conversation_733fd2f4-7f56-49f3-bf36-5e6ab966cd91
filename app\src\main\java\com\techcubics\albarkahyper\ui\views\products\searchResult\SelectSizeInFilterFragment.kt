package com.techcubics.albarkahyper.ui.views.products.searchResult

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.model.pojo.ProductColorsSizesData
import com.techcubics.data.model.pojo.Size
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.ISizesClickListener
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.FragmentSelectSizeInFilterBinding
import com.techcubics.albarkahyper.ui.adapters.home.SizesAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.ArrayList

class SelectSizeInFilterFragment : Fragment() , ISizesClickListener {

    private lateinit var binding : FragmentSelectSizeInFilterBinding
    private lateinit var sizeAdapter: SizesAdapter
    private lateinit var progressButton: ProgressButton
    private  val productsViewModel: ProductsViewModel by viewModel<ProductsViewModel>()
    private var selectedSizes: ArrayList<Size> = arrayListOf()
    private val mainViewModel: MainViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentSelectSizeInFilterBinding.inflate(inflater, container, false)
        initView()
        observeViews()
        return binding.root
    }

    private fun observeViews() {
        productsViewModel.productColorsSizesResponse.observe(viewLifecycleOwner, Observer {

            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)
            binding.containerLayout.visibility = View.VISIBLE
            binding.btnSearchSize.constraintsLayout.visibility = View.VISIBLE

            if (it.status!!) {

                if (it.data != null) {
                    if (it.data!!.colors!!.size > 0) {
                        showSizeData(it.data)
                    } else {

                    }
                }

            }
        })

    }

    private fun initView() {
        binding.toolbarFilter.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }
        progressButton = ProgressButton(requireContext())
        progressButton.init(binding.btnSearchSize)
        binding.btnSearchSize.textView.text = getString(com.techcubics.style.R.string.done)
        binding.toolbarFilter.tvTitle.text = getString(com.techcubics.style.R.string.available_sizes)
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        binding.containerLayout.visibility = View.GONE
        sizeAdapter = SizesAdapter(handler = this)
        productsViewModel.getColorsSizes()
        progressButton.binding.constraintsLayout.setOnClickListener {
            mainViewModel.setSizeList(selectedSizes)
            CoroutineScope(Dispatchers.Main).launch {
                progressButton.btnActivated()
                delay(1000)
                progressButton.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.done),null)
                delay(300)
                findNavController().popBackStack()
            }

        }
    }

    fun showSizeData(data: ProductColorsSizesData?) {
        data?.sizes?.let { sizeAdapter.setItemsList(it) }
        binding.includeFilterCategorySection.rvSizes.adapter = sizeAdapter
        binding.includeFilterCategorySection.rvSizes.layoutManager = LinearLayoutManager(requireContext(),LinearLayoutManager.VERTICAL,false)
    }

    override fun onAddSizeClick(size: Size) {
        selectedSizes.add(size)
    }

    override fun onRemoveSize(size: Size) {
        selectedSizes.remove(size)
    }


}