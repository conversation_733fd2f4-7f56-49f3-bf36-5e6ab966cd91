<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:theme="@style/checkbox"/>
            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="15dp"
                app:cardCornerRadius="5dp"
                >
                <ImageView
                    android:id="@+id/shop_image"
                    android:layout_width="40dp"
                    android:layout_height="30dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/flag_egypt"
                    />

            </androidx.cardview.widget.CardView>
            <TextView
                android:id="@+id/shop_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="18dp"
                android:paddingTop="16dp"
                android:paddingBottom="15dp"
                android:text="مصر"
                android:textAppearance="@style/label_item_shoptype"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
</layout>