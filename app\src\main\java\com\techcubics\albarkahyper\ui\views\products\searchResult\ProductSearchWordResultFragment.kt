package com.techcubics.albarkahyper.ui.views.products.searchResult


import android.content.Context
import android.os.Bundle
import android.text.Html
import android.text.Spanned
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.akiniyalocts.pagingrecycler.PagingDelegate
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.CartData
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.pojo.ProductSearchData
import com.techcubics.data.model.requests.SearchAreaRequest
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentProductSearchWordResultBinding
import com.techcubics.albarkahyper.ui.adapters.product.ProductsPagingAdapter
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*
import kotlin.math.ceil

class ProductSearchWordResultFragment : Fragment(), IPagePagedRowset<ProductSearchData>,
    IFilterClickListener, IFavClickListener, IOnAdapterItemClickHandler {


    private lateinit var binding: FragmentProductSearchWordResultBinding

    //    private val binding get() = _binding!!
    private lateinit var productsPagingAdapter: ProductsPagingAdapter<ProductSearchData>
    private var appContext: Context? = null
    private var totalPrice = 0f

    val productsFragmentViewModel: ProductsViewModel by viewModel<ProductsViewModel>()
    private lateinit var resultList: ArrayList<ProductSearchData>
    private var isLoading: Boolean = false
    private var isGeneral: Boolean = true
    private var word: String? = null
    private var priceFrom: Int? = 1
    private var priceTo: Int? = 300
    private var selectedColor: ArrayList<String>? = null
    private var selectedSize: ArrayList<String>? = null
    private var selectedCategories: ArrayList<Int>? = null
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var name: String
    private lateinit var etSearch: EditText
    private var _operation: Int = -1
    private var _position: Int? = null
    private lateinit var popupDialog: PopupDialog
    var f: ProductsFilterFragment? = null
    private val TAG = "ProductSearchWordResult"

    //    lateinit var searchWordProgressButton: CircleProgressButton
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private val mainViewModel: MainViewModel by activityViewModels()
    private var price: String? = null
    private var searchAreaRequest: SearchAreaRequest? = null
    private var latlng: Latlng? = null
    private var fromFilterPage: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
//        if (_binding == null) {
        Log.d(TAG, "onCreateView: $savedInstanceState")
        binding = FragmentProductSearchWordResultBinding.inflate(inflater, container, false)

        Log.i("conflictContext", "FragmentProductSearchWordResult")
        init()
//        }
        observers()
        events()
        return binding.root
    }

    //    override fun onResume() {
//        super.onResume()
//        getCurrentPage()
//        productsFragmentViewModel.getProductsByWordFilter(
//            word = word,
//            page = itemPage
//        )
//    }
    override fun init() {
        productsPagingAdapter =
            ProductsPagingAdapter<ProductSearchData>(
                onFavClickListener = this,
                onClickHandler = this
            )
//        binding.rvProducts.adapter = productsAdapter
//        binding.rvProducts.layoutManager =
//            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        // SharedPreferencesManager.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        etSearch = binding.searchToolbar.txtSearchWord
        arguments?.let {

            if (fromFilterPage == null) {
                if (word.isNullOrEmpty()) {
                    word = it.getString(Constants.INTENT_WORD) ?: ""
                }
                productsFragmentViewModel.getProductsByWordFilter(
                    word = word,
                    page = 1
                )
                etSearch.setText(word)
            }

            name = it.getString(Constants.INTENT_NAME, "")
            binding.toolbarSearchByWord.tvTitle.text = name
            //
        }
        showHidePlaceHolder(show = false, type = null, message = null)
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvProducts.visibility = View.GONE
        binding.includeQtyCartInfo.root.visibility = View.GONE
        calcPrice(0, 0f, 0f)
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)
        //
        resultList = ArrayList<ProductSearchData>()
    }

    override fun observers() {

        productsFragmentViewModel.productSearchResponse.observe(viewLifecycleOwner) {

            productsPagingAdapter.notifyDataSetChanged()
            Helper.loadingAnimationVisibility(View.GONE, binding.loadingAnimation.root)

            //try {
            if (it.status!!) {

                if (it.data != null) {

                    if (!it.data!!.isEmpty()) {
                        Log.i("product", "data on page " + it.data!!.size.toString())
                        showData(it.data!!)
                        binding.includeQtyCartInfo.root.visibility = View.VISIBLE
                        binding.rvProducts.visibility = View.VISIBLE
                        showHidePlaceHolder(
                            show = false,
                            type = null,
                            message = null
                        )
                    } else {
                        binding.pagingLoadingImg.visibility = View.GONE
                        binding.includeQtyCartInfo.root.visibility = View.GONE
                        binding.rvProducts.visibility = View.GONE
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Empty,
                            message = appContext?.resources?.getString(com.techcubics.style.R.string.message_empty_list_general)
                        )

                    }
                } else {
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    //empty
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            } else {
                binding.pagingLoadingImg.visibility = View.GONE
                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                //error
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Error,
                    message = it.message
                )
            }

//            } catch (ex: Exception) {
//                //error
//                Log.i("product", "exception  " + it.message)
//
//                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
//                btnFilter.isEnabled = false
//            }
        }


        productsFragmentViewModel.addRemoveFavoriteResponse.observe(viewLifecycleOwner, Observer {

            try {

                if (it.status!!) {

                    when (_operation) {
                        1 -> resultList[_position!!].isFav =
                            false
                        2 -> resultList[_position!!].isFav =
                            true
                    }
                    showHidePlaceHolder(
                        show = false,
                        type = null,
                        message = null
                    )
                    // productsAdapter.notifyDataSetChanged()

                } else {

                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.pagingLoadingImg.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Empty,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {


                binding.includeQtyCartInfo.root.visibility = View.GONE
                binding.pagingLoadingImg.visibility = View.GONE
                binding.rvProducts.visibility = View.GONE
                showHidePlaceHolder(
                    show = true,
                    type = LottieIconEnum.Empty,
                    message = ex.message
                )
            }


        })

        productsFragmentViewModel.addCartResponse.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.message.toString() != Constants.SERVER_ERROR) {
//               getCurrentPage()
//                productsFragmentViewModel.getProductsByWordFilter(
//                    word = word,
//                    colors = selectedColor,
//                    sizes = selectedSize,
//                    categories = selectedCategories,
//                    start = priceFrom,
//                    to = priceTo,
//                    page = itemPage,
//                    latlng = this.latlng,
//                    searchAreaRequest = this.searchAreaRequest
//                )
                    if (it.status == true) {
                        updateProductItem(it.data)
                        it.data?.totalPrice?.let { p ->
                            totalPrice = p
                            calcPrice(maxQty, minOrder, totalPrice)
                        }
//                        searchWordProgressButton.btnRoundFinishedSuccessfully()
//                    bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.added_to_cart))
                    } else {
                        calcPrice(0, 0f, 0f)
                        if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                            CoroutineScope(Dispatchers.Main).launch {
                                popupDialog.showSessionExpiredDialog(requireContext())
                                delay(1200)
                                popupDialog.onDismiss()
                                findNavController().navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                            }
                        } else {
//                            searchWordProgressButton.btnRoundFinishedFailed()
                            bottomSheetAlertDialog.showDialog(it.message.toString())
                        }

                    }
                } else if (it?.message.toString().contains(Constants.SERVER_ERROR)) {
                    calcPrice(0, 0f, 0f)
                    Helper.ShowErrorDialog(
                        requireContext(),
                        getString(com.techcubics.style.R.string.server_error)
                    )
                }
                productsPagingAdapter.items = resultList
                productsPagingAdapter.notifyItemChanged(itemPosition)
                productsFragmentViewModel.addCartResponse.value = null
            }
        }

        productsFragmentViewModel.cartItemRemovedResponse.observe(context as LifecycleOwner) { cart ->
            if (cart != null) {
//                getCurrentPage()
//                productsFragmentViewModel.getProductsByWordFilter(
//                    word = word,
//                    colors = selectedColor,
//                    sizes = selectedSize,
//                    categories = selectedCategories,
//                    start = priceFrom,
//                    to = priceTo,
//                    page = itemPage,
//                    latlng =this.latlng,
//                    searchAreaRequest = this.searchAreaRequest
//                )
                calcPrice(0, 0f, 0f)
                if (!cart.status!!) {
                    bottomSheetAlertDialog.showDialog(cart.message.toString())
                } else {
                    cart.data?.totalPrice?.let { p ->
                        totalPrice = p
                        calcPrice(maxQty, minOrder, totalPrice)
                    } ?: kotlin.run { totalPrice = 0f }
                    resultList[itemPosition].qtyCart = 0
                }
                productsPagingAdapter.items = resultList
                productsPagingAdapter.notifyItemChanged(itemPosition)
                productsFragmentViewModel.cartItemRemovedResponse.value = null
            }
        }

    }

    private fun updateProductItem(data: CartData?) {
        val newQtyCart =
            data?.cartDetails?.find { p ->
                p.modelId == resultList[itemPosition].productID
            }?.qty
        if (newQtyCart != null) {
            resultList[itemPosition].qtyCart = newQtyCart
        }
//        var currQty = resultList[itemPosition].qtyCart
//        currQty+=offset
//        resultList[itemPosition].qtyCart = currQty

    }

    private var itemPage = 1
    private fun getCurrentPage() {
        val offset = 10
        val numPages = ceil(resultList.size.toFloat() / offset.toFloat()).toInt()
        for (i in 0 until numPages) {
            val start = (offset * i)
            val end = offset * (i + 1)
            if (itemPosition in start until end) {
                itemPage = i + 1
                break
            }
        }
    }

    override fun events() {
        binding.searchToolbar.btnFilter.setOnClickListener {
            mainViewModel.refrence.value = this
            findNavController().navigate(R.id.action_productSearchWordResultFragment_to_productsFilterFragment)

        }

        binding.toolbarSearchByWord.mainToolbar.setNavigationOnClickListener {

            findNavController().popBackStack()
        }



        etSearch.setOnEditorActionListener { v, actionId, event ->
            when (actionId) {
                EditorInfo.IME_ACTION_SEARCH -> {

                    if (v.text.isNotBlank() && v.text.isNotEmpty()) {

                        v.clearFocus()
                        v.hideKeyboard()
                        resultList.clear()
                        this.word = v.text.toString()
//                        binding.rvProducts.adapter = null
                        binding.includeQtyCartInfo.root.visibility = View.GONE
                        binding.pagingLoadingImg.visibility = View.GONE
                        binding.rvProducts.visibility = View.GONE
                        Helper.loadingAnimationVisibility(
                            View.VISIBLE,
                            binding.loadingAnimation.root
                        )
                        showHidePlaceHolder(show = false, type = null, message = null)
                        isGeneral = true
                        productsFragmentViewModel.getProductsByWordFilter(
                            word = v.text.toString().trim(),
                            page = 1
                        )
                        fromFilterPage = null
                        true
                    } else {
                        false
                    }


                }

                else -> false
            }
        }
    }

    fun View.hideKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)
    }


    override fun showData(items: List<ProductSearchData>) {

        if (productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage == 1) {
            resultList = items as ArrayList<ProductSearchData>
            productsPagingAdapter.setItemsList(resultList)
            binding.rvProducts.adapter = productsPagingAdapter
            binding.rvProducts.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
//            //
//            productsAdapter.notifyDataSetChanged()
            var pageDelegate =
                PagingDelegate.Builder(productsPagingAdapter).attachTo(binding.rvProducts)
                    .listenWith(this).build()


        } else {

            items.forEach {
                val p = resultList.find { p -> p.id == it.id }
                val index = resultList.indexOf(p)
//                if (p==null) {
                resultList.add(it)
//                } else {
//                    resultList[index] = it
//                }
            }
            productsPagingAdapter.notifyDataSetChanged()
            onDonePaging()
        }

        Log.d(TAG, "showDiscounts: ${resultList.size}")

    }

    override fun showHidePlaceHolder(
        show: Boolean, type: LottieIconEnum?, message: String?,
        container: View?
    ) {

        if (show) {

            binding.placeholder.root.visibility = View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    binding.placeholder.layout.visibility = View.VISIBLE
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_empty)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {
                    binding.includeQtyCartInfo.root.visibility = View.GONE
                    binding.rvProducts.visibility = View.GONE
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    if (message == Constants.SERVER_ERROR) {
                        binding.placeholder.tvMessage.text =
                            getString(com.techcubics.style.R.string.server_error)

                    } else {
                        binding.placeholder.tvMessage.text = message

                    }
                }
                else -> throw IllegalStateException("error")

            }
        } else {

            binding.placeholder.root.visibility = View.GONE
        }
    }

    override fun onFavClick(parent: Int, position: Int, operation: Int) {
        _operation = operation
        _position = position
        resultList[position].id?.let { productsFragmentViewModel.addRemoveFav(it) }
    }

    override fun onFilterClick(
        from: Int,
        to: Int,
        colors: ArrayList<String>?,
        sizes: ArrayList<String>?,
        categories: ArrayList<Int>?,
        latlng: Latlng?,
        searchAreaRequest: SearchAreaRequest?,
        price: String?
    ) {

        fromFilterPage = "filter"

        var localCategories = categories

        this.selectedCategories = localCategories
        this.priceFrom = from
        this.priceTo = to
        this.latlng = latlng
        this.searchAreaRequest = searchAreaRequest
        this.price = price

        if (localCategories.isNullOrEmpty()) {
            localCategories = null
            selectedCategories = null
        }

        if (!localCategories.isNullOrEmpty()) {
            productsFragmentViewModel.getProductsByWordFilter(
                word = null,
                colors = null,
                sizes = null,
                categories = localCategories,
                start = from,
                to = to,
                page = 1,
                latlng = latlng,
                searchAreaRequest = searchAreaRequest,
                price = price
            )
        } else {
            productsFragmentViewModel.getProductsByWordFilter(
                word = word,
                colors = null,
                sizes = null,
                categories = localCategories,
                start = from,
                to = to,
                page = 1,
                latlng = latlng,
                searchAreaRequest = searchAreaRequest,
                price
            )
        }
        resultList.clear()
        binding.rvProducts.adapter = null
        binding.rvProducts.adapter?.notifyDataSetChanged()
        binding.pagingLoadingImg.visibility = View.GONE
        binding.rvProducts.visibility = View.GONE
        binding.includeQtyCartInfo.root.visibility = View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE, binding.loadingAnimation.root)

        isGeneral = false
        showHidePlaceHolder(show = false, type = null, message = null)
    }

    override fun onItemClicked(itemId: Int?, type: String) {
        val bundle = Bundle()
//        bundle.putString(Constants.ITEM_TYPE, RateTypesEnum.Product.value)
        itemPosition = resultList.indexOf(resultList.find { p -> p.id == itemId })
        bundle.putInt(Constants.INTENT_ID, itemId ?: -1)
        findNavController().navigate(R.id.view_product_details_from_search_word, bundle)
    }

    private var qty = 0
    private var maxQty = 0
    private var itemPrice = 0f
    private var minOrder = 0f
    private var itemPosition = -1

    override fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
        this.qty = qty ?: 0
        this.itemPrice = itemPrice
        this.maxQty = maxQty
        this.minOrder = minOrder
        this.itemPosition = itemPosition
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            calcPrice(maxQty, minOrder, totalPrice)
//            searchWordProgressButton = progressButton
            productsFragmentViewModel.addToCart(
                pathEndPoint,
                shopId,
                modelType,
                modelId,
                qty
            )

        } else {
            findNavController().navigate(R.id.go_to_login)
        }

    }

    override fun removeItemFromCart(
        modelType: String?,
        modelId: Int?,
        productPrice: Float,
        itemPosition: Int,
    ) {
        this.itemPosition = itemPosition
        if (SharedPreferencesManager.isLoggedIn() == "true") {
            calcPrice(maxQty, minOrder, totalPrice)
            productsFragmentViewModel.removeCartItem(modelType, modelId)

        } else {
            findNavController().navigate(R.id.go_to_login)
        }
    }

    private fun calcPrice(maxQty: Int, minOrder: Float, totalPrice: Float) {
        //min num
//        val minNumStr = getString(com.techcubics.style.R.string.max_num, maxQty.toString())
//        val minNumStyledText: Spanned = Html.fromHtml(minNumStr, Html.FROM_HTML_MODE_LEGACY)
//        binding.includeQtyCartInfo.minNum.text = minNumStyledText
//        //min order
        val numFormatMinOrder =
            java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(minOrder) + "&#8200;" +
                    getString(com.techcubics.style.R.string.currency_name)
        val minOrderStr = getString(com.techcubics.style.R.string.min_order, numFormatMinOrder)
        val minOrderStyledText: Spanned = Html.fromHtml(minOrderStr, Html.FROM_HTML_MODE_LEGACY)
        binding.includeQtyCartInfo.minOrder.text = minOrderStyledText
        //total amount
        val numFormatPrice = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH)
            .format(totalPrice) + "&#8200;" +
                getString(com.techcubics.style.R.string.currency_name)
        val totalAmountStr =
            getString(com.techcubics.style.R.string.product_order_total_amount, numFormatPrice)
        val totalAmountStyledText: Spanned = Html.fromHtml(
            totalAmountStr,
            Html.FROM_HTML_MODE_LEGACY
        )
        binding.includeQtyCartInfo.totalAmount.text = totalAmountStyledText

    }


    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (appContext == null) {
            appContext = context.applicationContext
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    override fun onPage(p0: Int) {


        if (!isLoading) {
            Log.d(TAG, "onPage: ${p0}")
            if (productsFragmentViewModel.productSearchResponse.value?.pagingator?.hasMorePages!!) {
                isLoading = true
                binding.pagingLoadingImg.visibility = View.VISIBLE

                /* when(isGeneral){
                     true->productsFragmentViewModel.getProductsByWord(word!!,response.pagingator?.currentPage!!+1)
                     false->productsFragmentViewModel.getProductsByWordFilter(colors=selectedColor, start =from,to=to,page=response.pagingator?.currentPage!!+1,word=word!!)
                 }*/


                if (fromFilterPage == null) {
                    priceFrom = null
                    priceTo = null
                    selectedCategories = null
                    latlng = null
                    price = null
                    searchAreaRequest = null
                } else {
                    selectedColor = null
                    selectedSize = null
                    if (selectedCategories.isNullOrEmpty()) {
                        selectedCategories = null

                    } else {
                        word = null
                    }
                }

                productsFragmentViewModel.getProductsByWordFilter(
                    word = word,
                    colors = null,
                    sizes = null,
                    categories = selectedCategories,
                    start = priceFrom,
                    to = priceTo,
                    page = productsFragmentViewModel.productSearchResponse.value?.pagingator?.currentPage!! + 1,
                    latlng = this.latlng,
                    searchAreaRequest = this.searchAreaRequest,
                    price = price
                )
            }
        }
    }

    override fun onDonePaging() {
        binding.pagingLoadingImg.visibility = View.GONE
        isLoading = false
    }
}