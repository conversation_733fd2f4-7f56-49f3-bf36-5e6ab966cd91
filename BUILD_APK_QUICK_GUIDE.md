# دليل سريع لبناء APK - Quick APK Build Guide

## 🚀 خطوات سريعة لبناء APK

### الطريقة الأولى: استخدام ملف Batch (Windows)
```batch
# تشغيل ملف البناء المجهز
build_apk.bat
```

### الطريقة الثانية: استخدام PowerShell (Windows)
```powershell
# تشغيل PowerShell script
powershell -ExecutionPolicy Bypass -File build_apk.ps1
```

### الطريقة الثالثة: أوامر Gradle المباشرة
```bash
# تنظيف المشروع
./gradlew clean

# بناء APK للإنتاج
./gradlew assembleRelease

# بناء Android App Bundle (مفضل لـ Google Play)
./gradlew bundleRelease
```

## 📁 مواقع الملفات بعد البناء

### APK Files:
```
app/build/outputs/apk/release/app-release.apk
```

### AAB Files (Android App Bundle):
```
app/build/outputs/bundle/release/app-release.aab
```

## ✅ التحقق من نجاح البناء

### 1. فحص وجود الملفات:
- تحقق من وجود `app-release.apk` في مجلد `app/build/outputs/apk/release/`
- تحقق من وجود `app-release.aab` في مجلد `app/build/outputs/bundle/release/`

### 2. فحص معلومات APK:
```bash
# عرض معلومات APK
aapt dump badging app/build/outputs/apk/release/app-release.apk
```

### 3. اختبار التثبيت:
```bash
# تثبيت على جهاز متصل
adb install app/build/outputs/apk/release/app-release.apk
```

## 🔧 حل المشاكل الشائعة

### مشكلة: Java not found
**الحل:**
```batch
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%
```

### مشكلة: Android SDK not found
**الحل:**
```batch
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%
```

### مشكلة: Gradle build failed
**الحل:**
1. تنظيف المشروع: `./gradlew clean`
2. إعادة تحميل dependencies: `./gradlew --refresh-dependencies`
3. إعادة المحاولة: `./gradlew assembleRelease`

## 📋 معلومات الإصدار الحالي

- **Version Code**: 30
- **Version Name**: 1.1.8
- **Target SDK**: 35 (Android 15)
- **Min SDK**: 24 (Android 7.0)
- **Package Name**: com.techcubics.albarkahyper

## 📦 ملفات Google Play Store

جميع الملفات المطلوبة للرفع على متجر Google Play متوفرة في مجلد:
```
google-play-assets/
├── README.md
├── app-description-arabic.txt
├── app-description-english.txt
├── privacy-policy.md
├── release-notes.txt
├── build-instructions.md
└── upload-checklist.md
```

## 🎯 الخطوات التالية

1. **بناء APK/AAB** باستخدام إحدى الطرق أعلاه
2. **اختبار التطبيق** على أجهزة مختلفة
3. **مراجعة ملفات Google Play** في مجلد `google-play-assets`
4. **رفع التطبيق** على Google Play Console
5. **متابعة عملية المراجعة**

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `build-instructions.md` للتفاصيل الكاملة
2. راجع `upload-checklist.md` قبل الرفع
3. تأكد من تحديث جميع المتطلبات

---

**ملاحظة مهمة**: تأكد من اختبار التطبيق بشكل كامل قبل الرفع على متجر Google Play!
