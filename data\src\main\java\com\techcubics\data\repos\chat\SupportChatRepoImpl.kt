package com.techcubics.data.repos.chat

import com.techcubics.data.model.pojo.SendSupportData
import com.techcubics.data.model.pojo.SupportTicketData
import com.techcubics.data.model.requests.TicketSendCallRequest
import com.techcubics.data.model.requests.chat.SupportSendRequest
import com.techcubics.data.model.requests.chat.SupportTicketMessagesRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

class SupportChatRepoImpl(private val retrofitBuilder: RetrofitBuilder): RepositoryResponse ,SupportChatRepo{

    override suspend fun getHistory(request:SupportTicketMessagesRequest): BaseResponse<MutableList<SupportTicketData>>?{
        return try {
            val result = retrofitBuilder.getBindObject()?.ticketCall()
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }

    override suspend fun send(request: TicketSendCallRequest): BaseResponse<SendSupportData>?{

        return try {

            val result = retrofitBuilder.getBindObject()?.ticketSendCall(request=request)
            baseResponse(result)
        } catch (ex: Exception) {
            handleServerExceptions(ex)
        }
    }
}