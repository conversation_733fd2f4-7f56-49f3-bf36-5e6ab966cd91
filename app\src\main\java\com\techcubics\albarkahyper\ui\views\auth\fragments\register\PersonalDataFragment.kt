package com.techcubics.albarkahyper.ui.views.auth.fragments.register

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.view.GravityCompat
import androidx.core.view.isEmpty
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.requests.auth.RegisterRequest
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.databinding.FragmentPersonalDataBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.albarkahyper.ui.views.products.details.viewmodels.MainViewModel
import com.techcubics.shared.enums.PagesEnum
import com.techcubics.shared.utils.AuthUtils
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class PersonalDataFragment : Fragment() {

    private lateinit var personaldataBinding: FragmentPersonalDataBinding
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val signUpViewModel by viewModel<AuthViewModel>()
    private val profileViewModel by viewModel<ProfileViewModel>()
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private lateinit var popupDialog: PopupDialog
    private val personalData=PersonalData()
    private val mainViewModel: MainViewModel by activityViewModels()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        personaldataBinding = FragmentPersonalDataBinding.inflate(inflater, container, false)

        initViews()
        viewObserver()
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        return personaldataBinding.root
    }


    private fun initViews() {

        personaldataBinding.termsConditionsLayout.termsConditionsCheckbox.setOnCheckedChangeListener { _, b ->
            sharedPreferencesManager.setTerms(b)
            if (b) {
                personalData.termsAndConditions = sharedPreferencesManager.isTermsAccepted()
                mainViewModel.setPersonalData(personalData)
            }
        }
        personaldataBinding.termsConditionsLayout.termsConditionsCheckbox.isChecked =
            sharedPreferencesManager.isTermsAccepted()

        if (sharedPreferencesManager.getLanguage().equals("ar")) {
            personaldataBinding.passwordEdt.gravity = GravityCompat.START
            personaldataBinding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            personaldataBinding.confirmPasswordEdt.gravity = GravityCompat.START
            personaldataBinding.confirmPasswordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
        }
        personaldataBinding.passwordEdt.addTextChangedListener {
            if (sharedPreferencesManager.getLanguage().equals("ar")) {
                personaldataBinding.passwordEdt.gravity = GravityCompat.END
                personaldataBinding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_END
                personaldataBinding.confirmPasswordEdt.gravity = GravityCompat.END
                personaldataBinding.confirmPasswordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_END
            } else {
                personaldataBinding.passwordEdt.gravity = GravityCompat.START
                personaldataBinding.passwordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
                personaldataBinding.confirmPasswordEdt.gravity = GravityCompat.START
                personaldataBinding.confirmPasswordEdt.textAlignment = View.TEXT_ALIGNMENT_VIEW_START
            }
        }

        personaldataBinding.countryPicker.setDefaultCountryUsingNameCode(sharedPreferencesManager.getCountryCode())
        personaldataBinding.countryPicker.resetToDefaultCountry()

        personaldataBinding.passwordEdt.addTextChangedListener {
//            AuthUtils.validatePassword(
//                personaldataBinding.passwordEdt.text.toString(),
//                personaldataBinding.validatePassGroup.specialchar,
//                personaldataBinding.validatePassGroup.AtoZ,
//                personaldataBinding.validatePassGroup.num,
//                personaldataBinding.validatePassGroup.charcount
//            )
            personalData.password=it.toString()
            mainViewModel.setPersonalData(personalData)
        }
        personaldataBinding.confirmPasswordEdt.addTextChangedListener {
            personalData.passwordConfirmation = it.toString()
            mainViewModel.setPersonalData(personalData)
        }

//        personaldataBinding.passwordEdt.setOnFocusChangeListener { _, hasFocus ->
//            if (hasFocus) {
//                personaldataBinding.validatePassGroup.group.visibility = View.VISIBLE
//            } else {
//                personaldataBinding.validatePassGroup.group.visibility = View.GONE
//            }
//        }


        personaldataBinding.termsConditionsLayout.termsConditionsTextview.setOnClickListener {
            Helper.loadingAnimationVisibility(
                View.VISIBLE,
                personaldataBinding.actionLoadingAnimation.root
            )
            profileViewModel.pagecall(PagesEnum.TermsCondition.value)
        }
        personaldataBinding.phoneEdt.addTextChangedListener {
            personalData.phone = it.toString()
            sharedPreferencesManager.convertCountryCode(personaldataBinding.countryPicker.selectedCountryName)
            personalData.mobileCode = personaldataBinding.countryPicker.selectedCountryCodeWithPlus
            personalData.countryCode = sharedPreferencesManager.getCountryCode()
            mainViewModel.setPersonalData(personalData)
        }
        personaldataBinding.nameEdt.addTextChangedListener {
            personalData.firstName = it.toString()
            mainViewModel.setPersonalData(personalData)
        }
        personaldataBinding.secondNameEdt.addTextChangedListener {
            personalData.lastName = it.toString()
            mainViewModel.setPersonalData(personalData)
        }

//        personaldataBinding.signBtnProgress.constraintsLayout.setOnClickListener {
//
//            if (checkUiValidity()) {
//                if (chechTermsAndConditions()) {
//                    progressBtn.btnActivated()
//                    sharedPreferencesManager.saveAddress(signUpBinding.addressEdt.text.toString())
//                    sharedPreferencesManager.convertCountryCode(signUpBinding.countryPicker.selectedCountryName)
//                    sharedPreferencesManager.saveMobileCode(signUpBinding.countryPicker.selectedCountryCodeWithPlus)
//                    Log.i("clothes", signUpBinding.countryPicker.selectedCountryName)
//                    Log.i("clothes", signUpBinding.countryPicker.selectedCountryCode)
//                    signUpViewModel.register(
//                        RegisterRequest(
//                            signUpBinding.nameEdt.text.toString(),
//                            signUpBinding.emailEdt.text.toString(),
//                            signUpBinding.phoneEdt.text.toString(),
//                            signUpBinding.passwordEdt.text.toString(),
//                            signUpBinding.confirmPasswordEdt.text.toString(),
//                            signUpBinding.addressEdt.text.toString(),
//                            sharedPreferencesManager.getCountryCode()
//                        )
//                    )
//                    sharedPreferencesManager.saveName(signUpBinding.emailEdt.text.toString())
//                    sharedPreferencesManager.savePassword(signUpBinding.passwordEdt.text.toString())
//                    sharedPreferencesManager.saveMobileCodeWithoutPlus(signUpBinding.countryPicker.selectedCountryCode)
//                }
//            }
//
//        }
    }


    private fun viewObserver() {
//        signUpViewModel.registerMutableLiveData.observe(viewLifecycleOwner) {
//            if (it?.status!!) {
//                sharedPreferencesManager.loggedIn("true")
//                progressBtn.btnFinishedSuccessfully(
//                    getString(com.techcubics.style.R.string.sign_up),
//                    null
//                )
//                CoroutineScope(Dispatchers.Main).launch {
//                    bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.signin_succeeded))
//                    delay(1500)
//                    bottomSheetAlertDialog.onDismiss()
//                    findNavController().popBackStack()
////                    findNavController().apply {
////                        navigate(R.id.action_signupFragment_to_homeFragment)
////                    }
//                }
//            } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
//                progressBtn.btnFinishedFailed(
//                    getString(com.techcubics.style.R.string.sign_up),
//                    null
//                )
//                Helper.ShowErrorDialog(
//                    requireContext(),
//                    getString(com.techcubics.style.R.string.server_error)
//                )
//            } else {
//                progressBtn.btnFinishedFailed(
//                    getString(com.techcubics.style.R.string.sign_up),
//                    null
//                )
//                sharedPreferencesManager.loggedIn("false")
//                bottomSheetAlertDialog.showDialog(it.message!!)
//            }
//
//        }
        profileViewModel.pageCallMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(
                View.GONE,
                personaldataBinding.actionLoadingAnimation.root
            )
            if (it != null) {
                if (!it.data?.description.isNullOrEmpty()) {
                    popupDialog.showTermsAndConditionsDialog(
                        requireContext(), it.data?.description!!, sharedPreferencesManager,
                        personaldataBinding.termsConditionsLayout.termsConditionsCheckbox
                    )

                }
                profileViewModel.pageCallMutableLiveData.value = null
            }

        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

    private fun chechTermsAndConditions(): Boolean {
        var check = true
        if (!personaldataBinding.termsConditionsLayout.termsConditionsCheckbox.isChecked) {
            bottomSheetAlertDialog.showDialog(getString(com.techcubics.style.R.string.accept_terms_and_conditions))
            check = false
        }
        return check
    }


}

data class PersonalData(
    var firstName: String?=null,
    var lastName: String?=null,
    var phone: String?=null,
    var countryCode: String?=null,
    var mobileCode: String?=null,
    var password: String?=null,
    var passwordConfirmation: String?=null,
    var termsAndConditions:Boolean?=false
)