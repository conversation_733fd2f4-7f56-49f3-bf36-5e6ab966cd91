package com.techcubics.albarkahyper.ui.adapters.home

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Size
import com.techcubics.albarkahyper.common.ISizesClickListener
import com.techcubics.albarkahyper.databinding.ItemSizeFilterBinding
import com.techcubics.albarkahyper.ui.adapters.holders.product.ProductSizeFilterHolderItem


class SizesAdapter(val handler: ISizesClickListener?=null): RecyclerView.Adapter<ProductSizeFilterHolderItem>() {

    lateinit var items:MutableList<Size>
    var check = false

    fun setItemsList(_items:MutableList<Size>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductSizeFilterHolderItem {
        val itemBinding = ItemSizeFilterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductSizeFilterHolderItem(itemBinding,parent.context,handler=this.handler)
    }

    override fun onBindViewHolder(holder: ProductSizeFilterHolderItem, position: Int) {

        if(check){
            Log.i("here","reserColorssssssssssssss22")
            holder.binding.size.isChecked = false
        }
        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
        return items.size
    }

    fun resetSizes() {
        check = true
        Log.i("here","reserColorssssssssssssss11")
    }

}