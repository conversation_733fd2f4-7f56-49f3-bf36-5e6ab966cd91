package com.techcubics.albarkahyper.ui.adapters.profile

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.Governerate
import com.techcubics.albarkahyper.databinding.ItemLanguageBinding
import com.techcubics.albarkahyper.ui.adapters.holders.profile.GovernerateHolderItem


class GovernerateAdapter(
    private val context: Context,
    private val listOfGovernerate: List<Governerate>,
    private val popupMenuItemLiveData: MutableLiveData<Governerate?>,
    private val dialog: Dialog
):
    RecyclerView.Adapter<GovernerateHolderItem>(){

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GovernerateHolderItem {

        val itemBinding = ItemLanguageBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return GovernerateHolderItem(itemBinding)
    }


    override fun onBindViewHolder(holder: GovernerateHolderItem, position: Int) {

        holder.itemName.text = listOfGovernerate.get(position).name

        holder.itemView.setOnClickListener {
            popupMenuItemLiveData.value = listOfGovernerate.get(position)
            dialog.dismiss()
        }

    }

    override fun getItemCount() = listOfGovernerate.size

}