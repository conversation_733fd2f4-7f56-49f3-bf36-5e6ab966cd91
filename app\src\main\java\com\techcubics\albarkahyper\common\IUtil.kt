package com.techcubics.albarkahyper.common

import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.requests.SearchAreaRequest


interface IColorsClickListener {
    fun onAddColorClick(color: ColorObject)
    fun onRemoveColor(color: ColorObject)

}

interface ISizesClickListener {
    fun onAddSizeClick(size: Size)
    fun onRemoveSize(size: Size)

}
interface ICategoryClickListener{
    fun onAddCategoryClick(category: Category)
    fun onRemoveCategory(category: Category)
}
interface IFilterClickListener {

    fun onFilterClick(
        from: Int,
        to: Int,
        colors: ArrayList<String>?,
        sizes: ArrayList<String>?,
        categories: ArrayList<Int>?,
        latlng: Latlng?,
        searchAreaRequest: SearchAreaRequest?,
        price : String?
    )
}

interface IFavClickListener {
    fun onFavClick(parent:Int=-1,position: Int,operation:Int)
}


interface IItemClickListener:java.io.Serializable {
    fun onItemClick(id: Int, name: String, logo: String){}
    fun perform(){}
}
interface StoreCategoryOnItemClickListener {
    fun <T> onCategoryClick(
        item:T
    )
}

interface NavigationBarVisibilityListener {
    fun navbarVisibility(isVisible:Int)
}

interface NotificationClickListener{
    fun onClick(type:String,id:Int?)
}