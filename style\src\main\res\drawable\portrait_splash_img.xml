<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="360dp"
    android:height="800dp"
    android:viewportWidth="360"
    android:viewportHeight="800">
  <group>
    <clip-path
        android:pathData="M0,0h360v800h-360z"/>
    <path
        android:pathData="M0,0h360v800h-360z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M39,259h282v282h-282z" />
    <path
        android:pathData="M336.35,732.76C335.54,749.75 328.55,769.41 328.53,784.92C328.56,798.61 332.43,810.99 336.07,823.34C337.08,826.71 338.17,829.99 339.26,833.27C349.22,862.94 360.85,893.91 353.29,927.66C346.51,957.6 324.17,986.05 296.58,1001.21C268.99,1016.37 237.2,1018.23 212.47,1007.49C192.96,998.97 177.62,982.46 171.57,961.33C165.52,940.05 169.2,914.23 182.63,892.75C198.25,867.75 226.19,848.39 232.74,821.04C236.92,803.27 230.53,788.37 230.37,771.46C230.23,761.25 232.19,750.39 237.54,740.63C249.34,719.27 279.12,700.43 301.05,697.72C310.68,696.5 320.19,699.22 326.77,705.14C334.82,712.01 336.82,721.84 336.35,732.76Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="243.51"
            android:startY="1153.32"
            android:endX="191.28"
            android:endY="686.74"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M477.25,838.88C468.2,854.28 444.93,850.27 430.5,854.77C415.05,859.64 403.23,866.11 394.09,874.63C376.05,891.59 376.83,917.52 378.48,940.11C380.29,965.22 379.88,989.9 360.47,1009.21C356.2,1013.4 349.4,1018.37 341.31,1017.74C327.13,1016.67 319.76,1003.36 313.53,992.53C294.03,959.01 259.25,910.79 213.82,930.76C206.84,933.82 199.63,936.95 192.33,935.88C156.35,930.44 163.21,894.89 175.6,870.55C187.99,846.2 205.11,822.26 212.09,795.04C219.51,766.08 204.84,734.5 219.17,706.83C223.32,698.89 229.75,693.83 236.81,692.91C245.71,691.73 254.67,696.13 260.53,699.46C286.51,714.24 288.78,751.05 315.87,764.48C333.41,773.19 354.75,770.28 373.37,766.4C404.15,759.88 431.64,758.56 455.33,782.76C467.03,794.79 475.34,809.72 478.68,825.08C479.75,829.99 479.62,833.97 478.06,837.22C477.82,837.75 477.49,838.35 477.25,838.88Z"
        android:strokeAlpha="0.56"
        android:fillColor="#7FACD6"
        android:fillAlpha="0.56"/>
    <path
        android:pathData="M199.74,863.4C186.85,850.43 174.16,835.94 167.3,818.6C161.08,802.72 159.87,778.23 172.76,764.67C181.06,755.91 195.53,762.48 205.61,762.7C270.23,764.4 301.04,694.27 358.64,678.77C368.74,676 380.2,676.1 389.27,681.72C398.72,687.58 403.88,698.39 406.98,709.08C414.21,734.24 412.48,759.73 410.59,785.53C408.88,808.19 398.7,839.77 414.67,859.46C421.14,867.39 432.89,868.26 441.05,873.71C462.3,888.08 453.61,918.7 439.26,934.97C422.6,953.88 396.41,964.94 371.02,962.94C337.85,960.32 295.69,925.47 263.73,950.01C260.59,952.39 257.59,955.53 253.75,956.06C248.22,956.86 243.18,952.01 241.59,946.7C239.09,938.7 240.12,930.31 238.77,922.11C234.64,898.32 217,880.82 199.74,863.4Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="222.94"
            android:startY="717.05"
            android:endX="434.4"
            android:endY="940.02"
            android:type="linear">
          <item android:offset="0" android:color="#FFDD9ACD"/>
          <item android:offset="1" android:color="#CC2403A8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M309.36,643.47C308.53,643.38 307.76,643.36 307,643.34C305.7,643.39 304.4,643.44 303.19,642.96C300.93,641.99 300.09,639.37 298.15,637.96C296.06,636.38 293.18,635.56 290.51,635.42C277.83,635.15 269.77,648.43 266.37,659.06C263.66,667.41 265.32,675.31 272.5,680.51C278.55,684.85 287.12,686.79 294.45,684.88C294.83,684.81 295.14,684.67 295.52,684.6C297.53,684.03 299.54,683.31 301.68,683.36C304.2,683.49 306.84,684.38 309.45,684.29C311.66,684.26 314.06,683.47 315.92,682.44C320.59,679.79 323.35,674.8 325.11,670.03C327.45,663.81 326.91,656.61 323.11,651.18C319.96,646.76 314.7,644.04 309.36,643.47Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="296.52"
            android:startY="685.35"
            android:endX="294.43"
            android:endY="635.24"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M312.7,616.79C312.4,616.63 312.09,616.62 311.79,616.62C311.18,616.45 310.49,616.36 309.88,616.35C307.36,616.22 304.73,617.23 302.85,618.87C302.85,618.87 302.85,618.87 302.77,618.95C302.69,619.02 302.69,619.02 302.61,619.09L302.53,619.17C301.9,619.77 301.27,620.52 300.79,621.12C299.74,622.7 299.07,624.67 299.01,626.51C298.98,629.95 300.56,633.19 303.4,635.09C304.37,635.79 305.51,636.2 306.65,636.46C306.65,636.46 306.65,636.46 306.72,636.53C310.88,637.92 315.83,636.19 318.24,632.42C320.73,628.58 320.51,623.45 317.41,620.03C316.24,618.47 314.52,617.29 312.7,616.79Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="309.85"
            android:startY="636.96"
            android:endX="308.99"
            android:endY="616.26"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M286.2,623.15C283.38,627.59 290.2,631.95 293.02,627.5C295.91,623.13 289.09,618.78 286.2,623.15Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="289.75"
            android:startY="629.42"
            android:endX="289.42"
            android:endY="621.35"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M14.05,36.46C17.9,18.28 28.82,-2.03 31.56,-18.7C33.93,-33.42 31.97,-47.2 30.24,-60.9C29.74,-64.64 29.15,-68.3 28.56,-71.96C23.11,-105.04 16.1,-139.71 30.11,-175.11C42.61,-206.5 71.5,-234.43 103.67,-247.46C135.85,-260.48 170.2,-258.71 194.77,-244.22C214.15,-232.74 227.67,-213.17 230.44,-189.74C233.18,-166.14 224.72,-138.82 206.58,-117.32C185.48,-92.29 152.19,-74.79 140.39,-46.17C132.8,-27.56 137.02,-10.78 134.23,7.42C132.59,18.42 128.58,29.86 121.15,39.72C104.78,61.29 69.61,78 45.67,78.32C35.16,78.48 25.46,74.43 19.45,67.29C12.05,58.94 11.63,48.14 14.05,36.46Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="187.14"
            android:startY="-404.69"
            android:endX="187.92"
            android:endY="103.11"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M-119.03,-95.23C-106.57,-110.74 -82.21,-103.64 -65.85,-106.77C-48.35,-110.17 -34.47,-115.73 -23.12,-123.82C-0.69,-139.94 3.04,-167.98 5.24,-192.51C7.72,-219.79 12.5,-246.34 36.83,-264.82C42.16,-268.83 50.37,-273.38 58.97,-271.73C74.07,-268.88 79.67,-253.66 84.47,-241.24C99.58,-202.8 128.56,-146.69 181.03,-162.78C189.09,-165.25 197.41,-167.76 205.09,-165.73C242.91,-155.58 229.25,-118.09 211.6,-93.33C193.96,-68.58 171.29,-44.82 158.98,-16.33C145.88,14 156.12,49.78 135.8,77.88C129.93,85.94 122.11,90.63 114.34,90.77C104.53,90.99 95.65,85.18 89.93,80.88C64.54,61.86 68.58,21.92 41.75,4.22C24.39,-7.26 0.88,-6.68 -19.87,-4.72C-54.19,-1.37 -84.04,-3.23 -105.31,-32.14C-115.79,-46.49 -122.12,-63.57 -123.01,-80.52C-123.29,-85.95 -122.46,-90.21 -120.21,-93.53C-119.85,-94.07 -119.39,-94.69 -119.03,-95.23Z"
        android:strokeAlpha="0.56"
        android:fillColor="#7FACD6"
        android:fillAlpha="0.56"/>
    <path
        android:pathData="M184.8,-89.23C196.4,-73.72 207.52,-56.58 211.86,-37.07C215.77,-19.21 212.76,7.32 196.47,20.39C185.98,28.85 171.55,20.03 160.72,18.59C91.38,9.05 45.81,80.94 -18.99,90.77C-30.36,92.54 -42.7,91.07 -51.48,83.93C-60.63,76.49 -64.29,64.22 -65.74,52.33C-69.1,24.36 -62.74,-2.91 -56.16,-30.48C-50.32,-54.7 -33.79,-87.52 -47.52,-110.64C-53.1,-119.96 -65.61,-122.3 -73.44,-129.15C-93.81,-147.17 -79.05,-179.13 -60.71,-194.96C-39.43,-213.34 -9.26,-222.13 17.75,-216.95C53.04,-210.16 92.33,-167.57 131.11,-190.2C134.9,-192.39 138.69,-195.42 142.93,-195.53C149.02,-195.73 153.61,-189.91 154.38,-183.99C155.66,-175.07 153.07,-166.16 153.08,-157.15C153.34,-131.02 169.27,-110.06 184.8,-89.23Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="134"
            android:startY="65.72"
            android:endX="-57.17"
            android:endY="-197.93"
            android:type="linear">
          <item android:offset="0" android:color="#FFDD9ACD"/>
          <item android:offset="1" android:color="#CC2403A8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M32.65,135.49C33.53,135.69 34.35,135.8 35.17,135.91C36.58,136.01 37.99,136.12 39.21,136.78C41.47,138.09 41.91,141.01 43.75,142.77C45.73,144.71 48.68,145.95 51.53,146.41C65.14,148.21 76.16,134.88 81.69,123.84C86.08,115.17 85.69,106.46 78.88,100C73.12,94.61 64.23,91.5 56.01,92.68C55.58,92.7 55.22,92.82 54.8,92.85C52.54,93.22 50.25,93.76 47.95,93.46C45.26,93.02 42.57,91.73 39.75,91.53C37.36,91.29 34.64,91.86 32.45,92.74C26.95,95.04 23.11,100.08 20.37,105.02C16.76,111.43 16.07,119.25 19.21,125.55C21.82,130.69 27.01,134.24 32.65,135.49Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="53.86"
            android:startY="91.92"
            android:endX="50.12"
            android:endY="146.33"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M25.41,163.43C25.71,163.63 26.03,163.68 26.36,163.72C26.98,163.97 27.71,164.15 28.36,164.24C31.05,164.68 34.06,163.9 36.38,162.36C36.38,162.36 36.38,162.36 36.48,162.29C36.58,162.22 36.58,162.22 36.68,162.15L36.77,162.07C37.56,161.51 38.38,160.77 39,160.18C40.4,158.6 41.47,156.56 41.86,154.59C42.5,150.89 41.37,147.21 38.64,144.83C37.72,143.95 36.57,143.38 35.39,142.97C35.39,142.97 35.39,142.97 35.32,142.88C31.09,140.89 25.46,142.16 22.2,145.93C18.84,149.77 18.17,155.32 20.91,159.38C21.9,161.19 23.54,162.67 25.41,163.43Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="32.03"
            android:startY="142.04"
            android:endX="30.49"
            android:endY="164.52"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M55.11,160.3C58.92,155.85 52.35,150.35 48.53,154.8C44.64,159.17 51.22,164.67 55.11,160.3Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="52.38"
            android:startY="153.12"
            android:endX="51.78"
            android:endY="161.88"
            android:type="linear">
          <item android:offset="0" android:color="#FF7FACD6"/>
          <item android:offset="1" android:color="#FFE9B7D4"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
