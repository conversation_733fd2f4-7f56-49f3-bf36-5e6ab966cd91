package com.techcubics.albarkahyper.ui.views.products.details.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.techcubics.data.model.pojo.Category
import com.techcubics.data.model.pojo.ColorObject
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.data.model.pojo.Size
import com.techcubics.data.model.requests.SearchAreaRequest
import com.techcubics.albarkahyper.common.IFilterClickListener
import com.techcubics.albarkahyper.ui.views.auth.fragments.register.FacilityInfo
import com.techcubics.albarkahyper.ui.views.auth.fragments.register.FacilityLocationInfo
import com.techcubics.albarkahyper.ui.views.auth.fragments.register.PersonalData
import com.techcubics.data.model.pojo.ProductDetailsDto

class MainViewModel:ViewModel() {
    private val mutableSeconds = MutableLiveData<Float>()
    val seconds: LiveData<Float> get() = mutableSeconds
    private val mutableSelectedCategoryArrayList = MutableLiveData<ArrayList<Category>>()
    private val mutableSelectedSizeArrayList = MutableLiveData<ArrayList<Size>>()
    private val mutableSelectedColorArrayList = MutableLiveData<ArrayList<ColorObject>>()
    fun setSeconds(sec: Float) {
        mutableSeconds.value = sec
    }
    val refrence = MutableLiveData<IFilterClickListener>()
    private val mutableIsLocationAdded = MutableLiveData<Boolean?>()
    val selectedCategories: MutableLiveData<ArrayList<Category>> get() = mutableSelectedCategoryArrayList
    val selectedSize: MutableLiveData<ArrayList<Size>> get() = mutableSelectedSizeArrayList
    val selectedColor: MutableLiveData<ArrayList<ColorObject>> get() = mutableSelectedColorArrayList
    val isLocationAdded:LiveData<Boolean?> get() = mutableIsLocationAdded
    fun setLocationId(isAdded:Boolean){
        mutableIsLocationAdded.value = isAdded
    }
    fun removeLocationObserver(){
        mutableIsLocationAdded.postValue(null)
    }
    private val _facilityTypeMutableLiveData = MutableLiveData<FacilityInfo?>()
    val facilityTypeLiveData get() = _facilityTypeMutableLiveData
    fun setFacilityInfo(facilityInfo: FacilityInfo){
        _facilityTypeMutableLiveData.postValue(facilityInfo)
    }
    fun removeFacilityObserver(){
        _facilityTypeMutableLiveData.postValue(null)
    }

    private val _facilityLocationMutableLiveData = MutableLiveData<FacilityLocationInfo?>()
    val facilityLocationLiveData get() = _facilityLocationMutableLiveData
    fun setFacilityLocationInfo(facilityLocationInfo: FacilityLocationInfo){
        _facilityLocationMutableLiveData.postValue(facilityLocationInfo)
    }
    fun removeFacilityLocationObserver(){
        _facilityLocationMutableLiveData.postValue(null)
    }

    private val _personalDataMutableLiveData = MutableLiveData<PersonalData?>()
    val personalDataLiveData get() = _personalDataMutableLiveData
    fun setPersonalData(personalData: PersonalData){
        _personalDataMutableLiveData.postValue(personalData)
    }
    fun removePersonalDataObserver(){
        _personalDataMutableLiveData.postValue(null)
    }
    fun setCategoryList(categoryList : ArrayList<Category>){
        mutableSelectedCategoryArrayList.value = categoryList
    }

    fun setColorList(colorList : ArrayList<ColorObject>){
        mutableSelectedColorArrayList.value = colorList
    }

    fun setSizeList(sizeList : ArrayList<Size>){
        mutableSelectedSizeArrayList.value = sizeList
    }
    //////////////////////////////////////////
    private val _mutableLatLng = MutableLiveData<Latlng?>()
    val latLngLiveData:LiveData<Latlng?> get() = _mutableLatLng

    fun setLatLng(latLng: Latlng){
        _mutableLatLng.postValue(latLng)
    }
    fun removeLatLngObserver(){
        _mutableLatLng.postValue(null)
    }
    private val _mutableAllCities = MutableLiveData<String?>()
    val allCitiesLiveData:LiveData<String?> get() = _mutableAllCities
    fun setAllCities(allCities:String){
        _mutableAllCities.postValue(allCities)
    }
    fun removeAllCitiesObserver(){
        _mutableAllCities.postValue(null)
    }
    private val _mutableGovRegion = MutableLiveData<SearchAreaRequest?>()
    val govRegionLiveData:LiveData<SearchAreaRequest?> get() = _mutableGovRegion
    fun setGovRegion(searchAreaRequest: SearchAreaRequest){
        _mutableGovRegion.postValue(searchAreaRequest)
    }
    fun removeGovRegionObserver(){
        _mutableGovRegion.postValue(null)
    }
    private val _updateProductLiveData=MutableLiveData<ProductDetailsDto?>()
    val updateProductLiveData:LiveData<ProductDetailsDto?> get() = _updateProductLiveData
    fun updateProduct(product: ProductDetailsDto){
        _updateProductLiveData.postValue(product)
    }
    fun removeUpdateProductObserver(){
        _updateProductLiveData.postValue(null)
    }
}