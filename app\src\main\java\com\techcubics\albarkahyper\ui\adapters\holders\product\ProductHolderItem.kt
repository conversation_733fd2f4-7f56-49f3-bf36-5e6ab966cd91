package com.techcubics.albarkahyper.ui.adapters.holders.product

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.*
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.FavouriteProduct
import com.techcubics.data.model.pojo.Image
import com.techcubics.data.model.pojo.ProductSearchData
import com.techcubics.albarkahyper.databinding.ItemProductBinding
import com.techcubics.data.model.pojo.ProductDetailsDto
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import org.koin.android.ext.android.get

class ProductHolderItem<T>(
    val binding: ItemProductBinding,
    val context: Context,
    val margin: Int = 0,
    val onFavClickListener: IFavClickListener,
    val onClickHandler: IOnAdapterItemClickHandler
) : RecyclerView.ViewHolder(binding.root) {

    private lateinit var quantityButtonsController: QuantityButtonsController

    private val TAG = "ProductHolderItem"

    //    private lateinit var progressButton: CircleProgressButton
    private val SharedPreferencesManager: SharedPreferencesManager by lazy {
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    private var shopId: Int = -1
    private lateinit var modelType: String
    private var productId = -1
    private var productPrice = 0f
    private var qtyCart = 0
    private var maxQty = 0
    private var minQty = 0
    private var minOrder = 0f
    private var data: T? = null
    private var endpoint = EndPointConstants.add_product
    fun bind(data: T) {
        this.data = data
        binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, 0, 0)
//        progressButton = CircleProgressButton(context)
//        progressButton.initRoundButton(binding.btnOrder)

        when (data) {

            is ProductDetailsDto -> {
                fillByCategory(data)
            }
            is ProductSearchData -> {
                fillByWord(data)
            }
            is FavouriteProduct -> {
                fillByFavorite(data)
            }

        }
        ////////////
        quantityButtonsController = QuantityButtonsController(
            binding.quantityController.root,
            null,
            context,
            maxQty = maxQty,
            minQty = minQty
        )
        quantityButtonsController.setIsItem(
            updateQuantity = ::updateQuantity,
            removeCartItem = ::removeCartItem
        )
        quantityButtonsController.updateQuantity(qtyCart)
        quantityButtonsController.isBtnDelete()
        quantityButtonsController.isBtnIncreaseEnabled()

        if (qtyCart > 0) {
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
        } else {
            binding.btnOrder.root.visibility = View.VISIBLE
            binding.quantityCont.visibility = View.INVISIBLE
        }
        ///////////
//        val maxNumStr=context.getString(com.techcubics.style.R.string.max_num1, maxQty.toString())
//        val numFormatPrice = java.text.NumberFormat.getNumberInstance(Locale.ENGLISH).format(minOrder) +"&#8200;"+
//                context.getString(com.techcubics.style.R.string.currency_name)
//        val minOrderStr =  context.getString(com.techcubics.style.R.string.min_order1, numFormatPrice)
//        val str =  "$maxNumStr$minOrderStr"
//        val styledText: Spanned = Html.fromHtml(str, FROM_HTML_MODE_LEGACY)
//        binding.tvDescription.text = styledText
        binding.maxQty.text = maxQty.toString()
        binding.minQty.text = minQty.toString()

    }

    private fun fillByCategory(data: ProductDetailsDto) {
        shopId = data.shop?.id ?: -1
        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productId ?: -1
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter!! else data.price!!
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart?:0
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        minOrder = data.shop?.districts?.get(0)?.minOrderPrice ?: 0f
        setEndPoint(data.isDiscount)


        binding.tvTitle.text = data.name
        //binding.tvProductDescription.text = data.description
//        binding.tvRating.text = data.rateCount.toString()
//        binding.rateBar.rating = data.rate ?: 0f
//        binding.tvDescription.text = data.description
        data.shop?.logo?.let { Helper.loadImage(context, it, binding.logo) }
        binding.tvBranchName.text = data.shop?.name ?: ""
        binding.tvCurrentPrice.text =
            "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        data.images.ifEmpty { data.images = arrayListOf(Image("", -1, "")) }
        if (data.images.size > 0) {
            Helper.loadImage(context, data.images[0].path, binding.imgThumb)
        }
//        progressButton = CircleProgressButton(context)
//        progressButton.initRoundButton(binding.btnOrder)

        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }

        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
            }
            else -> {}
        }
        binding.btnAddFav.setOnClickListener {
            when (data.isFav ?: false) {
                true -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
                false -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
        }

        binding.branchContainer.setOnClickListener { view ->
            val bundle = Bundle()
            bundle.putInt(Constants.INTENT_ID, data.shop?.id!!)

        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productId, RateTypesEnum.Product.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
//            if (SharedPreferencesManager.isLoggedIn() == "true") {
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
//            updateItem(1)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                data.shop?.id,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                minOrder,
                bindingAdapterPosition
            )
        }
        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }
    }

    private fun setEndPoint(discount: Boolean?) {
        endpoint = if (discount == true) {
            EndPointConstants.add_discount
        } else {
            EndPointConstants.add_product
        }
    }

    private fun fillByWord(data: ProductSearchData) {
        shopId = data.shop?.id!!
        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productID ?: -1
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter!! else data.price!!
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart?:0
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        minOrder = data.shop?.districts?.get(0)?.minOrderPrice ?: 0f
        binding.tvTitle.text = data.name
        setEndPoint(data.isDiscount)
        // binding.tvProductDescription.text = data.description
//       .
//        binding.tvDescription.text = data.description
        Helper.loadImage(context, data.shop!!.logo, binding.logo)
        binding.tvBranchName.text = data.shop!!.name
        binding.tvCurrentPrice.text =
            "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        data.images?.get(0).let { Helper.loadImage(context, it?.path!!, binding.imgThumb) }


        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }

        when (data.isFav) {
            true -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 1
//                    )
//                }
            }
            false -> {
                binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
//                binding.btnAddFav.setOnClickListener {
//                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
//                    onFavClickListener.onFavClick(
//                        position = bindingAdapterPosition,
//                        operation = 2
//                    )
//                }
            }
            else -> {}
        }
        binding.btnAddFav.setOnClickListener {
            when (data.isFav ?: false) {
                true -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_add_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 1
                    )
                }
                false -> {
                    binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
                    onFavClickListener.onFavClick(
                        position = bindingAdapterPosition,
                        operation = 2
                    )
                }
            }
        }

        binding.branchContainer.setOnClickListener { view ->
            val bundle = Bundle()
            bundle.putInt(Constants.INTENT_ID, data.shop!!.id)

        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productID, RateTypesEnum.Product.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
//            if (SharedPreferencesManager.isLoggedIn() == "true") {
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                data.shop!!.id,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                minOrder,
                bindingAdapterPosition
            )
//            updateItem(1)
        }
        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }
    }

    private fun fillByFavorite(data: FavouriteProduct) {
        shopId = data.shopID
        modelType =  if (data.isDiscount == true) RateTypesEnum.Discount.value else RateTypesEnum.Product.value
        productId = if (data.isDiscount == true) data.discount?.get(0)?.discountID ?: -1 else data.productID
        productPrice = if (data.isDiscount == true) data.discount?.get(0)?.priceAfter ?: 0f else data.price
        qtyCart = if (data.isDiscount == true) data.discount?.get(0)?.quantityCart?:0 else data.qtyCart
        maxQty = if (data.isDiscount == true) data.discount?.get(0)?.maxQty?:0 else data.maxQty
        minQty = if (data.isDiscount == true) data.discount?.get(0)?.minQty?:0 else data.minQty

        minOrder = data.shopDistricts?.get(0)?.minOrderPrice ?: 0f
        binding.tvTitle.text = data.name
        setEndPoint(data.isDiscount)
        //  binding.tvProductDescription.text = data.description
//        binding.tvRating.text = data.rate_count.toString()
//        binding.rateBar.rating = data.rate
//        binding.tvDescription.text = data.description
        Helper.loadImage(context, data.shopLogo, binding.logo)
        binding.tvBranchName.text = data.shopName
        binding.tvCurrentPrice.text =
            "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        if (data.Images.size > 0) {
            Helper.loadImage(context, data.Images[0].path, binding.imgThumb)
        }

        if (SharedPreferencesManager.isLoggedIn().equals("false")) {
            binding.btnAddFav.visibility = View.GONE
        }

        binding.btnAddFav.setImageResource(com.techcubics.style.R.drawable.ic_remove_favorite)
        binding.btnAddFav.setOnClickListener {
            onFavClickListener.onFavClick(
                position = bindingAdapterPosition,
                operation = 1
            )
        }

        binding.branchContainer.setOnClickListener { view ->
            val bundle = Bundle()
            bundle.putInt(Constants.INTENT_ID, data.shopID)

        }
        itemView.setOnClickListener {
            onClickHandler.onItemClicked(data.productID, RateTypesEnum.Product.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
//            if (SharedPreferencesManager.isLoggedIn() == "true") {
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
            quantityButtonsController.updateQuantity(minQty)
            quantityButtonsController.isBtnIncreaseEnabled()
            onClickHandler.addToCart(
                endpoint,
                data.shopID,
                modelType,
                productId,
                minQty,
                productPrice,
                maxQty,
                minQty,
                minOrder,
                bindingAdapterPosition
            )
//            updateItem(1)
        }
        if (data.isDiscount == false) {
            binding.priceDiscountLayout.visibility = View.GONE
            binding.tvCurrentPrice.visibility =View.VISIBLE
            binding.tvDiscountTag.visibility = View.GONE
            binding.tvCurrentPrice.text =
                "${data.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        } else {
            binding.tvCurrentPrice.visibility = View.GONE
            binding.tvDiscountTag.visibility = View.VISIBLE
            binding.priceDiscountLayout.visibility = View.VISIBLE
            binding.tvBeforeDiscountPrice.text =
                "${data.discount?.get(0)?.priceBefore} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            binding.tvAfterDiscountPrice.text =
                "${data.discount?.get(0)?.priceAfter} ${context.getString(com.techcubics.style.R.string.currency_name)}"
            //binding.tvPercentage.text = "${data.percent}"
        }

    }

    //    @Suppress("UNCHECKED_CAST")
//    private fun updateItem(qty: Int) {
//        when(data){
//            is ProductDetailsDto->{
//                (bindingAdapter as ProductsAdapter<ProductDetailsDto>)
//                    .items[absoluteAdapterPosition].qtyCart = qty.toString()
//            }
//            is ProductSearchData -> {
//                (bindingAdapter as ProductsAdapter<ProductSearchData>)
//                    .items[absoluteAdapterPosition].qtyCart = qty
//            }
//            is FavouriteProduct -> {
//                (bindingAdapter as ProductsAdapter<FavouriteProduct>)
//                    .items[absoluteAdapterPosition].qtyCart = qty
//            }
//        }
//        bindingAdapter?.notifyItemChanged(absoluteAdapterPosition)
//    }
    private fun updateQuantity(
        qty: Int,
    ) {
        onClickHandler.addToCart(
            endpoint,
            shopId,
            modelType,
            productId,
            qty,
            productPrice,
            maxQty,
            minQty,
            minOrder,
            bindingAdapterPosition
        )
//        updateItem(qty)
    }

    private fun removeCartItem() {
        binding.btnOrder.root.visibility = View.VISIBLE
        binding.quantityCont.visibility = View.INVISIBLE
        onClickHandler.removeItemFromCart(
            modelType,
            productId,
            productPrice,
            bindingAdapterPosition
        )
//        updateItem(0)
    }
}

