package com.techcubics.shared.constants

object Constants {
    const val MAIN_CATEGORY: String="main_cat"
    const val MIN_ORDER: String="min_order"
    const val LOGIN_STATE: String = "login_state"
    const val PARENT_INDEX: String = "parent_index"
    const val INTENT_LIST_OF_PRODUCTS: String = "list_of_products"
    const val INTENT_LIST_OF_CATEGORIES: String = "list_of_cats"
    const val YOUTUBE_SECONDS:String  ="sec"
    const val PHOTO = "photo"
    const val USER = "USER"
    const val LATLNG = "CURRENT_LATLNG"
    const val CHOOSEN_LATLNG = "CHOOSEN_LATLNG"
    const val YOUTUBE_VID_ID:String  ="vid_id"
    const val SERVER_ERROR: String = "server_error"
    const val BASE_API_URL = "https://dashboard.albarkahyper.com/api/"
    const val TECHCUBIC_WEBSITE_URL = "http://www.techcubics.com"
    const val TECHCUBIC_FACEBOOK_URL = "https://www.facebook.com/TechCubics"
    const val TECHCUBIC_WHATSAPP_URL = "https://api.whatsapp.com/send?phone=+201090285513"
    const val INTENT_PAGE_TYPE = "page_type"
    const val INTENT_NOTE = "note"
    const val INTENT_ID = "id"
    const val INTENT_SLUG = "slug"
    const val INTENT_NAME = "name"
    const val INTENT_WORD = "word"
    const val INTENT_PLACE_ID = "shop_id"
    const val INTENT_LATITUDE = "latitude"
    const val INTENT_LONGITUDE = "longitude"
    const val INTENT_TYPE_ID = "type_id"
    const val SHOP_ID = "shop_id"
    const val ITEM_TYPE = "type"
    const val INTENT_URL = "url"
    const val ORDER_ID = "order_id"
    const val VERFICATION_ID = "verificationId"
    const val MOBILE = "mobile"
    const val ADD = "add"
    const val JOIN = "join"
    const val UPDATE = "update"
    const val FRAGMENT_TAG = "fragTag"
    const val LOCATION_ID = "location_id"
    const val BASE_STORE_SHARE_URL="https://albarkahyper.com/#/shop/"
    const val BASE_PRODUCT_SHARE_URL="https://albarkahyper.com/#/product/"
    const val BASE_DISCOUNT_SHARE_URL="https://albarkahyper.com/#/discounts/"
    const val BASE_OFFER_SHARE_URL="https://albarkahyper.com/#/offers/"
    const val BASE_SAVE_SHARE_URL="https://albarkahyper.com/#/saves/"
    const val CART = "Cart"
    const val langaueList = "LANGUAGEList"
    const val LANGUAGE = "LANGUAGE"
    const val DETECT_LOCATION: String = "detect_location"
    const val ALL_CITIES: String = "all_cities"
    const val ALL_AREAS: String = "all_areas"
}
object EndPointConstants {
    const val register = "register"
    const val login = "login"
    const val Social = "login/social"
    const val Forgetget_password = "forget-password"
    const val Forget_password_web = "forget-password-web"
    const val Confirm_password = "confirm-password"
    const val reset_password = "reset-password"
    const val profile = "profile"
    const val update_password = "update-password"
    const val update_profile = "update-profile"
    const val firebase_token = "firebase-token"
    const val logout = "logout"
    const val languages = "languages"
    const val home = "home"
    const val pages = "pages"
    const val offers = "home/offers"
    const val saves = "home/saves"
    const val p = "home/products/discounts"
    const val add_favourite = "add-favourite"
    const val myFavourites = "myFavourites"
    const val branch_types = "home/branch-types"
    const val contact_us = "home/contact-us"
    const val categories = "home/categories"
    const val sub_categories = "home/sub-categories"
    const val add_rate = "add-rate"
    const val social_media = "social-media"
    const val settings = "setting"


    const val product_colors = "product-colors"
    const val product_colors_sizes = "product-colors-sizes"
    const val product_details = "product/{id}"
    const val add_product = "add-product"
    const val add_save = "add-save"
    const val add_offer = "add-offer"
    const val add_discount = "add-discount"
    const val cart = "cart"
    const val remove_cart_item = "cart/remove-model-type"
    const val submit_coupon = "cart/coupon"
    const val select_location = "cart/select-location"
    const val location = "location"
    const val shops_search="home/shop-search"
    const val product_search="home/product-search"
    const val product_by_category="home/product-by-category"
    const val product_by_sub_category="home/product-by-sub-category"
    const val most_wanted_products="home/most-wanted-products"
    const val furniture_nearby="home/shop-nearby"
    const val furnitures = "home/search/shops"
    const val address="address"
    const val countries = "countries"
    const val furniture = "shop"
    const val details_by_qrcode = "shop/details-by-qrcode"
    const val ticket = "ticket"
    const val ticket_send = "ticket/send"
    const val checkout = "checkout"
    const val discount_details = "home/discounts/{id}"
    const val save_details = "home/saves/{id}"
    const val offer_details = "home/offers/{id}"
    const val prescription_add = "prescription/add"

    const val chats = "chats"
    const val message_send = "message/send"
    const val room_messages = "message"
    const val live_chats = "live-chat/chats"
    const val live_message_send = "live-chat"
    const val delivery_areas = "home/delivery-area/{furId}"
    const val governorate_delivery_areas = "home/governorate-areas/{deliveryId}"
    const val region_delivery_area = "home/region/{govId}/{deliveryId}"
    const val rates = "rates"

    const val governorate_by_country = "governorate-by-country"
    const val region_by_governerate = "region-by-governorate"
    const val store_location = "location/store"
    const val update_location = "location/update"
    const val delete_location = "location/delete"
    const val show_location = "location/show"
    const val join_us = "home/join-us"
    const val store_purchase = "listing/store"
    const val purchases_list = "listing"
    const val show_purchase = "listing/show"
    const val update_purchase = "listing/update"
    const val delete_purchase = "listing/delete"
    const val purchase_mark_buying = "listing/buying"
    const val previous_order = "order/previous"
    const val current_order = "order/current"
    const val get_share_link="get-share-link"
    const val notifications="notifications"
    const val products_by_subcategory="shop/{shopID}/subcategory/{subCatId}"

    const val branchTypesCategories="home/categories-by-branchType"
    const val branch_type_nearby = "home/branch-type-nearby"
    const val banners="banners"
    const val banners_home="banner-homes"
    const val remove_all_cart = "cart/remove-all-cart"
    const val read_notifications="read-notification"


}