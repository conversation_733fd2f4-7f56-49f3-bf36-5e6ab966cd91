package com.techcubics.albarkahyper.ui.adapters.profile

import android.app.Dialog
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.appcompat.widget.AppCompatButton
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.databinding.ItemShoptypeBinding
import com.techcubics.albarkahyper.ui.adapters.holders.profile.BranchHolderItem

class BranchesAdapter(
    private val context: Context,
    private val listOfBranchesType: List<StoreTypes>,
    private val popupMenuItemLiveData: MutableLiveData<ArrayList<StoreTypes?>>,
    private val yesbtn: AppCompatButton,
    private val nobtn: AppCompatButton,
    private val dialog: Dialog
) :
    RecyclerView.Adapter<BranchHolderItem>() {
    val tempShopTypeList: ArrayList<StoreTypes?> = arrayListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BranchHolderItem {
        val itemBinding =
            ItemShoptypeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BranchHolderItem(itemBinding)
    }


    override fun onBindViewHolder(holder: BranchHolderItem, position: Int) {

        holder.itemName.text = listOfBranchesType.get(position).name
        Helper.loadImage(context, listOfBranchesType.get(position).image, holder.image)

        holder.itemView.isClickable = false

        holder.itemChecked.setOnClickListener {
            if (holder.itemChecked.isChecked) {
                tempShopTypeList.add(listOfBranchesType.get(position))
            } else {
                tempShopTypeList.remove(listOfBranchesType.get(position))
            }

        }

        yesbtn.setOnClickListener {
            Log.i("clothes", tempShopTypeList.toString())
            popupMenuItemLiveData.postValue(tempShopTypeList)
            dialog.dismiss()
        }
        nobtn.setOnClickListener {
            dialog.dismiss()
        }
    }

    override fun getItemCount() = listOfBranchesType.size


}