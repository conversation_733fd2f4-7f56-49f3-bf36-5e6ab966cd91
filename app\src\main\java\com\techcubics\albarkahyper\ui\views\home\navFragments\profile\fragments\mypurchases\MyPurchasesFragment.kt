package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mypurchases

import android.app.AlertDialog
import android.app.DatePickerDialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Mypurchase
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IRefreshListListenerWithCalender
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.databinding.FragmentMyPurchasesBinding
import com.techcubics.albarkahyper.ui.adapters.profile.MyPurchaseAdapter
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import com.techcubics.shared.constants.Constants
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*


class MyPurchasesFragment : Fragment(), IRefreshListListenerWithCalender {

    private lateinit var fragmentMyPurchasesBinding: FragmentMyPurchasesBinding

    private val profileViewModel by viewModel<ProfileViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog
    private lateinit var adapter: MyPurchaseAdapter
    private lateinit var currentDate: LocalDate
    private var day = 0
    private var month = 0
    private var year = 0
    private var selectedDate = ""
    private var TAG = ""

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        fragmentMyPurchasesBinding = FragmentMyPurchasesBinding.inflate(inflater, container, false)
        Log.i("here", "maFrag")
        initViews()
        observeViews()
        observeAdapters()
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        return fragmentMyPurchasesBinding.root
    }

    private fun observeViews() {
        authViewModel.checkAuthorizationMutableLiveData.observe(viewLifecycleOwner) {

            if (it != null) {
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    findNavController().navigate(R.id.goToLogin)
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.GONE
                    fragmentMyPurchasesBinding.serverErrorLayout.layout.visibility = View.VISIBLE
                    fragmentMyPurchasesBinding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    fragmentMyPurchasesBinding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                }

            }
        }

        profileViewModel.listPurchasesResponseMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(
                View.GONE,
                fragmentMyPurchasesBinding.actionLoadingAnimation.root
            )
            if (it != null) {
                if (it.status!!) {
                    if (it.data.isNullOrEmpty()) {
                        //start with current date
//                        calenderStartDate(day, month, year)
//                        initCalender()
                        fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.GONE
                        fragmentMyPurchasesBinding.placeholderLayout.layout.visibility =
                            View.VISIBLE
                        fragmentMyPurchasesBinding.placeholderLayout.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                        fragmentMyPurchasesBinding.placeholderLayout.tvMessage.text =
                            getString(com.techcubics.style.R.string.message_empty_list_general)
                    } else {
                        //start with oldest date in stored list in api
                        fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.VISIBLE
                        fragmentMyPurchasesBinding.placeholderLayout.layout.visibility = View.GONE
                        //getOldestDate(it.data!!)
                        adapter = MyPurchaseAdapter(
                            requireContext(),
                            it.data ?: arrayListOf(),
                            profileViewModel,
                            TAG
                        )
                        fragmentMyPurchasesBinding.mypurchasesRV.adapter = adapter

                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.GONE
                    fragmentMyPurchasesBinding.serverErrorLayout.layout.visibility =
                        View.VISIBLE
                    fragmentMyPurchasesBinding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    fragmentMyPurchasesBinding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                } else {
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.listPurchasesResponseMutableLiveData.value = null
            }
        }

        profileViewModel.listPurchasesByDateResponseMutableLiveData.observe(viewLifecycleOwner) {
            Helper.loadingAnimationVisibility(
                View.GONE,
                fragmentMyPurchasesBinding.actionLoadingAnimation.root
            )

            if (it != null) {
                if (it.status!!) {
                    if (it.data.isNullOrEmpty()) {
                        fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.GONE
                        fragmentMyPurchasesBinding.placeholderLayout.layout.visibility =
                            View.VISIBLE
                        fragmentMyPurchasesBinding.placeholderLayout.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                        fragmentMyPurchasesBinding.placeholderLayout.tvMessage.text =
                            getString(com.techcubics.style.R.string.message_empty_list_general)
                    } else {
                        //start with oldest date in stored list in api
                        fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.VISIBLE
                        fragmentMyPurchasesBinding.placeholderLayout.layout.visibility = View.GONE

                        adapter = MyPurchaseAdapter(
                            requireContext(),
                            it.data ?: arrayListOf(),
                            profileViewModel,TAG
                        )
                        fragmentMyPurchasesBinding.mypurchasesRV.adapter = adapter

                    }
                } else if (it.message!!.contains(Constants.SERVER_ERROR)) {
                    fragmentMyPurchasesBinding.mypurchasesRV.visibility = View.GONE
                    fragmentMyPurchasesBinding.serverErrorLayout.layout.visibility =
                        View.VISIBLE
                    fragmentMyPurchasesBinding.serverErrorLayout.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                    fragmentMyPurchasesBinding.serverErrorLayout.tvMessage.setText(com.techcubics.style.R.string.server_error)
                } else {
                    bottomSheetAlertDialog.showDialog(it.message!!)
                }
                profileViewModel.listPurchasesByDateResponseMutableLiveData.value = null
            }
        }
    }

    private fun getOldestDate(data: ArrayList<Mypurchase>) {
        val oldestDateArrayList = arrayListOf<String>()
        for (oldest in data) {
            oldestDateArrayList.add(oldest.date)
        }

        val myFormat = "yyyy-MM-dd"
        val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern(myFormat, Locale.US)
        val dateList: MutableList<LocalDate> = ArrayList()
        for (ds in oldestDateArrayList) {
            dateList.add(LocalDate.parse(ds, dateFormatter))
        }
        Collections.sort(dateList)
        Log.i(
            "market",
            "" + dateList.get(0).dayOfMonth + "-" + dateList.get(0).monthValue + "-" + dateList.get(
                0
            ).year
        )
//        calenderStartDate(
//            dateList.get(0).dayOfMonth,
//            dateList.get(0).monthValue,
//            dateList.get(0).year
//        )
        initCalender()
    }

    private fun observeAdapters() {
        profileViewModel.popupMenuItemLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                profileViewModel.deletePurchase(it.toInt())
                profileViewModel.popupMenuItemLiveData.value = null
                onItemClick(selectedDate, TAG)
            }
        }

        profileViewModel.deleteLocationLocationMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    onItemClick(selectedDate, TAG)
                    Helper.loadingAnimationVisibility(
                        View.GONE,
                        fragmentMyPurchasesBinding.actionLoadingAnimation.root
                    )
                }
            }
        }

        profileViewModel.showPurchaseMutableLiveData.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.status!!) {
                    activity?.supportFragmentManager?.let { it1 ->
                        AddNewPurchaseBottomSheet(this, it.data,TAG).show(
                            it1,
                            "AddNewPurchase"
                        )
                    }
                }
            }

        }
    }

    private fun initViews() {

//        currentDate = LocalDate.now()
//        day = currentDate.dayOfMonth
//        month = currentDate.monthValue
//        year = currentDate.year
//        selectedDate = dateFormate()

        fragmentMyPurchasesBinding.toolbar.tvTitle.text =
            getString(com.techcubics.style.R.string.mypurchases)
        fragmentMyPurchasesBinding.toolbar.mainToolbar.setNavigationOnClickListener {
            requireActivity().onBackPressed()
        }
        Helper.loadingAnimationVisibility(
            View.VISIBLE,
            fragmentMyPurchasesBinding.actionLoadingAnimation.root
        )
        checkAutherization()
        TAG = "alllist"
        onItemClick("",TAG)
        fragmentMyPurchasesBinding.fab.setOnClickListener {
            activity?.supportFragmentManager?.let { it1 ->
                AddNewPurchaseBottomSheet(this, null, TAG).show(
                    it1,
                    "AddNewPurchase"
                )
            }
        }

        initCalender()

        val swipeGesture = object : SwipeGesture(requireContext()) {
            override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

                when (direction) {
                    ItemTouchHelper.LEFT -> {
                        Log.i("market", "delete item")
                        showConfirmDeleteDialog(viewHolder.position)


                    }
                    ItemTouchHelper.RIGHT -> {
                        Log.i("market", "edit item")
                        adapter.editItem(viewHolder.position, this@MyPurchasesFragment)
                    }
                }
            }
        }

        val touchHelper = ItemTouchHelper(swipeGesture)
        touchHelper.attachToRecyclerView(fragmentMyPurchasesBinding.mypurchasesRV)


    }


//    private fun calenderStartDate(day: Int, month: Int, year: Int) {
//        fragmentMyPurchasesBinding.dayScrollCalender.setStartDate(day, month, year)
//    }
//
//    private fun initCalender() {
//        fragmentMyPurchasesBinding.dayScrollCalender.visibility = View.VISIBLE
//        fragmentMyPurchasesBinding.dayScrollCalender.getSelectedDate {
//            selectedDate = choosenDateFormat(date = it!!)
//            onItemClick(selectedDate)
//        }
//    }
//
//    private fun highlightCurrentDate() {
//        (((fragmentMyPurchasesBinding.dayScrollCalender[0]
//                as LinearLayout)[1]
//                as LinearLayout)[0] as RecyclerView).post {
//            ((((fragmentMyPurchasesBinding.dayScrollCalender[0]
//                    as LinearLayout)[1]
//                    as LinearLayout)[0] as RecyclerView)
//                .findViewHolderForAdapterPosition(0)
//                ?.itemView as LinearLayout)[1].performClick()
//        }
//    }

    private fun showConfirmDeleteDialog(position: Int) {
        val builder = AlertDialog.Builder(context)
            .create()
        val view = layoutInflater.inflate(R.layout.dialog_confirm_action, null)
        val noButton = view.findViewById<Button>(R.id.no)
        val yesButton = view.findViewById<Button>(R.id.yes)

        builder.setView(view)
        noButton.setOnClickListener {
            onItemClick(selectedDate, TAG)
            builder.dismiss()
        }
        yesButton.setOnClickListener {
            adapter.deleteItem(
                position,
                this,
                fragmentMyPurchasesBinding.actionLoadingAnimation.root
            )
            builder.dismiss()
            Helper.loadingAnimationVisibility(
                View.VISIBLE,
                fragmentMyPurchasesBinding.actionLoadingAnimation.root
            )
        }
        builder.setCanceledOnTouchOutside(false)
        builder.show()
    }

    private fun checkAutherization() {
        if (sharedPreferencesManager.isLoggedIn() == "true") {
            authViewModel.checkAuthorization()
        }
    }

//    private fun dateFormate(): String {
//        val myCalender = Calendar.getInstance()
//        myCalender.set(Calendar.YEAR, year)
//        myCalender.set(Calendar.MONTH, month)
//        myCalender.set(Calendar.DAY_OF_MONTH, day)
//        val myFormat = "yyyy-MM-dd"
//        val sdf = SimpleDateFormat(myFormat, Locale.UK)
//        return sdf.format(myCalender.time)
//    }
//
//    private fun choosenDateFormat(date: Date): String {
//        val myFormat = "yyyy-MM-dd"
//        val sdf = SimpleDateFormat(myFormat, Locale.UK)
//        Log.i("market", "clicked : " + sdf.format(date))
//        return sdf.format(date)
//    }

    private fun initCalender() {

        val myCalender = Calendar.getInstance()
        val datePicker = DatePickerDialog.OnDateSetListener { _, year, month, day ->
            myCalender.set(Calendar.YEAR, year)
            myCalender.set(Calendar.MONTH, month)
            myCalender.set(Calendar.DAY_OF_MONTH, day)
            val myFormat = "yyyy-MM-dd"
            val sdf = SimpleDateFormat(myFormat, Locale.UK)
            TAG = "Filter"
            selectedDate = sdf.format(myCalender.time)
            onItemClick(selectedDate,TAG)
        }

        fragmentMyPurchasesBinding.btnFilter.setOnClickListener {
            DatePickerDialog(
                requireContext(),
                com.techcubics.style.R.style.DialogTheme,
                datePicker,
                myCalender.get(Calendar.YEAR),
                myCalender.get(Calendar.MONTH),
                myCalender.get(Calendar.DAY_OF_MONTH)
            ).show()
        }
    }

    override fun onItemClick(date: String?, TAG: String) {
        if (date != null) {
            if(TAG.equals("Filter")){
                profileViewModel.listPurchasesByDate(date)
            }else if(TAG.equals("alllist")){
                profileViewModel.listPurchases()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }


}