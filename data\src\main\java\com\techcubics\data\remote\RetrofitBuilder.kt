package com.techcubics.data.remote

import android.icu.util.ULocale.getLanguage
import com.google.gson.GsonBuilder
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.shared.constants.Constants
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit

class RetrofitBuilder(private val sharedPreferencesManager: SharedPreferencesManager) {

    // var mContext: Context? = null
    val BASE_URL = Constants.BASE_API_URL
    var retrofit: Retrofit? = null

    fun start(): Retrofit? {
        if (retrofit == null) {
            val logging = HttpLoggingInterceptor()
            // set your desired log level
            logging.level = HttpLoggingInterceptor.Level.BODY


            val okHttpClient = OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .addInterceptor(logging)
                .addInterceptor { chain ->
                    val original = chain.request()
                    val request = original.newBuilder()
                        .addHeader("Authorization", "Bearer " + sharedPreferencesManager.getToken())
                        .addHeader("Accept", "application/json")
                       .addHeader("Api-Version", "v1")
                        .addHeader("country-id", sharedPreferencesManager.getCountryID())
                        .addHeader("Accept-Language", sharedPreferencesManager.getLanguage())
                        .method(original.method, original.body)
                        .build()
                    chain.proceed(request)
                }
                .build()

            val gson = GsonBuilder()
                .setLenient()
                .create()

              retrofit = Retrofit.Builder().apply {
                baseUrl(BASE_URL)
                addConverterFactory(GsonConverterFactory.create(gson))
                addConverterFactory(ScalarsConverterFactory.create())
            }.client(okHttpClient).build()


        }
        return retrofit
    }

    fun getBindObject(): EndPoints? {
        return start()?.create(EndPoints::class.java)
    }

}