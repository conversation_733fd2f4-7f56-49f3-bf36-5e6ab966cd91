<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/btn_remove"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:background="@drawable/bg_gray_ripple"
            android:contentDescription="@string/content_desc_remove_button"
            android:padding="10dp"
            android:src="@drawable/ic_remove"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/color_gray_5" />

        <TextView
            android:id="@+id/product_title"
            style="@style/label_cart_item_product_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:textStyle="bold"
            android:textAlignment="viewStart"
            android:text="Sofas and arm chairs"
            app:layout_constraintEnd_toStartOf="@id/btn_remove"
            app:layout_constraintStart_toStartOf="@+id/guideline"
            app:layout_constraintTop_toTopOf="@id/btn_remove" />
        <TextView
            android:id="@+id/product_status_note"
            style="@style/label_item_area"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textAlignment="viewStart"
            android:visibility="gone"
            android:layout_marginBottom="10dp"
            android:text="@string/product_status_note"
            android:textColor="@color/color_red_3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/product_title"
            app:layout_constraintTop_toBottomOf="@id/product_title" />
        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/product_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:backgroundTint="@color/color_blue_2"
                android:contentDescription="@string/content_desc_product_image"
                android:scaleType="centerCrop"
                android:src="@drawable/portrait_placeholder" />

        </androidx.cardview.widget.CardView>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.25" />

        <TextView
            android:id="@+id/color_title"
            style="@style/label_cart_item_small_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/color_title"
            app:layout_constraintStart_toStartOf="@id/product_title"
            app:layout_constraintTop_toBottomOf="@id/product_status_note" />

        <TextView
            android:id="@+id/color"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:layout_marginStart="4dp"
            android:background="@drawable/circle_gray"
            android:textAlignment="viewStart"
            app:layout_constraintBottom_toBottomOf="@id/color_title"
            app:layout_constraintStart_toEndOf="@id/color_title"
            app:layout_constraintTop_toTopOf="@id/color_title" />

        <TextView
            android:id="@+id/size_title"
            style="@style/label_cart_item_small_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:text="@string/size_title"
            app:layout_constraintBottom_toBottomOf="@id/color_title"
            app:layout_constraintStart_toEndOf="@id/color"
            app:layout_constraintTop_toTopOf="@id/color_title" />

        <TextView
            android:id="@+id/size"
            style="@style/label_cart_item_small_title_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textAlignment="viewStart"
            app:layout_constraintBottom_toBottomOf="@id/size_title"
            app:layout_constraintStart_toEndOf="@id/size_title"
            app:layout_constraintTop_toTopOf="@id/size_title"
            tools:text="small" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/quantity_cont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/bg_cart_quantity"
            android:padding="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/product_title"
            app:layout_constraintTop_toBottomOf="@id/size">


            <TextView
                android:id="@+id/product_price"
                style="@style/label_cart_item_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/qty_cont"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/qty_cont"
                tools:text="5000 جنيه" />

            <androidx.cardview.widget.CardView
                android:id="@+id/qty_cont"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                app:cardCornerRadius="6dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toEndOf="@id/product_price"
                app:layout_constraintTop_toTopOf="parent">
                <include
                    android:id="@+id/product_qty_cart_container"
                    layout="@layout/include_input_quantity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </androidx.cardview.widget.CardView>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/min_order_note"
            style="@style/label_item_area"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textAlignment="viewStart"
            android:layout_marginBottom="10dp"
            android:text="@string/min_order_number"
            android:textColor="@color/color_red_3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/guideline"
            app:layout_constraintTop_toBottomOf="@id/quantity_cont" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>