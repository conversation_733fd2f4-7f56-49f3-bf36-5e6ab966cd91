package com.techcubics.albarkahyper.ui.adapters.profile

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.jakewharton.processphoenix.ProcessPhoenix
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Countries
import com.techcubics.data.model.pojo.DeliveryAreaId
import com.techcubics.albarkahyper.MainActivity
import com.techcubics.shared.constants.Constants
import com.techcubics.albarkahyper.databinding.ItemCountryBinding
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.ui.adapters.holders.profile.CountryHolderItem
import com.techcubics.style.R
import org.koin.java.KoinJavaComponent
import java.util.*


class CountryAdapter(
    private val context: Context,
    private val listOfCountries: List<Any>?,
    private val popupMenuItemLiveData: MutableLiveData<Any?>,
    private val progressBar: ProgressBar,
    private val country_rv: RecyclerView,
    private val TAG: String,
    private val dialog: Dialog
) :
    RecyclerView.Adapter<CountryHolderItem>() {
    private lateinit var list_Join: List<Countries>
    private lateinit var list_Add: List<DeliveryAreaId>
    private lateinit var list_language : List<String>
    private val sharedPreferencesManager: SharedPreferencesManager by KoinJavaComponent.inject(
        SharedPreferencesManager::class.java
    )
    val maincontext = context as MainActivity
    init {
        if (TAG.equals("join") || TAG.equals("profile")) {
            list_Join = listOfCountries as List<Countries>
            popupMenuItemLiveData as MutableLiveData<Countries>
        }
        else if(TAG.equals(Constants.LANGUAGE)){
            list_language = listOfCountries as List<String>
            popupMenuItemLiveData as MutableLiveData<String>
        }
        else {
            list_Add = listOfCountries as List<DeliveryAreaId>
            popupMenuItemLiveData as MutableLiveData<DeliveryAreaId>
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CountryHolderItem {

        progressBar.visibility = View.GONE
        country_rv.visibility = View.VISIBLE
        val itemBinding =
            ItemCountryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return CountryHolderItem(itemBinding)
    }


    override fun onBindViewHolder(holder: CountryHolderItem, position: Int) {

        if (TAG.equals("join")) {
            holder.itemName.text = list_Join?.get(position)?.name
            Helper.loadImage(context,list_Join?.get(position)?.flag!!,holder.image)

            holder.itemView.setOnClickListener {
                popupMenuItemLiveData.value = list_Join?.get(position)
                dialog.dismiss()
            }
        } else if(TAG.equals("profile")){
            holder.itemName.text = list_Join?.get(position)?.name
            Helper.loadImage(context,list_Join?.get(position)?.flag!!,holder.image)
            holder.itemView.setOnClickListener {
                popupMenuItemLiveData.value = list_Join?.get(position)
                dialog.dismiss()
            }
        }
        else if(TAG.equals(Constants.LANGUAGE)){
            when(list_language.get(position)){
                "ar" -> {
                    holder.itemName.text = context.getString(R.string.arabic)
                    holder.image.setImageResource(com.hbb20.R.drawable.flag_egypt)
                }
                "en" -> {
                    holder.itemName.text = context.getString(R.string.english)
                    holder.image.setImageResource(com.hbb20.R.drawable.flag_united_kingdom)
                }
            }
            holder.itemView.setOnClickListener {
                popupMenuItemLiveData.value = list_language.get(position)
                dialog.dismiss()
                chooseLanguage(list_language.get(position))
            }
        }
        else{
            holder.itemName.text = list_Add.get(position)?.country?.name
            Helper.loadImage(context,list_Add.get(position)?.country?.flag!!,holder.image)

            holder.itemView.setOnClickListener {
                popupMenuItemLiveData.value = list_Add?.get(position)
                dialog.dismiss()
            }
        }}

    override fun getItemCount(): Int {
        if (TAG.equals("join") || TAG.equals("profile"))
            return list_Join.size
        else if(TAG.equals(Constants.LANGUAGE))
            return list_language.size
        else
            return list_Add.size
    }

    private fun chooseLanguage(newValue: String){
        when(newValue) {
            "ar" -> {
                if(!sharedPreferencesManager.getLanguage().equals("ar")){
                    maincontext.updateLocale(Locale("ar"))
                }
            }
            "en" -> {
                if(!sharedPreferencesManager.getLanguage().equals("en")){
                    maincontext.updateLocale(Locale("en"))
                }
            }
            "de" -> {
            }
            "tr" -> {

            }
        }

    }

    private fun relaunchApp(context: Activity, language: String) {
        sharedPreferencesManager.saveLanguage(language)
        ProcessPhoenix.triggerRebirth(context)
    }

}