package com.techcubics.data.repos.auth

import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.ForgetPasswordConfirmResponseData
import com.techcubics.data.model.pojo.ForgetPasswordResponseData
import com.techcubics.data.model.pojo.SettingData
import com.techcubics.data.model.pojo.User
import com.techcubics.data.model.requests.auth.*
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.remote.RetrofitBuilder
import com.techcubics.data.repos.RepositoryResponse

class AuthRepoImpl(private val retrofitBuilder: RetrofitBuilder,private val sharedPreferencesManager: SharedPreferencesManager) : AuthRepo, RepositoryResponse {

    override suspend fun checkAutherization(): BaseResponse<User>? {

        try {

            val result = retrofitBuilder.getBindObject()?.checkAutherization()
            return baseResponse(result)

        }  catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun login(request: LoginRequest): BaseResponse<User>? {
        try {

            val result = retrofitBuilder.getBindObject()
                ?.normalloginCall(request)
            if(result?.body()?.data != null){
                sharedPreferencesManager.saveToken(result.body()?.data?.token)
                sharedPreferencesManager.saveUserID(result.body()?.data?.userID)
                sharedPreferencesManager.saveID(result.body()?.data?.id)
            }
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun register(request: RegisterRequest): BaseResponse<User>? {
        try {
            val result = retrofitBuilder.getBindObject()?.registerCall(request)
            if(result?.body()?.data != null){
                sharedPreferencesManager.saveToken(result.body()?.data?.token)
                sharedPreferencesManager.saveUserID(result.body()?.data?.userID)
                sharedPreferencesManager.saveID(result.body()?.data?.id)
            }
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }

    }

    override suspend fun socialLoginCall(request: SocialRequest): BaseResponse<User>? {
        try {
            val result = retrofitBuilder.getBindObject()?.socialLoginCall(request)
            if(result?.body()?.data != null){
                sharedPreferencesManager.saveToken(result.body()?.data?.token)
                sharedPreferencesManager.saveUserID(result.body()?.data?.userID)
                sharedPreferencesManager.saveID(result.body()?.data?.id)
            }
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun forgetPasswordEmailCall(request: ForgetPasswordRequest): BaseResponse<ForgetPasswordResponseData>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.forgetPasswordEmailCall(request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun forgetPasswordPhoneCall(request: ForgetPasswordRequest): BaseResponse<ForgetPasswordResponseData>? {
        try {
            val result =
                retrofitBuilder.getBindObject()?.forgetPasswordMobileCall(request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun forgetPasswordConfirmCall(request: ForgetPasswordConfirmRequest): BaseResponse<ForgetPasswordConfirmResponseData>? {
        try {
            val result = retrofitBuilder.getBindObject()
                ?.forgetPasswordConfirmCall(request)
            return baseResponse(result)
        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun forgetPasswordResetCall(request: ForgetPasswordResetRequest): BaseResponse<Nothing>? {
        try {
            val result = retrofitBuilder.getBindObject()
                ?.forgetPasswordResetCall(request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun logout(): BaseResponse<String>? {
        try {
            val result = retrofitBuilder.getBindObject()?.logoutCall()
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

    override suspend fun updatePasswordResetCall(request: UpdatePasswordRequest): BaseResponse<Nothing>? {
        try {
            val result = retrofitBuilder.getBindObject()
                ?.updatePasswordResetCall(request)
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }
    override suspend fun getSettings(): BaseResponse<SettingData>? {

        try {
            val result = retrofitBuilder.getBindObject()?.getSettings()
            return baseResponse(result)

        } catch (ex: Exception) {
            return handleServerExceptions(ex)
        }
    }

}