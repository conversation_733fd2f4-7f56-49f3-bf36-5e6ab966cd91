<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
<!--    app:cardCornerRadius="10dp"-->
<!--    android:layout_marginStart="2dp"-->
<!--    android:layout_marginEnd="2dp"-->
<!--    android:layout_marginTop="5dp"-->
<!--    app:cardElevation="4dp"-->
<!--    android:layout_marginBottom="5dp">-->


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/calenderDayText"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="10"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:textSize="15dp"
        android:textAppearance="@style/header_auth"
        />

</androidx.cardview.widget.CardView>