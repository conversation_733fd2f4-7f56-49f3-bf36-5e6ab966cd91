<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/discount_details_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingTop="10dp"
    android:paddingBottom="16dp">

    <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
        android:id="@+id/dots_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:dotsColor="@color/color_gray_22"
        app:dotsCornerRadius="8dp"
        app:dotsSize="16dp"
        app:dotsSpacing="4dp"
        app:dotsWidthFactor="2.5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:progressMode="false"
        app:selectedDotColor="@color/black" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="5dp"
        android:textAlignment="viewStart"
        android:theme="@style/product_details_title"
        app:layout_constraintEnd_toStartOf="@id/tv_price"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="لاب توب"
        app:layout_constraintTop_toBottomOf="@id/dots_indicator" />
    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/color_59"
        android:theme="@style/product_details_price"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        tools:text="66 EGP" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/branch_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/gallery_img"
            android:layout_width="@dimen/icons_size_height_weight_6"
            android:layout_height="@dimen/icons_size_height_weight_6"
            android:layout_marginTop="10dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/portrait_welcome_img"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/gallery_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:theme="@style/product_details_store_name"
            app:layout_constraintBottom_toBottomOf="@+id/gallery_img"
            app:layout_constraintStart_toEndOf="@id/gallery_img"
            app:layout_constraintTop_toTopOf="@id/gallery_img"
            tools:text="Branch name" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    <View
        android:id="@+id/products_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/branch_container"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/product_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:itemCount="3"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/products_divider"
        tools:listitem="@layout/item_offer_product" />
    <View
        android:id="@+id/cart_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginVertical="10dp"
        android:background="@drawable/bg_btn_disabled"
        app:layout_constraintTop_toBottomOf="@id/product_recycler_view"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quantity_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_cart_quantity"
        android:padding="10dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cart_divider">

        <TextView
            android:id="@+id/price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            style="@style/product_details_price"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="66 EGP"
            />
        <include
            android:id="@+id/product_qty_cart_container"
            layout="@layout/include_input_quantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/price" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/min_order_note"
        style="@style/label_item_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:text="@string/min_order_number"
        android:textColor="@color/color_red_3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/quantity_cont" />
    <include
        android:id="@+id/btn_add_to_cart"
        layout="@layout/btn_progress"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="30dp"
        android:theme="@style/label_add_to_cart_button"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/min_order_note" />

</androidx.constraintlayout.widget.ConstraintLayout>
