<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/facility_locationn"
        android:textSize="18dp"
        android:textAppearance="@style/header_auth"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/info"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:text="@string/country_"
                    android:textAppearance="@style/label_joinus" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:layout_weight="1"
                    android:text="@string/governorate"
                    android:textAppearance="@style/label_joinus" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:weightSum="2">

                <EditText
                    android:id="@+id/country"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/ic_arrow_down"
                    android:drawablePadding="12dp"
                    android:focusableInTouchMode="false"
                    android:inputType="textNoSuggestions"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />

                <EditText
                    android:id="@+id/governerate"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:clickable="false"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/ic_arrow_down"
                    android:drawablePadding="12dp"
                    android:enabled="false"
                    android:focusableInTouchMode="false"
                    android:inputType="textNoSuggestions"
                    android:longClickable="false"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_gravity="start"
                android:text="@string/region"
                android:textAppearance="@style/label_joinus" />

            <EditText
                android:id="@+id/region"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/et_style_with_borders"
                android:clickable="false"
                android:cursorVisible="false"
                android:drawableEnd="@drawable/ic_arrow_down"
                android:drawablePadding="12dp"
                android:enabled="false"
                android:focusableInTouchMode="false"
                android:inputType="textNoSuggestions"
                android:longClickable="false"
                android:padding="3dp"
                android:textAppearance="@style/label_edittext_joinus"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_gravity="start"
                    android:text="@string/address_"
                    android:textAppearance="@style/label_joinus" />

                <EditText
                    android:id="@+id/address"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style_with_borders"
                    android:inputType="textNoSuggestions"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />
            </LinearLayout>

            <com.google.android.gms.maps.MapView
                android:id="@+id/map"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="50dp"
                android:background="@drawable/et_style_with_borders" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />





</androidx.constraintlayout.widget.ConstraintLayout>