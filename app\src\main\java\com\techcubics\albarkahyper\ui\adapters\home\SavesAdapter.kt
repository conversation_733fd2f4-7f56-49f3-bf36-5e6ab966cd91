package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.akiniyalocts.pagingrecycler.PagingAdapter
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.databinding.ItemSavesBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.SaveHolderItem
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler


class SavesAdapter<T>(val margin:Int=0,val clickHandler: IOnAdapterItemClickHandler): PagingAdapter() {

    lateinit var items:List<T>
    lateinit var itemBinding: ItemSavesBinding

    fun setItemsList(_items:List<T>) {
        items = _items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SaveHolderItem<T> {
        itemBinding = ItemSavesBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SaveHolderItem(itemBinding,parent.context,margin,clickHandler)
    }

    override fun onBindViewHolder(defaultHolder: RecyclerView.ViewHolder, position: Int) {
        super.onBindViewHolder(defaultHolder, position)
        val holder= defaultHolder as SaveHolderItem<T>
        holder.bind(items.get(position))
    }

    override fun getItemCount(): Int {
        return super.getItemCount()
    }

    override fun getPagingLayout(): Int {
        return R.layout.item_saves
    }

    override fun getPagingItemCount(): Int {
        return items.size
    }




}