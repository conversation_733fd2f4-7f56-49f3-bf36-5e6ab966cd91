package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class ProductSearchStoreData(

    @SerializedName("id")
    val id:Int,
    /*
    @SerializedName("furniture_id")
    val furnitureID:Int,
    @SerializedName("branch_type_id")
    val branchTypeID:Int,
    @SerializedName("branch_type_name")
    val branchTypeName:String,*/
    @SerializedName("name")
    val name:String,
    /*@SerializedName("description")
    val description:String,*/
    @SerializedName("logo")
    val logo:String,
    @SerializedName("minimum_order_price")
    val minOrder:Float,

    /*@SerializedName("address")
    val address:String,
    @SerializedName("is_fav")
    val isFavorite:Boolean,
    @SerializedName("rate")
    val rate:String,
    @SerializedName("rate_count")
    val rate_count:Int,
    @SerializedName("open")
    val isOpened:<PERSON><PERSON><PERSON>,
    @SerializedName("link_google_play")
    val linkGooglePlay:String,
    @SerializedName("link_apple_store")
    val linkAppleStore:String,
    @SerializedName("qr_image")
    val qrImage:String,
    @SerializedName("lat")
    val latitute:String,
    @SerializedName("lng")
    val longitude:String,*/

    @SerializedName("districts")
    val districts: List<Districts?>? = null,
    )
