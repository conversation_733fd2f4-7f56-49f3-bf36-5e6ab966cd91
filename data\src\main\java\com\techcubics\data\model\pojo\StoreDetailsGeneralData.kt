package com.techcubics.data.model.pojo

import android.graphics.Region
import com.google.gson.annotations.SerializedName

data class StoreDetailsGeneralData(
    @SerializedName("id")
    val id:Int,
    @SerializedName("shop_id")
    val shopID:Int,
    @SerializedName("favourited_count")
    val favouritedCount:Int,
    @SerializedName("name")
    val name:String,
    @SerializedName("image")
    val image:String,
    @SerializedName("slug")
    var slug: String? = null,
    @SerializedName("description")
    val description:String,
    @SerializedName("logo")
    val logo:String,
    @SerializedName("phone")
    val phone:String,
    @SerializedName("email")
    val email:String,
    @SerializedName("country")
    val country:Country,
    @SerializedName("governorate")
    val governorate: Governerate,
    @SerializedName("region")
    val region:Regoin,
    @SerializedName("address")
    val address:String,
    //branch_type
    //times of week
    @SerializedName("branch_types")
    val branchTypes:MutableList<BranchType>,
    @SerializedName("is_fav")
    var isFav:Boolean,
    @SerializedName("is_rate")
    var isRated:Boolean,
    @SerializedName("rate")
    val rate:Float,
    @SerializedName("rate_count")
    var rate_count:Int,
    @SerializedName("open")
    val isOpened:Boolean,
    @SerializedName("link_google_play")
    val linkGooglePlay:String,
    @SerializedName("link_apple_store")
    val linkAppleStore:String,
    @SerializedName("qr_image")
    val qrImage:String,
    @SerializedName("lat")
    val latitute:String,
    @SerializedName("lng")
    val longitude:String,
    @SerializedName("count_items_cart")
    val count_items_cart:String?,
    @SerializedName("total_amount_cart")
    val total_amount_cart:String?,
    @SerializedName("times_of_week")
    val workingTimes:MutableList<StoreDetailsBranchesTime>,
    @SerializedName("chat_enable")
    val isChatEnabled:Boolean?=null,
    @SerializedName("minimum_order_price"   ) var minOrder     : Float=0f

)
