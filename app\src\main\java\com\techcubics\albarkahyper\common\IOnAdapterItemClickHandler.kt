package com.techcubics.albarkahyper.common

interface IOnAdapterItemClickHandler {
    fun onItemClicked(itemId: Int?, type: String) {}
    fun onItemClicked(itemId: Int?, type: String,name:String) {}
    fun addToCart(
        pathEndPoint: String,
        shopId: Int?,
        modelType: String?,
        modelId: Int?,
        qty: Int?,
        itemPrice: Float = 0f,
        maxQty: Int,
        minQty: Int,
        minOrder: Float,
        itemPosition: Int
    ) {
    }

    fun removeItemFromCart(
        modelType: String?,
        modelId: Int?,
        productPrice: Float,
        itemPosition: Int,
    ) {
    }
}