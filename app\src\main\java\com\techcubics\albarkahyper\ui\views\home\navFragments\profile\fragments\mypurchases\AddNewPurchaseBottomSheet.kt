package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.fragments.mypurchases

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.techcubics.data.model.pojo.Mypurchase
import com.techcubics.data.model.requests.profile.MyPurchaseRequset
import com.techcubics.data.model.requests.profile.location.UpdatePurchaseRequest
import com.techcubics.albarkahyper.common.BottomSheetAlertDialog
import com.techcubics.albarkahyper.common.IRefreshListListenerWithCalender
import com.techcubics.albarkahyper.common.ProgressButton
import com.techcubics.albarkahyper.databinding.DialogNewPurchaseBinding
import com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels.ProfileViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.SimpleDateFormat
import java.util.*

class AddNewPurchaseBottomSheet(
    val listner: IRefreshListListenerWithCalender,
    val purchaseitem: Mypurchase?,
    val TAG: String
) : BottomSheetDialogFragment() {

    private lateinit var binding : DialogNewPurchaseBinding
    private lateinit var progressBtn : ProgressButton
    private val profileViewModel by viewModel<ProfileViewModel>()
    private lateinit var bottomSheetAlertDialog: BottomSheetAlertDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, com.techcubics.style.R.style.bottomsheet_style)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogNewPurchaseBinding.inflate(inflater, container, false)
        
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        dialog?.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.WRAP_CONTENT)
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog?.window?.attributes?.windowAnimations = com.techcubics.style.R.style.dialog_animation
        dialog?.window?.setGravity(Gravity.BOTTOM)

        progressBtn = ProgressButton(requireContext())
        progressBtn.init(binding.signBtnProgress)
        bottomSheetAlertDialog = BottomSheetAlertDialog()
        bottomSheetAlertDialog.init(requireContext())
        initViews()
        viewObservers()
        return binding.root
    }

    private fun viewObservers() {
        profileViewModel.addLocationResponseMutableLiveData.observe(viewLifecycleOwner){
            CoroutineScope(Dispatchers.Main).launch {
                if(it != null){
                    if(it.status!!){
                        progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.save),null)

                    }else{
                        progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.save),null)
                    }
                    listner.onItemClick(binding.date.text.toString(), TAG)
                    delay(1200)
                    dialog?.dismiss()
                }
            }

        }

        profileViewModel.updateLocationMutableLiveData.observe(viewLifecycleOwner){
            CoroutineScope(Dispatchers.Main).launch {
                if(it != null){
                    if(it.status!!){
                        progressBtn.btnFinishedSuccessfully(getString(com.techcubics.style.R.string.save),null)

                    }else{
                        progressBtn.btnFinishedFailed(getString(com.techcubics.style.R.string.save),null)
                    }
                    listner.onItemClick(binding.date.text.toString(), TAG)
                    delay(1200)
                    dialog?.dismiss()
                }
            }
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initViews() {
        binding.signBtnProgress.textView.text = getString(com.techcubics.style.R.string.save)
        binding.signBtnProgress.textView.setTextAppearance(com.techcubics.style.R.style.label_updateprofile)
        binding.signBtnProgress.constraintsLayout.background = resources.getDrawable(com.techcubics.style.R.drawable.bg_round_gray_background,null)

        if(purchaseitem != null){
            binding.newPurchasesText.setText(purchaseitem.item)
            binding.date.setText(purchaseitem.date)
            edittextWatcher()
        }

        binding.newPurchasesText.addTextChangedListener{
            if(binding.newPurchasesText.equals("")){
                binding.signBtnProgress.textView.setTextAppearance(com.techcubics.style.R.style.label_updateprofile)
                binding.signBtnProgress.constraintsLayout.background = resources.getDrawable(com.techcubics.style.R.drawable.bg_round_gray_background,null)
            }
            else{
                edittextWatcher()
            }
        }

        binding.date.addTextChangedListener{
            if(binding.newPurchasesText.equals("")){
                binding.signBtnProgress.textView.setTextAppearance(com.techcubics.style.R.style.label_updateprofile)
                binding.signBtnProgress.constraintsLayout.background = resources.getDrawable(com.techcubics.style.R.drawable.bg_round_gray_background,null)
            }
            else{
                edittextWatcher()
            }
        }

        binding.date.setOnClickListener {
            binding.chooseCalender.visibility = View.VISIBLE
        }

        initCalender()

        binding.signBtnProgress.constraintsLayout.setOnClickListener {
            progressBtn.btnActivated()
            if(purchaseitem != null){
                profileViewModel.updatePurchase(UpdatePurchaseRequest(purchaseitem.id,binding.date.text.toString(),binding.newPurchasesText.text.toString()))

            }else{
                profileViewModel.addPurchase(MyPurchaseRequset(binding.date.text.toString(),binding.newPurchasesText.text.toString()))
            }
        }

    }

    private fun initCalender() {
        val myCalender = Calendar.getInstance()
        val datePicker = DatePickerDialog.OnDateSetListener { _, year, month, day ->
            myCalender.set(Calendar.YEAR, year)
            myCalender.set(Calendar.MONTH, month)
            myCalender.set(Calendar.DAY_OF_MONTH, day)
            updateDateTextView(myCalender)
        }

        binding.dateImg.setOnClickListener {
            binding.chooseCalender.visibility = View.GONE
            DatePickerDialog(
                requireContext(),
                com.techcubics.style.R.style.DialogTheme,
                datePicker,
                myCalender.get(Calendar.YEAR),
                myCalender.get(Calendar.MONTH),
                myCalender.get(Calendar.DAY_OF_MONTH)
            ).show()
        }
    }

    private fun updateDateTextView(myCalender: Calendar) {
        val myFormat = "yyyy-MM-dd"
        val sdf = SimpleDateFormat(myFormat, Locale.UK)
        binding.date.setText(sdf.format(myCalender.time))

    }



    @SuppressLint("UseCompatLoadingForDrawables")
    private fun edittextWatcher() {
        if(binding.newPurchasesText.text.isNotEmpty() && binding.date.text.isNotEmpty()){
            binding.signBtnProgress.constraintsLayout.background = resources.getDrawable(com.techcubics.style.R.drawable.btn_style,null)
            binding.signBtnProgress.textView.setTextAppearance(com.techcubics.style.R.style.label_button)

        }else
        {
            binding.signBtnProgress.textView.setTextAppearance(com.techcubics.style.R.style.label_updateprofile)
            binding.signBtnProgress.constraintsLayout.background = resources.getDrawable(com.techcubics.style.R.drawable.bg_round_gray_background,null)
        }
    }


}