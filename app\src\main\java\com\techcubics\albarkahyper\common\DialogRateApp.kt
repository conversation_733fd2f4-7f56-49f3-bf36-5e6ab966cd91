package com.techcubics.albarkahyper.common

import android.app.AlertDialog
import android.app.Dialog
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.RatingBar
import android.widget.RatingBar.OnRatingBarChangeListener
import android.widget.Toast
import androidx.appcompat.app.AppCompatDialogFragment
import com.techcubics.albarkahyper.common.DialogManager.showMaterialFeedback
import com.techcubics.albarkahyper.databinding.DialogRateAppBinding


class DialogRateApp : AppCompatDialogFragment(),
    OnRatingBarChangeListener {
    private var _binding: DialogRateAppBinding? = null
    private val binding get() = _binding!!
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = AlertDialog.Builder(activity)
        val inflater = requireActivity().layoutInflater
        _binding = DialogRateAppBinding.inflate(inflater)
        builder.setView(binding.root)
        binding.btMaybeLater.setOnClickListener { dismiss() }
        binding.btRatingSend.setOnClickListener {
            Toast.makeText(
                activity,
                "Please select 5 star rating!",
                Toast.LENGTH_SHORT
            ).show()
        }
        binding.btRatingBar.onRatingBarChangeListener = this
        builder.setCancelable(false)
        return builder.create()
    }

    override fun onRatingChanged(ratingBar: RatingBar, v: Float, b: Boolean) {
//        if (v >= 3) {
            val packageName = requireActivity().packageName
            var intent: Intent
            try {
                intent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
                startActivity(intent)
            } catch (activityNotFoundException: ActivityNotFoundException) {
                intent = Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("https://play.google.com/store/apps/details?id=$packageName")
                )
                startActivity(intent)
            }
            dismiss()
//        }
//        else if (v > 0) {
//            showMaterialFeedback(requireActivity(), v, "<EMAIL>")
//            dismiss()
//        }
    }

    companion object {
        const val KEY = "fragment_rate"
    }
}