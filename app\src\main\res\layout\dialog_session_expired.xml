<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_popup_dialog"
    android:minWidth="300dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center">
        <ProgressBar
            android:id="@+id/session_expired_progressbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminateTint="@color/app_color" />
        <TextView
            android:id="@+id/session_expired_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/session_expired"
            android:layout_marginStart="15dp"
            android:textAppearance="@style/label_profile"
            android:textStyle="bold"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>