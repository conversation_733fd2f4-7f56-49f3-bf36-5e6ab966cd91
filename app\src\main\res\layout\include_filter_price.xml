<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/title"
        style="@style/label_section_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/price"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/line_divder_item"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <RadioGroup
        android:id="@+id/less_more_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="vertical"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toBottomOf="@id/divider1"
        android:layout_marginTop="20dp">
        <RadioButton
            android:id="@+id/from_less_more"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:buttonTint="@color/app_color"
            android:text="@string/less_more_price"
            android:textAppearance="@style/label_updateprofile"
            />
        <RadioButton
            android:id="@+id/from_more_less"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:buttonTint="@color/app_color"
            android:text="@string/more_less_price"
            android:layout_marginTop="20dp"
            android:textAppearance="@style/label_updateprofile"
            />

    </RadioGroup>
    <LinearLayout
        android:id="@+id/layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="20dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintBottom_toTopOf="@+id/rangeSlider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/less_more_price"
        app:layout_constraintEnd_toEndOf="parent">
        <TextView
            android:id="@+id/tvStart"
            style="@style/label_filter_start_end_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAlignment="viewStart"
            android:text="1 جنية"/>

        <TextView
            android:id="@+id/tvEnd"
            style="@style/label_filter_start_end_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAlignment="viewEnd"
            android:text="300 جنية"
            />
    </LinearLayout>


    <com.google.android.material.slider.RangeSlider
        android:theme="@style/MyMaterialTheme"
        android:id="@+id/rangeSlider"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:valueFrom="1.0"
        android:valueTo="300.0"
        app:labelBehavior="gone"
        app:trackColor="@color/app_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout"
        app:values="@array/initial_slider_values"/>

</androidx.constraintlayout.widget.ConstraintLayout>