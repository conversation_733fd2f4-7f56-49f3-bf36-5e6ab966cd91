plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: '../dependencies.gradle'

android {
    namespace 'com.techcubics.style'
    compileSdk 33

    defaultConfig {
        minSdk 24
        targetSdk 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation kotlinDependencies.core
    implementation androidxDependencies.core
    implementation materialDependencies.core
    testImplementation juintDependencies.core
    androidTestImplementation testDependencies.core
    androidTestImplementation espressoDependencies.core
}