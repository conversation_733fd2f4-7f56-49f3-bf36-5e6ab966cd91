<vector android:autoMirrored="true" android:height="24dp"
    android:viewportHeight="24" android:viewportWidth="24"
    android:width="24dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#00000000"
        android:pathData="M20,21V19C20,17.939 19.579,16.922 18.828,16.172C18.078,15.421 17.061,15 16,15H8C6.939,15 5.922,15.421 5.172,16.172C4.421,16.922 4,17.939 4,19V21"
        android:strokeColor="#000000" android:strokeLineCap="round"
        android:strokeLineJoin="round" android:strokeWidth="2"/>
    <path android:fillColor="#00000000"
        android:pathData="M12,11C14.209,11 16,9.209 16,7C16,4.791 14.209,3 12,3C9.791,3 8,4.791 8,7C8,9.209 9.791,11 12,11Z"
        android:strokeColor="#000000" android:strokeLineCap="round"
        android:strokeLineJoin="round" android:strokeWidth="2"/>
</vector>
