package com.techcubics.albarkahyper.ui.views.home.navFragments.profile.viewmodels

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.model.pojo.StoreTypes
import com.techcubics.data.model.pojo.*
import com.techcubics.data.model.pojo.Locations
import com.techcubics.data.model.requests.profile.UpdateProfileRequest
import com.techcubics.data.model.requests.profile.location.AddLoactionRequest
import com.techcubics.data.model.requests.profile.location.UpdateLocationRequest
import com.techcubics.data.model.requests.profile.ContactusRequest
import com.techcubics.data.model.requests.profile.JoinusRequest
import com.techcubics.data.model.requests.profile.MyPurchaseRequset
import com.techcubics.data.model.requests.profile.location.UpdatePurchaseRequest
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.home.profile.ProfileRepo
import kotlinx.coroutines.launch


class ProfileViewModel(private val profileRepo: ProfileRepo) : ViewModel() {

    var countriesAndDeliveryAreasPopupMenuItemLiveData: MutableLiveData<DeliveryAreaId?> = MutableLiveData()
    var countriesPopupMenuItemLiveData: MutableLiveData<Countries?> = MutableLiveData()
    var popupMenuItemLiveData: MutableLiveData<String?> = MutableLiveData()
    var branchTypePopMenuItemLiveData: MutableLiveData<ArrayList<StoreTypes?>> = MutableLiveData()
    var countriesMutableLiveData = MutableLiveData<BaseResponse<List<Countries>>?>()
    val governeratesMutableLiveData = MutableLiveData<Governerate?>()
    val profileMutableLiveData = MutableLiveData<BaseResponse<User>?>()
    val updateProfileMutableLiveData = MutableLiveData<BaseResponse<User>?>()
    val listOfgovernerateMutableLiveData = MutableLiveData<BaseResponse<ArrayList<Governerate>>?>()
    val listOfregionsMutableLiveData = MutableLiveData<BaseResponse<ArrayList<Regoin>>?>()
    val regionPopupMenuItemLiveData = MutableLiveData<Regoin?>()
    val addLocationResponseMutableLiveData = MutableLiveData<BaseResponse<String>?>()
    val selectedEditLocationMutableLiveData = MutableLiveData<Locations?>()
    val selectedDeleteLocationMutableLiveData = MutableLiveData<Locations?>()
    val showLocationMutableLiveData = MutableLiveData<BaseResponse<Locations>?>()
    val showPurchaseMutableLiveData = MutableLiveData<BaseResponse<Mypurchase>?>()
    val listPurchasesResponseMutableLiveData = MutableLiveData<BaseResponse<ArrayList<Mypurchase>>?>()
    val listPurchasesByDateResponseMutableLiveData = MutableLiveData<BaseResponse<ArrayList<Mypurchase>>?>()

    val updateLocationMutableLiveData = MutableLiveData<BaseResponse<String>?>()
    val deleteLocationLocationMutableLiveData = MutableLiveData<BaseResponse<String>?>()
    val joinusMutableLiveData = MutableLiveData<BaseResponse<String>?>()
    val branchTypeMutableLiveData = MutableLiveData<BaseResponse<NearStoreTypes>?>()
    val callusMutableLiveData = MutableLiveData<BaseResponse<Nothing>?>()
    val pageCallMutableLiveData = MutableLiveData<BaseResponse<PageData>?>()
    val itemPositionLiveData = MutableLiveData<Int?>()
    val deliveryAreasResponse = MutableLiveData<BaseResponse<ArrayList<DeliveryAreaId>>?>()
    val languageMutableLiveData = MutableLiveData<BaseResponse<List<Language>>?>()



//    private val profileRepo: ProfileRepo = ProfileRepoImpl()

    fun getCountries() {

        viewModelScope.launch {
            val countriesResponse = profileRepo.getCountries()

            if (countriesResponse != null) {
                countriesMutableLiveData.postValue(countriesResponse)
            }
        }

    }

    fun getProfile() {

        viewModelScope.launch {
            val profileResponse = profileRepo.profileCall()

            if (profileResponse != null) {
                profileMutableLiveData.postValue(profileResponse)
            }
        }

    }

    fun getLanguages() {

        viewModelScope.launch {
            val languageResponse = profileRepo.getLanguages()

            if (languageResponse != null) {
                languageMutableLiveData.postValue(languageResponse)
            }
        }

    }

    fun updateProfileCall(request: UpdateProfileRequest) {

        viewModelScope.launch {
            val updateProfileResponse = profileRepo.updateProfileCall(request)
            updateProfileResponse?.let { updateProfileMutableLiveData.postValue(it) }

        }
    }

    fun getGovernerateByCountryId(country_id: Int) {
        viewModelScope.launch {
            val listOfGovernerateResponse = profileRepo.getGovernerateByCountryId(country_id)

            if (listOfGovernerateResponse != null) {
                listOfgovernerateMutableLiveData.postValue(listOfGovernerateResponse)
            }
        }

    }

    fun getRegionBygovernorateId(governerate_id: Int, country_id: Int) {
        viewModelScope.launch {
            val listOfRegionResponse =
                profileRepo.getRegionBygovernorateId(governerate_id, country_id)

            if (listOfRegionResponse != null) {
                listOfregionsMutableLiveData.postValue(listOfRegionResponse)
            }
        }

    }

    fun addLocation(request : AddLoactionRequest) {
        viewModelScope.launch {
            val addLocationResponse = profileRepo.storeLocation(request)
            if (addLocationResponse != null) {
                addLocationResponseMutableLiveData.postValue(addLocationResponse)
            }
        }

    }

    fun showLocation(location_id: Int, country_id: Int, country_code: String) {
        viewModelScope.launch {
            val showLocationResponse =
                profileRepo.showLocation(location_id, country_id, country_code)


            if (showLocationResponse != null) {
                showLocationMutableLiveData.postValue(showLocationResponse)

            }
        }

    }

    fun updateLocation(request : UpdateLocationRequest) {
        viewModelScope.launch {
            val updateLocationResponse = profileRepo.updateLocation(request)
            if (updateLocationResponse != null) {
                updateLocationMutableLiveData.postValue(updateLocationResponse)
            }
        }

    }

    fun deleteLocation(location_id: Int, country_id: Int, country_code: String) {
        viewModelScope.launch {
            val deleteLocationResponse =
                profileRepo.deleteLocation(location_id, country_id, country_code)

            if (deleteLocationResponse != null) {
                deleteLocationLocationMutableLiveData.postValue(deleteLocationResponse)
            }

        }
    }

    fun storeTypesCall() {
        viewModelScope.launch {
            val storeTypeResponse = profileRepo.storeTypesCall()

            if (storeTypeResponse != null) {

                branchTypeMutableLiveData.postValue(storeTypeResponse)
            }

        }
    }

    fun joinus(request : JoinusRequest){
        viewModelScope.launch {
            val joinusResponse = profileRepo.joinus(request)
            if (joinusResponse != null) {
                joinusMutableLiveData.postValue(joinusResponse)
            }
        }

    }

    fun callus(request: ContactusRequest) {
        viewModelScope.launch {
            val callusResponse =
                profileRepo.contactUsCall(request)

            if (callusResponse != null) {
                callusMutableLiveData.postValue(callusResponse)

            }

        }
    }

    fun pagecall(page_name: String) {
        viewModelScope.launch {
            val pageResponse = profileRepo.pagesCall(page_name)
            if (pageResponse != null) {
                pageCallMutableLiveData.postValue(pageResponse)
            }

        }
    }

    fun getCountriesAndDeliveryAreasByFurnitureId(furnitureId: Int, countryId: Int) {
        viewModelScope.launch {
            val res = profileRepo.getCountriesAndDeliveryAreasByFurnitureId(furnitureId, countryId)
            res?.let { deliveryAreasResponse.postValue(it) }
        }
    }

    fun getGovernorateAreasByDeliveryId(deliveryAreaId: Int, countryId: Int) {
        viewModelScope.launch {
            val res = profileRepo.getGovernorateAreasByDeliveryId(deliveryAreaId, countryId)
            res?.let { listOfgovernerateMutableLiveData.postValue(it) }
        }
    }

    fun getRegionByGovernorateAndDeliveryAreaId(governorateId: Int, deliveryAreaId: Int, countryId: Int) {
        viewModelScope.launch {
            val res = profileRepo.getRegionByGovernorateAndDeliveryAreaId(
                governorateId,
                deliveryAreaId,
                countryId
            )
            res?.let { listOfregionsMutableLiveData.postValue(it) }
        }
    }


    fun addPurchase(request : MyPurchaseRequset) {
        viewModelScope.launch {
            val addPurchaseResponse = profileRepo.storePurchase(request)
            if (addPurchaseResponse != null) {
                addLocationResponseMutableLiveData.postValue(addPurchaseResponse)
            }
        }

    }

    fun showPurchase(listing_id: Int) {
        viewModelScope.launch {
            val showPurchaseResponse =
                profileRepo.showPurchase(listing_id)

            if (showPurchaseResponse != null) {
                showPurchaseMutableLiveData.postValue(showPurchaseResponse)

            }
        }

    }

    fun updatePurchase(request : UpdatePurchaseRequest) {
        viewModelScope.launch {
            val updatePurchaseResponse = profileRepo.updatePurchase(request)
            if (updatePurchaseResponse != null) {
                updateLocationMutableLiveData.postValue(updatePurchaseResponse)
            }
        }

    }

    fun deletePurchase(listing_id: Int) {
        viewModelScope.launch {
            val deletePurchaseResponse =
                profileRepo.deletePurchase(listing_id)

            if (deletePurchaseResponse != null) {
                deleteLocationLocationMutableLiveData.postValue(deletePurchaseResponse)
            }

        }
    }

    fun listPurchases() {
        viewModelScope.launch {
            val listPurchasesResponse = profileRepo.listPurchase("")

            if (listPurchasesResponse != null) {
                listPurchasesResponseMutableLiveData.postValue(listPurchasesResponse)
            }

        }
    }

    fun listPurchasesByDate(date : String) {
        viewModelScope.launch {
            val listPurchasesResponse = profileRepo.listPurchase(date)

            if (listPurchasesResponse != null) {
                listPurchasesByDateResponseMutableLiveData.postValue(listPurchasesResponse)
            }

        }
    }

    fun markAsBuying(listing_id: Int){
        viewModelScope.launch {
            val check = profileRepo.markPurchaseAsBuying(listing_id)

            if (check != null) {
                joinusMutableLiveData.postValue(check)
            }

        }
    }
}