<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="300dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_popup_dialog">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/countryRV"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
            tools:itemCount="4"
            tools:listitem="@layout/item_country"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
        <ProgressBar
            android:id="@+id/progress_bar"
            android:visibility="gone"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
        <TextView
            android:id="@+id/empty_region"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/no_regions"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:padding="15dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center_horizontal"/>
    </androidx.constraintlayout.widget.ConstraintLayout>




</LinearLayout>
