package com.techcubics.data.repos.chat

import com.techcubics.data.model.pojo.OrderMessageData
import com.techcubics.data.model.pojo.OrderRoomsData
import com.techcubics.data.model.pojo.SendOrderData
import com.techcubics.data.model.requests.OrderSendCallRequest
import com.techcubics.data.model.requests.chat.OrderMessagesRequest
import com.techcubics.data.model.requests.chat.OrderRoomsRequest
import com.techcubics.data.model.requests.chat.OrderSendRequest
import com.techcubics.data.remote.BaseResponse

interface OrderChatRepo {
    suspend fun getRooms(request: OrderRoomsRequest): BaseResponse<MutableList<OrderRoomsData>>?
    suspend fun send(request: OrderSendCallRequest): BaseResponse<SendOrderData>?
    suspend fun getRoomHistory(request: OrderMessagesRequest): BaseResponse<OrderMessageData>?
}