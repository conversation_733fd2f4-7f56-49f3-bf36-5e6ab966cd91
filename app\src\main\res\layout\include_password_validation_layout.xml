<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/group"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="right"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:id="@+id/charcount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:text="@string/_8_characters_minimum"
        android:textAppearance="@style/label_auth"
        android:textSize="10dp"/>

    <TextView
        android:id="@+id/num"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:text="@string/at_least_one_number"
        android:textAppearance="@style/label_auth"
        android:textSize="10dp"/>

    <TextView
        android:id="@+id/AtoZ"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:text="@string/at_least_one_uppercase"
        android:textAppearance="@style/label_auth"
        android:textSize="10dp"/>


    <TextView
        android:id="@+id/specialchar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:text="@string/at_least_one_special_symbol"
        android:textAppearance="@style/label_auth"
        android:textSize="10dp"/>






</LinearLayout>

