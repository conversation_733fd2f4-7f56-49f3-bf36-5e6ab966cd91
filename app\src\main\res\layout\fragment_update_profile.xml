<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:id="@+id/updateprofile_layout"
    tools:context=".ui.views.home.navFragments.profile.fragments.updateprofile.UpdateProfileFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintTop_toTopOf="parent"
        />
    <androidx.core.widget.NestedScrollView
        android:id="@+id/sc_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_marginTop="11dp">

            <FrameLayout
                android:layout_width="116dp"
                android:layout_height="116dp"
                android:layout_gravity="center">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/profilePic"
                    android:layout_width="116dp"
                    android:layout_height="116dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:src="@drawable/portrait_placeholder" />

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_camera"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_gravity="center"
                    android:clickable="true"
                    android:background="?attr/selectableItemBackground"                    android:src="@drawable/ic_camera_profile" />
            </FrameLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="41dp"
                android:text="@string/personal_information"
                android:textAppearance="@style/header_updateprofile" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_weight="1"
                    android:text="@string/first_name"
                    android:textAppearance="@style/label_updateprofile" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_weight="1"
                    android:text="@string/last_name"
                    android:textAppearance="@style/label_updateprofile" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:weightSum="2">

                <EditText
                    android:id="@+id/first_name"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_updateprofile"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/last_name"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_updateprofile"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/phone_number"
                    android:textAppearance="@style/label_updateprofile" />

                <EditText
                    android:id="@+id/phone_number"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:inputType="number"
                    android:background="@drawable/et_style_with_borders"
                    android:padding="3dp"
                    android:layoutDirection="ltr"
                    android:textAppearance="@style/label_edittext_updateprofile"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/facility_name"
                    android:textAppearance="@style/label_joinus" />

                <EditText
                    android:id="@+id/facility_name"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style_with_borders"
                    android:inputType="textNoSuggestions"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:text="@string/facility_type_"
                android:textAppearance="@style/label_joinus" />
            <EditText
                android:id="@+id/facility_type_edt"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/et_style_with_borders"
                android:inputType="textNoSuggestions"
                android:padding="3dp"
                android:textAppearance="@style/label_edittext_joinus" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:text="@string/country"
                    android:textAppearance="@style/label_joinus" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:layout_weight="1"
                    android:text="@string/governorate_"
                    android:textAppearance="@style/label_joinus" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:weightSum="2">

                <EditText
                    android:id="@+id/country"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/ic_arrow_down"
                    android:drawablePadding="12dp"
                    android:focusableInTouchMode="false"
                    android:inputType="textNoSuggestions"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />

                <EditText
                    android:id="@+id/governerate"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/et_style_with_borders"
                    android:cursorVisible="false"
                    android:drawableEnd="@drawable/ic_arrow_down"
                    android:drawablePadding="12dp"
                    android:focusableInTouchMode="false"
                    android:inputType="textNoSuggestions"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_gravity="start"
                android:text="@string/region_"
                android:textAppearance="@style/label_joinus" />

            <EditText
                android:id="@+id/region"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/et_style_with_borders"
                android:cursorVisible="false"
                android:drawableEnd="@drawable/ic_arrow_down"
                android:drawablePadding="12dp"
                android:focusableInTouchMode="false"
                android:inputType="textNoSuggestions"
                android:padding="3dp"
                android:textAppearance="@style/label_edittext_joinus"
                />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/address"
                    android:textAppearance="@style/label_joinus" />

                <EditText
                    android:id="@+id/address"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/et_style_with_borders"
                    android:inputType="textNoSuggestions"
                    android:padding="3dp"
                    android:textAppearance="@style/label_edittext_joinus"
                    />
            </LinearLayout>
            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/map"
                android:name="com.google.android.gms.maps.SupportMapFragment"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/et_style_with_borders" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginEnd="16dp"-->
<!--                    android:text="@string/date_of_birth"-->
<!--                    android:textAppearance="@style/label_updateprofile" />-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="45dp"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginTop="8dp"-->
<!--                    android:layout_marginEnd="16dp"-->
<!--                    android:background="@drawable/et_style_with_borders"-->
<!--                    android:drawableEnd="@drawable/ic_calender"-->
<!--                    android:orientation="horizontal"-->
<!--                    android:paddingEnd="24dp">-->

<!--                    <EditText-->
<!--                        android:id="@+id/date_birth"-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:layout_weight="1"-->
<!--                        android:background="@drawable/et_style_with_borders"-->
<!--                        android:paddingStart="3dp"-->
<!--                        android:singleLine="true"-->
<!--                        android:inputType="number"-->
<!--                        android:clickable="true"-->
<!--                        android:cursorVisible="false"-->
<!--                        android:focusableInTouchMode="false"-->
<!--                        android:textAppearance="@style/label_edittext_updateprofile"-->
<!--                        android:textSize="14sp" />-->

<!--                    <ImageButton-->
<!--                        android:id="@+id/date_birth_img"-->
<!--                        style="?android:buttonBarButtonStyle"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:paddingBottom="5dp"-->
<!--                        android:src="@drawable/ic_calender" />-->
<!--                </LinearLayout>-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginEnd="16dp"-->
<!--                    android:layout_marginTop="16dp"-->
<!--                    android:text="@string/gender"-->
<!--                    android:textAppearance="@style/label_updateprofile" />-->

<!--                <RadioGroup-->
<!--                    android:id="@+id/gendre_group"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginStart="16dp"-->
<!--                    android:layout_marginEnd="16dp"-->
<!--                    android:layout_marginTop="5dp"-->
<!--                    android:orientation="horizontal">-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/male"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginEnd="23dp"-->
<!--                        android:text="@string/male"-->
<!--                        style="@style/label_updateprofile" />-->

<!--                    <RadioButton-->
<!--                        android:id="@+id/female"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/female"-->
<!--                        style="@style/label_updateprofile" />-->
<!--                </RadioGroup>-->

                <TextView
                    android:id="@+id/change_pass"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/change_password"
                    android:clickable="true"
                    android:background="?attr/selectableItemBackground"
                    android:textAppearance="@style/header_updateprofile"
                    android:textColor="#2B84A0" />

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="82dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="70dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/action_loading_animation"
        layout="@layout/include_action_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />




</androidx.constraintlayout.widget.ConstraintLayout>