package com.techcubics.albarkahyper.ui.views

import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.google.firebase.messaging.FirebaseMessaging
import com.techcubics.albarkahyper.MainActivity
import com.techcubics.albarkahyper.R
import com.techcubics.albarkahyper.common.NetworkChangeReceiver
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.home.navFragments.home.viewmodels.HomeFragmentViewModel
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.shared.enums.LoginStateEnum
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class SplashActivity : AppCompatActivity() {


    private val TAG = "SplashActivityTst"
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    private val authViewModel by viewModel<AuthViewModel>()
    private val homeViewModel by viewModel<HomeFragmentViewModel>()
    private lateinit var networkReceiver: NetworkChangeReceiver

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
//        Helper.full(this)
        networkReceiver = NetworkChangeReceiver(this)
        this.registerReceiver(networkReceiver, IntentFilter("android.net.conn.CONNECTIVITY_CHANGE"))
        initViews()
        observeViews()

        Log.d(TAG, "onCreate: ${sharedPreferencesManager.getToken()}")
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) Helper.hideSystemUI(window)
        else Helper.showSystemUI(window)
    }
    @OptIn(DelicateCoroutinesApi::class)
    private fun observeViews() {

        authViewModel.checkAuthorizationMutableLiveData.observe(this@SplashActivity) {

            if (it?.message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!) {
                Log.i("clothes","from splash unautherized")
                sharedPreferencesManager.setLoginState(LoginStateEnum.SessionExpired.value)
                GlobalScope.launch {
                    delay(1500)
                    startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                    finish()
                }
            } else {
                sharedPreferencesManager.setLoginState(LoginStateEnum.LoggedIn.value)
                GlobalScope.launch {
                    delay(1500)
                    startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                    finish()
                    updateFCMToken()
                }
            }

        }

        homeViewModel.updateFirebaseTokenResponse.observe(this@SplashActivity) {
            Log.w(TAG, "Updated FCM token ")

        }
        authViewModel.settingsMutableLiveData.observe(this@SplashActivity, Observer {

            try {
                if(it?.status!!){

                    if(it?.data!=null)
                    {
                        if(it?.data!!.version!=null){

                            if(it?.data!!.must_user_check_android_version.equals("yes")){

                                val manager = this.packageManager
                                val info = manager.getPackageInfo(this.packageName, PackageManager.GET_ACTIVITIES)
                                if(it?.data!!.version!!.toInt() > info.versionCode){
                                    Helper.ShowUpdateDialog(this@SplashActivity,getString(com.techcubics.style.R.string.message_new_version))
                                }else{

                                    if (sharedPreferencesManager.isLoggedIn() == "true") {
                                        authViewModel.checkAuthorization()
                                    } else {
                                        GlobalScope.launch {
                                            delay(1500)
                                            startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                                            finish()
                                        }
                                    }
                                }
                            }else{

                                if (sharedPreferencesManager.isLoggedIn() == "true") {
                                    authViewModel.checkAuthorization()
                                } else {
                                    sharedPreferencesManager.setLoginState(LoginStateEnum.Other.value)
                                    GlobalScope.launch {
                                        delay(1500)
                                        startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                                        finish()
                                    }
                                }
                            }

                        }else{
                            Helper.ShowErrorDialog(this@SplashActivity,it?.message)
                        }
                    }
                }else{

                    Helper.ShowErrorDialog(this@SplashActivity,getString(com.techcubics.style.R.string.server_error))
                }

            }
            catch (ex:Exception){

                Helper.ShowErrorDialog(this@SplashActivity,ex.message)
            }

        })
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun initViews() {

        if (NetworkChangeReceiver.isOnline(this)) {
            if (sharedPreferencesManager.isLoggedIn() == "true") {
                authViewModel.checkAuthorization()
            } else {
                sharedPreferencesManager.setLoginState(LoginStateEnum.Other.value)
                GlobalScope.launch {
                    delay(1500)
                    startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                    finish()
                }
            }
        } else {
            Log.i("here", "false")

            GlobalScope.launch {
                delay(1500)
                startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                finish()
            }
        }


    }

    override fun onDestroy() {
        super.onDestroy()
        this.unregisterReceiver(networkReceiver)

    }

    private fun updateFCMToken() {


        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            Log.i(TAG, "onCreate")
            if (task.isSuccessful) {
                // Get new FCM registration token
                val token = task.result
                Log.i(TAG, "onCreate: $token")
                // Log and toast
                homeViewModel.updateFCMToken(token.toString())
                // SharedPreferencesManager.saveFireBaseToken(token.toString())
            }

        }
    }

}