package com.techcubics.albarkahyper

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.IntentSender
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.location.Location
import android.location.LocationManager
import android.opengl.Visibility
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityCompat.startIntentSenderForResult
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.*
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.tasks.Task
// import com.google.android.play.core.appupdate.AppUpdateManager
// import com.google.android.play.core.appupdate.AppUpdateManagerFactory
// import com.google.android.play.core.install.model.AppUpdateType
// import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Latlng
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.albarkahyper.common.ContextWrapper
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.NetworkChangeReceiver
import com.techcubics.albarkahyper.databinding.ActivityMainBinding
import com.techcubics.albarkahyper.ui.views.auth.viewmodels.AuthViewModel
import com.techcubics.albarkahyper.ui.views.products.ProductsViewModel
import com.techcubics.shared.enums.LoginStateEnum
import com.zeugmasolutions.localehelper.LocaleHelper
import com.zeugmasolutions.localehelper.LocaleHelperActivityDelegate
import com.zeugmasolutions.localehelper.LocaleHelperActivityDelegateImpl
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.DecimalFormat
import java.util.*


class MainActivity : AppCompatActivity(), NavigationBarVisibilityListener {
    private val TAG = "MainActivity"
    lateinit var mainActivityBinding: ActivityMainBinding
    private val localeDelegate: LocaleHelperActivityDelegate = LocaleHelperActivityDelegateImpl()
    private lateinit var fusedLocation: FusedLocationProviderClient
    private lateinit var mLocationCallback: LocationCallback
    private lateinit var locationRequest: LocationRequest
    private lateinit var lastLocation: Location
    private val authViewModel by viewModel<AuthViewModel>()
    private val sharedPreferencesManager: SharedPreferencesManager by inject()
    lateinit var netwokReciever: NetworkChangeReceiver
    private lateinit var navController: NavController
    private val productsViewModel by viewModel<ProductsViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mainActivityBinding = ActivityMainBinding.inflate(layoutInflater)
        val view = mainActivityBinding.root
        setContentView(view)
        localeDelegate.onCreate(this)
        Log.i("conflictContext", "MainActivity")
        fusedLocation = LocationServices.getFusedLocationProviderClient(this)
        netwokReciever = NetworkChangeReceiver(this)
        this.registerReceiver(netwokReciever, IntentFilter("android.net.conn.CONNECTIVITY_CHANGE"))
        initViews()
        observeViews()
        val navHostFragment =
            supportFragmentManager.findFragmentById(com.techcubics.albarkahyper.R.id.fragmentContainerView) as NavHostFragment
        navController = navHostFragment.navController
        mainActivityBinding.bottomNavigationView.setupWithNavController(navController)
        //  Log.d(TAG, "onCreate: ${SharedPreferencesManager.getToken()}")
        handleIntent(intent)

    }

    private fun observeViews() {
        authViewModel.checkAuthorizationMutableLiveData.observe(this){

            if (it != null) {
                if (it.message!!.contains(getString(com.techcubics.style.R.string.unauthenticated))) {
                    sharedPreferencesManager.setLoginState(LoginStateEnum.SessionExpired.value)
                }else{
                    sharedPreferencesManager.setLoginState(LoginStateEnum.LoggedIn.value)
                    productsViewModel.getHomeBanner()
                }
                authViewModel.checkAuthorizationMutableLiveData.value = null
            }

        }

        productsViewModel.bannersResponse.observe(this){
            if(it != null){


                if(it.data?.size!!>0) {
                    mainActivityBinding.homeMainBannerCont.visibility = View.VISIBLE
                    it.data?.get(0)?.image?.let { it1 ->
                        Helper.loadImage(
                            this,
                            it1, mainActivityBinding.homeMainBanner.image1
                        )
                    }
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let { handleIntent(it) }
    }

    private fun handleIntent(intent:Intent){
        val uri = intent.data
        if (uri != null) {
            val itemType = uri.getQueryParameter("type")
            val itemId  = uri.getQueryParameter("item_id")
            val bundle = Bundle()
            bundle.putString(Constants.INTENT_SLUG,itemId)
            when(itemType){
                RateTypesEnum.Store.value->{

                }
                RateTypesEnum.Product.value->{
                    navController.navigate(R.id.productDetailsFragment,bundle)
                }
                RateTypesEnum.Discount.value->{
                    itemId?.toInt()?.let { bundle.putInt(Constants.INTENT_ID, it) }
                    navController.navigate(R.id.discountDetailsFragment,bundle)
                }
                RateTypesEnum.Offer.value->{
                    navController.navigate(R.id.offerDetailsFragment,bundle)
                }
                RateTypesEnum.Save.value->{
                    navController.navigate(R.id.saveDetailsFragment,bundle)
                }
            }
        }
    }
    private fun initViews() {
        if (NetworkChangeReceiver.isOnline(this)) {

            locationRequest = LocationRequest.create()
            locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY)
            locationRequest.setInterval(5000)
            locationRequest.setFastestInterval(2000)
            mainActivityBinding.homeMainBannerCont.visibility = View.GONE
            Helper.loadingAnimationVisibility(View.GONE,mainActivityBinding.loadingAnimation.root)


            mainActivityBinding.homeMainBannerCont.visibility = View.GONE
            mainActivityBinding.homeMainBanner.closeBanner.setOnClickListener {
                mainActivityBinding.homeMainBannerCont.visibility = View.GONE
            }
            getCurrentLocation()
            if (sharedPreferencesManager.isLoggedIn().equals("true")) {

                if (sharedPreferencesManager.isLoggedIn().equals("true")) {
                    authViewModel.checkAuthorization()
                }
            } else {
                sharedPreferencesManager.setLoginState(LoginStateEnum.Other.value)
            }


        }
    }

    override fun attachBaseContext(newBase: Context?) {
        Log.i("here", "attachBase")
        super.attachBaseContext(newBase?.let { localeDelegate.attachBaseContext(it) })
    }

    @SuppressLint("ObsoleteSdkInt")
    private fun getCurrentLocation() {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                if (isGPSEnabled()) {

                    mLocationCallback = object : LocationCallback() {
                        override fun onLocationResult(locationResult: LocationResult) {
                            val locationList = locationResult.locations
                            if (locationList.isNotEmpty()) {
                                //The last location in the list is the newest
                                val location = locationList.last()
                                lastLocation = location
                                stopLocationUpdates()
                                //Place current location marker
                                val latLng = LatLng(location.latitude, location.longitude)
                                val mlatlng: Latlng = Latlng(
                                    roundToSevenDigits(latLng.latitude),
                                    roundToSevenDigits(latLng.longitude)
                                )
                                sharedPreferencesManager.saveObject(Constants.LATLNG, mlatlng)
                            }
                        }
                    }
                    fusedLocation.requestLocationUpdates(locationRequest, mLocationCallback, null)

                } else {
                    turnOnGPS()
                }
            } else {
                requestPermissions(arrayOf(Manifest.permission.ACCESS_FINE_LOCATION), 1)
            }
        }
    }

    fun stopLocationUpdates() {
        fusedLocation.removeLocationUpdates(mLocationCallback)
        Log.i("here", "Location updates removed")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Log.i("update", "activity")
        if (requestCode == 2) {
            if (resultCode == Activity.RESULT_OK) {

                getCurrentLocation()
            }
        }
    }

    private fun roundToSevenDigits(value: Double): String {
        val df = DecimalFormat("##.#######").format(value)
        val englishdf = convertArabic(df)
        Log.i("here", "arabic to eng " + englishdf)
        return englishdf
    }

    private fun convertArabic(arabicStr: String): String {
        val chArr = arabicStr.toCharArray()
        val sb = StringBuilder()
        for (ch in chArr) {
            if (Character.isDigit(ch)) {
                sb.append(Character.getNumericValue(ch))
            } else if (ch == '٫') {
                sb.append(".")
            } else {
                sb.append(ch)
            }
        }
        return sb.toString()
    }

    private fun turnOnGPS() {
        val builder = LocationSettingsRequest.Builder()
            .addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: Task<LocationSettingsResponse> =
            LocationServices.getSettingsClient(this)
                .checkLocationSettings(builder.build())
        result.addOnCompleteListener { task ->
            try {
                val response: LocationSettingsResponse =
                    task.getResult(ApiException::class.java)
                Toast.makeText(this, "GPS is already tured on", Toast.LENGTH_SHORT)
                    .show()
            } catch (e: ApiException) {
                when (e.statusCode) {
                    LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> try {
                        val resolvableApiException = e as ResolvableApiException
                        startIntentSenderForResult(
                            resolvableApiException.getResolution().getIntentSender(),
                            2,
                            null,
                            0,
                            0,
                            0,
                            null
                        )
                    } catch (ex: IntentSender.SendIntentException) {
                        ex.printStackTrace()
                    }
                    LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
                }
            }

        }
    }


    private fun isGPSEnabled(): Boolean {
        var locationManager: LocationManager? = null
        var isEnabled = false
        if (locationManager == null) {
            locationManager = this.getSystemService(Context.LOCATION_SERVICE) as LocationManager?
        }
        isEnabled = locationManager!!.isProviderEnabled(LocationManager.GPS_PROVIDER)
        return isEnabled
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        if (requestCode == 1) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                if (isGPSEnabled()) {

                    getCurrentLocation()

                } else {

                    turnOnGPS()
                }
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun navbarVisibility(isVisible: Int) {
        mainActivityBinding.bottomNavigationView.visibility = isVisible
        mainActivityBinding.btnOpenCart.visibility = isVisible
    }



    //////////////////



    override fun onResume() {
        super.onResume()
        localeDelegate.onResumed(this)

    }
    override fun getDelegate() = localeDelegate.getAppCompatDelegate(super.getDelegate())
    override fun onPause() {
        super.onPause()
        localeDelegate.onPaused()
    }

    override fun createConfigurationContext(overrideConfiguration: Configuration): Context {
        val context = super.createConfigurationContext(overrideConfiguration)
        return LocaleHelper.onAttach(context)
    }

    override fun getApplicationContext(): Context =
        localeDelegate.getApplicationContext(super.getApplicationContext())

    open fun updateLocale(locale: Locale) {
        sharedPreferencesManager.saveLanguage(locale.language)
        localeDelegate.setLocale(this@MainActivity, locale)

    }

    override fun onDestroy() {
        super.onDestroy()
        this.unregisterReceiver(netwokReciever)
    }

}