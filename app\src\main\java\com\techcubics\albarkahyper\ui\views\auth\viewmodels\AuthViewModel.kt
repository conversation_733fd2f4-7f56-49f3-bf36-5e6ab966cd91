package com.techcubics.albarkahyper.ui.views.auth.viewmodels

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.techcubics.data.model.pojo.ForgetPasswordConfirmResponseData
import com.techcubics.data.model.pojo.ForgetPasswordResponseData
import com.techcubics.data.model.pojo.SettingData
import com.techcubics.data.model.pojo.User
import com.techcubics.data.model.requests.auth.*
import com.techcubics.data.remote.BaseResponse
import com.techcubics.data.repos.auth.AuthRepoImpl
import com.techcubics.data.repos.auth.AuthRepo
import kotlinx.coroutines.launch


class AuthViewModel(private val authRepo: AuthRepo) : ViewModel() {

    private  val TAG = "AuthViewModel"
    val checkAuthorizationMutableLiveData = MutableLiveData<BaseResponse<User>?>()
    val loginMutableLiveData = MutableLiveData<BaseResponse<User>?>()
    val loginSocialMutableLiveData = MutableLiveData<BaseResponse<User>?>()
    val logoutMutableLiveData = MutableLiveData<BaseResponse<String>?>()
    val registerMutableLiveData = MutableLiveData<BaseResponse<User>?>()
    val forgetPasswordByEmailMutableLiveData =
        MutableLiveData<BaseResponse<ForgetPasswordResponseData>?>()
    val forgetPasswordByPhoneMutableLiveData =
        MutableLiveData<BaseResponse<ForgetPasswordResponseData>?>()
    val forgetPasswordConfirmMutableLiveData =
        MutableLiveData<BaseResponse<ForgetPasswordConfirmResponseData>?>()
    val forgetPasswordResetMutableLiveData = MutableLiveData<BaseResponse<Nothing>?>()
    val updatePasswordResetMutableLiveData = MutableLiveData<BaseResponse<Nothing>?>()
    val settingsMutableLiveData = MutableLiveData<BaseResponse<SettingData>?>()

    fun checkAuthorization() {

        viewModelScope.launch {
            val checkResponse = authRepo.checkAutherization()

            if (checkResponse != null) {
                checkAuthorizationMutableLiveData.postValue(checkResponse)
            }
        }

    }

    fun login(userData: LoginRequest) {

        viewModelScope.launch {
            val loginResponse = authRepo.login(userData)

            if (loginResponse != null) {
                loginMutableLiveData.postValue(loginResponse)
            }
        }

    }

    fun register(userData: RegisterRequest) {

        viewModelScope.launch {
            val registerResponse = authRepo.register(userData)

            if (registerResponse != null) {
                registerMutableLiveData.postValue(registerResponse)
            }

        }

    }

    fun loginSocial(request: SocialRequest) {

        viewModelScope.launch {
            val loginResponse = authRepo.socialLoginCall(request)

            if (loginResponse != null) {
                loginSocialMutableLiveData.postValue(loginResponse)
            }
        }

    }

    fun forgetPasswordByEmail(request: ForgetPasswordRequest) {

        viewModelScope.launch {
            val forgetPasswordResponse = authRepo.forgetPasswordEmailCall(request)

            if (forgetPasswordResponse != null) {
                forgetPasswordByEmailMutableLiveData.postValue(forgetPasswordResponse)
            }
        }

    }

    fun forgetPasswordByPhone(request: ForgetPasswordRequest) {

        viewModelScope.launch {
            val forgetPasswordResponse = authRepo.forgetPasswordPhoneCall(request)

            if (forgetPasswordResponse != null) {
                forgetPasswordByPhoneMutableLiveData.postValue(forgetPasswordResponse)
            }
        }

    }

    fun forgetPasswordConfirmCall(request: ForgetPasswordConfirmRequest) {
        viewModelScope.launch {
            val forgetPasswordConfirmResponse =
                authRepo.forgetPasswordConfirmCall(request)

            if (forgetPasswordConfirmResponse != null) {
                forgetPasswordConfirmMutableLiveData.postValue(forgetPasswordConfirmResponse)
            }
        }

    }

    fun forgetPasswordResetCall(request: ForgetPasswordResetRequest) {
        viewModelScope.launch {
            val forgetPasswordResetResponse =
                authRepo.forgetPasswordResetCall(request)
            if (forgetPasswordResetResponse != null) {
                forgetPasswordResetMutableLiveData.postValue(forgetPasswordResetResponse)
            }
        }
    }

    fun logout() {
        viewModelScope.launch {
            val logoutResponse = authRepo.logout()

            if (logoutResponse != null) {
                logoutMutableLiveData.postValue(logoutResponse)
            }
        }

    }

    fun updatePassword(request: UpdatePasswordRequest) {
        viewModelScope.launch {
            val updatePasswordResponse =
                authRepo.updatePasswordResetCall(request)

            if (updatePasswordResponse != null) {
                updatePasswordResetMutableLiveData.postValue(updatePasswordResponse)
            }
        }
    }
    fun getSettings() {

        viewModelScope.launch {
            val setting=authRepo.getSettings()
            if (setting != null) {
                settingsMutableLiveData.postValue(setting)
            }
        }

    }

}