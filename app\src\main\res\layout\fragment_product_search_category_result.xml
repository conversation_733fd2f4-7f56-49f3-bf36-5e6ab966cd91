<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.views.products.searchResult.ProductSearchCategoryResultFragment">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/linearLayoutCompat2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:layout_alignParentTop="true"
        android:layout_above="@id/include_qty_cart_info">

        <include

            android:id="@+id/toolbarSearchByCategory"
            layout="@layout/toolbar_fragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll_view"
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvSubCategories"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_sub_category" />

                <include
                    android:id="@+id/includeMostSales"
                    layout="@layout/include_most_sales"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"

                    />



                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp">

                    <TextView
                        android:id="@+id/tvCurrentSection"
                        style="@style/label_section_header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        tools:text="section name"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.26" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvProducts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        tools:itemCount="3"
                        app:layout_constraintTop_toBottomOf="@id/tvCurrentSection"
                        tools:listitem="@layout/item_product"


                        />

                </androidx.constraintlayout.widget.ConstraintLayout>



            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.core.widget.NestedScrollView>


        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/pagingLoadingImg"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="bottom"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:layout_constraintBottom_toBottomOf="@id/rvProducts"
            android:visibility="gone"
            android:background="@color/white"
            app:lottie_rawRes="@raw/lottie_pager_loading" />

    </androidx.appcompat.widget.LinearLayoutCompat>


    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />

    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone" />

    <include
        android:id="@+id/include_qty_cart_info"
        layout="@layout/include_qty_cart_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />


</RelativeLayout>


