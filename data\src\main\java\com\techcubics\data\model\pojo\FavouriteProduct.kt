package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class FavouriteProduct(
    @SerializedName("id")
     val id:Int,

    @SerializedName("name")
    val name:String,
    @SerializedName("product_id")
    val productID:Int,
    @SerializedName("description")
    val description:String,
    @SerializedName("price")
    val price:Float,
    @SerializedName("video")
    val video:String,

    @SerializedName("sizes")
    val Sizes:MutableList<Size>,
    @SerializedName("images")
    val Images:MutableList<Image>,

    @SerializedName("shop_id")
    val shopID:Int,
    @SerializedName("shop_name")
    val shopName:String,
    @SerializedName("shop_logo")
    val shopLogo:String,

    @SerializedName("rate")
    val rate:Float,
    @SerializedName("rate_count")
    val rate_count:Int,
    @SerializedName("minimum_order_number"   ) var minQty     : Int=1,
    @SerializedName("maximum_order_number"   ) var maxQty     : Int=1,
    @SerializedName("qty_cart") var qtyCart     : Int           = 0,
    @SerializedName("shop_minimum_order_price"   ) var minOrder     : Float=0f,
    @SerializedName("shop_districts"   ) var shopDistricts     : List<Districts?>? = null,
    @SerializedName("is_discount"          ) var isDiscount         : Boolean?          = null,
//    @SerializedName("discount_id"          ) var discountId         : Int?              = null,
//    @SerializedName("price_before"         ) var priceBefore        : Float?              = null,
//    @SerializedName("price_after"          ) var priceAfter         : Float?           = null,
//    @SerializedName("percent"              ) var percent            : Double?           = null
    @SerializedName("discounts"              ) var discount          : ArrayList<DiscountDto>?           = null,

    )


