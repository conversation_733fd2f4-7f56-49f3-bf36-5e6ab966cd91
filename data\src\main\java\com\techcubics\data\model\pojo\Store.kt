package com.techcubics.data.model.pojo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Store(

    @SerializedName("id")
    val id:Int,
    @SerializedName("shop_id")
    val shopID:Int,

    @SerializedName("name")
    val name:String,
    @SerializedName("description")
    val description:String,
    @SerializedName("logo")
    val logo:String,
    @SerializedName("address")
    val address:String,
    @SerializedName("is_fav")
    var isFav:Boolean,
    @SerializedName("rate")
    val rate:Float,

    @SerializedName("open")
    val isOpened:<PERSON><PERSON><PERSON>,
    @SerializedName("link_google_play")
    val linkGooglePlay:String,
    @SerializedName("link_apple_store")
    val linkAppleStore:String,
    @SerializedName("qr_image")
    val qrImage:String,
    @SerializedName("lat")
    val latitute:String,
    @SerializedName("lng")
    val longitude:String,

):Parcelable
