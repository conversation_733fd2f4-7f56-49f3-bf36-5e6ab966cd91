<vector android:autoMirrored="false" android:height="24dp"
    android:viewportHeight="24" android:viewportWidth="24"
    android:width="24dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#00000000"
        android:pathData="M6,3c1,0 3,0.6 3,3S7,9 6,9h12M6,3h12c1,0 3,0.6 3,3s-2,3 -3,3M6,3c-1,0 -3,0.6 -3,3v13.002C3,20.106 3.895,21 5,21h5.5M18,9v1M7,13h3m-3,4h3m4,2v-3m2,0h1.5a1.5,1.5 0,0 0,1.5 -1.5v0a1.5,1.5 0,0 0,-1.5 -1.5H14v3m2,0 l3,3m-3,-3h-2m7,5 l-2,-2m0,0 l2,-2m-2,2 l-2,2"
        android:strokeColor="@color/white" android:strokeLineCap="round"
        android:strokeLineJoin="round" android:strokeWidth="2"/>
</vector>
