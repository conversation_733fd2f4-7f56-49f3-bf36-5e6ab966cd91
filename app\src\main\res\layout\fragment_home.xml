<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.views.home.navFragments.home.fragments.HomeFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <include
                android:id="@+id/include_home_toolbar"
                layout="@layout/toolbar_home_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp" />


            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/mainView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvBanners"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_slider" />

                <ru.tinkoff.scrollingpagerindicator.ScrollingPagerIndicator
                    android:id="@+id/indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="8dp"
                    app:spi_dotColor="@color/color_gray_1"
                    app:spi_dotSelectedColor="@color/app_color"
                    app:spi_dotSelectedSize="12dp"
                    app:spi_dotSize="12dp"
                    app:spi_visibleDotCount="3"
                    app:spi_visibleDotThreshold="0" />

                <include
                    android:id="@+id/include_home_branch_types_section"
                    layout="@layout/include_home_branch_types"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"

                    />

            </androidx.appcompat.widget.LinearLayoutCompat>
            <!--            </LinearLayout>-->


        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

    <include
        android:id="@+id/placeholder"
        layout="@layout/include_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"/>

    <include
        android:id="@+id/loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone" />

        <include
            android:id="@+id/includeNetwork"
            android:visibility="gone"
            layout="@layout/include_no_network_layout" />
</RelativeLayout>