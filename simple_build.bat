@echo off
echo ========================================
echo   AlBarkaHyper APK Builder
echo ========================================
echo.

echo Checking current directory...
echo Current directory: %CD%
echo.

echo Checking if gradlew.bat exists...
if exist "gradlew.bat" (
    echo ✓ gradlew.bat found
) else (
    echo ✗ gradlew.bat NOT found!
    echo Please make sure you are in the correct project directory.
    pause
    exit /b 1
)
echo.

echo Checking Java installation...
java -version 2>nul
if %ERRORLEVEL% neq 0 (
    echo ✗ Java not found or not in PATH
    echo Please install Java JDK 17 or higher
    echo Download from: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✓ Java found
)
echo.

echo Starting build process...
echo.

echo Step 1: Cleaning project...
call gradlew.bat clean
if %ERRORLEVEL% neq 0 (
    echo ✗ Clean failed!
    echo Check the error messages above.
    pause
    exit /b 1
) else (
    echo ✓ Clean completed successfully
)
echo.

echo Step 2: Building Release APK...
call gradlew.bat assembleRelease
if %ERRORLEVEL% neq 0 (
    echo ✗ APK build failed!
    echo Check the error messages above.
    pause
    exit /b 1
) else (
    echo ✓ APK build completed successfully
)
echo.

echo Step 3: Building Release Bundle (AAB)...
call gradlew.bat bundleRelease
if %ERRORLEVEL% neq 0 (
    echo ✗ Bundle build failed!
    echo APK was built successfully, but Bundle failed.
    echo You can still use the APK file.
) else (
    echo ✓ Bundle build completed successfully
)
echo.

echo ========================================
echo   Build Process Completed!
echo ========================================
echo.

echo Checking output files...
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✓ APK file created: app\build\outputs\apk\release\app-release.apk
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo   File size: %%~zI bytes
) else (
    echo ✗ APK file NOT found!
)

if exist "app\build\outputs\bundle\release\app-release.aab" (
    echo ✓ AAB file created: app\build\outputs\bundle\release\app-release.aab
    for %%I in ("app\build\outputs\bundle\release\app-release.aab") do echo   File size: %%~zI bytes
) else (
    echo ✗ AAB file NOT found!
)
echo.

echo Full paths:
echo APK: %CD%\app\build\outputs\apk\release\app-release.apk
echo AAB: %CD%\app\build\outputs\bundle\release\app-release.aab
echo.

echo ========================================
pause
