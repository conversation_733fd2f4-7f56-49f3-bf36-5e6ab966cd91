package com.techcubics.albarkahyper.common

import android.util.Log
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.Adapter
import kotlinx.coroutines.*
import kotlinx.coroutines.Dispatchers.Main
import java.util.*
import kotlin.coroutines.coroutineContext

class Slider(
    private val recyclerView: RecyclerView,
    private val delay: Long
) {


    private lateinit var job: Job

    private var isScrollDirectionToEnd = true

    private lateinit var adapter: Adapter<*>

    private var startTimer = true

    private val layoutManager: LinearLayoutManager by lazy {
        recyclerView.layoutManager as LinearLayoutManager
    }

    private var incrementValue = 1

    companion object {
        private var TAG = "Slider_For_RV"
    }

    fun setSnapHelper() {
        recyclerView.onFlingListener = null
        val snapHelper = LinearSnapHelper()
        snapHelper.attachToRecyclerView(recyclerView)
    }

    fun setAdapter(adapter: Adapter<*>) {
        this.adapter = adapter
    }

    init {
        setTimer()
        onSliderScroll()
    }

    fun start() {
        startTimer = true
    }

    private fun setTimer() {
        job = CoroutineScope(Dispatchers.IO).launch {
            while (startTimer) {
                delay(delay)
                updateUI()
            }
        }
    }

    private suspend fun updateUI() {
        withContext(Main) {
            if (layoutManager.findLastCompletelyVisibleItemPosition() in 0 until adapter.itemCount) {
                setScroll(layoutManager.findLastCompletelyVisibleItemPosition() + incrementValue)
            }
            resetDirection()
        }
    }

    private fun resetDirection() {
        if (isListDirectionBoundary()) {
            isScrollDirectionToEnd = !isScrollDirectionToEnd
            incrementValue *= -1
        }
    }

    private fun isListDirectionBoundary(): Boolean =
        (layoutManager.findLastCompletelyVisibleItemPosition() + 1 ==
                adapter.itemCount && isScrollDirectionToEnd) ||
                (layoutManager.findLastCompletelyVisibleItemPosition() == 0 && !isScrollDirectionToEnd)

    private fun setScroll(position: Int) {
        if (!isListDirectionBoundary()) {
            layoutManager.smoothScrollToPosition(
                recyclerView,
                RecyclerView.State(),
                position
            )
        }
    }

    private fun onSliderScroll() {
        recyclerView.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    start()
                } else {
                    pause()
                }
            }
        })
    }

    fun pause() {
        startTimer = false
    }

    fun stop() {
        job.cancel()
    }
}