<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/sc_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp">
                <include

                    android:id="@+id/searchview"
                    layout="@layout/include_order_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/past_recycleview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:layout_editor_absoluteX="0dp"
                    android:layout_marginTop="23dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    tools:layout_editor_absoluteY="72dp"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
                    app:layout_constraintTop_toBottomOf="@id/searchview"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:listitem="@layout/item_order"
                    />
            </LinearLayout>


        </androidx.core.widget.NestedScrollView>
        <include
            android:id="@+id/past_error_layout"
            layout="@layout/include_placeholder"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />


        <include
            android:id="@+id/loading_animation"
            layout="@layout/include_page_loading_animation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>