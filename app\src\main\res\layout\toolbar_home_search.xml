<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    app:elevation="0dp"
    android:padding="0dp"
    android:elevation="0dp"

    >
      <androidx.appcompat.widget.Toolbar
          android:layout_width="match_parent"
          android:layout_height="match_parent"


          >
          <androidx.constraintlayout.widget.ConstraintLayout
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:layout_marginEnd="16dp"

              >

              <TextView
                  android:id="@+id/tvWelcome"
                  style="@style/toolbar_home_search_welcome"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_gravity="center"
                  android:layout_margin="8dp"
                  android:text="@string/main_toolbar_title"
                  android:textStyle="bold"
                  app:layout_constraintStart_toStartOf="parent"
                  app:layout_constraintTop_toTopOf="parent" />

              <TextView
                  android:id="@+id/tvAppName"
                  style="@style/toolbar_home_search_app_name"
                  android:layout_width="0dp"
                  android:layout_height="wrap_content"
                  android:layout_marginTop="8dp"
                  android:layout_marginEnd="16dp"
                  android:text="@string/app_name_title"

                  android:textAllCaps="true"
                  app:layout_constraintBottom_toBottomOf="parent"
                  app:layout_constraintEnd_toStartOf="@+id/btnNotifications"
                  app:layout_constraintStart_toStartOf="@+id/tvWelcome"
                  app:layout_constraintTop_toBottomOf="@+id/tvWelcome" />


              <ImageButton
                  android:id="@+id/btnNotifications"
                  android:layout_width="@dimen/icons_size_height_weight_9"
                  android:layout_height="@dimen/icons_size_height_weight_9"
                  android:background="?attr/selectableItemBackground"
                  android:src="@drawable/ic_notifications"
                  app:layout_constraintBottom_toBottomOf="@+id/tvAppName"
                  app:layout_constraintEnd_toEndOf="parent"
                  app:layout_constraintTop_toTopOf="@+id/tvAppName" />

              <TextView
                  android:id="@+id/tvUnReadNo"
                  style="@style/chat_room_unread"
                  android:layout_width="24dp"
                  android:layout_height="24dp"
                  android:layout_gravity="center_vertical"
                  android:background="@drawable/circle_red"
                  android:ellipsize="end"
                  android:gravity="center"
                  app:layout_constraintEnd_toEndOf="@+id/btnNotifications"
                  app:layout_constraintTop_toBottomOf="@+id/tvWelcome"
                  tools:text="1" />




          </androidx.constraintlayout.widget.ConstraintLayout>
      </androidx.appcompat.widget.Toolbar>


</com.google.android.material.appbar.AppBarLayout>




