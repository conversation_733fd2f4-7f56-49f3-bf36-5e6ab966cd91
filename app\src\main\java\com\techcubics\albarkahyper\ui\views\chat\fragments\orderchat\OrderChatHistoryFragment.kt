package com.techcubics.albarkahyper.ui.views.chat.fragments.orderchat

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.OrderMessageHistoryData
import com.techcubics.data.model.pojo.OrderMessageOrderInfoData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.IPageRowset
import com.techcubics.albarkahyper.common.NavigationBarVisibilityListener
import com.techcubics.albarkahyper.common.PopupDialog
import com.techcubics.albarkahyper.databinding.FragmentChatHistoryBinding
import com.techcubics.albarkahyper.ui.adapters.chat.ChatHistoryAdapter
import com.techcubics.albarkahyper.ui.views.chat.viewmodels.ChatViewModel
import com.techcubics.shared.constants.Constants
import com.techcubics.shared.enums.LottieIconEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import androidx.navigation.fragment.navArgs

class OrderChatHistoryFragment :  Fragment() , IPageRowset<OrderMessageHistoryData> {

    private  lateinit var binding: FragmentChatHistoryBinding
    private lateinit var chatHistoryAdapter: ChatHistoryAdapter<OrderMessageHistoryData>
    private val chatViewModel by viewModel<ChatViewModel>()
    private val SharedPreferencesManager: SharedPreferencesManager by inject()
    private lateinit var popupDialog: PopupDialog
    private var orderID:Int?=null
    private  var empName:String?=null
    private val args:OrderChatHistoryFragmentArgs? by navArgs()

    private  val TAG = "OrderChatHistoryFragment"
    companion object {
        val instance: OrderChatHistoryFragment = OrderChatHistoryFragment()
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding= FragmentChatHistoryBinding.inflate(inflater,container,false)
        init()
        observers()
        events()
        return binding.root
    }

    override fun init() {

        popupDialog = PopupDialog()
        popupDialog.init(requireContext())
        arguments?.let { arg->
            
            orderID = arg.getInt(Constants.INTENT_ID, -1)
            empName = arg.getString(Constants.INTENT_NAME)
            chatViewModel.getRoomMessagesHistory(orderID!!)

            binding.toolbar.tvTitle.text=empName


       /*binding.toolbar.tvTitle.setCompoundDrawablesWithIntrinsicBounds(0,0,
           com.techcubics.style.R.drawable.ic_support_user,0)*/

        }
        //
        showHidePlaceHolder(show=false,type= null,message=null)
        binding.rvHistory.visibility=View.GONE
        Helper.loadingAnimationVisibility(View.VISIBLE,binding.loadingAnimation.root)
    }
    override  fun observers(){

        chatViewModel.orderMessagesResponse.observe(viewLifecycleOwner, Observer {

//            binding.shimmerID.stopShimmer()
//            binding.shimmerID.visibility=View.GONE
Helper.loadingAnimationVisibility(View.GONE,binding.loadingAnimation.root)
            try {
                if (it.status!!) {

                    if(it.data!=null){

                        it?.data!!.info.let { f->
                            it?.data!!.messages.add(getDefaultMessage(f))

                            showData(it?.data!!.messages!!)
                            binding.rvHistory.visibility = View.VISIBLE

                            try {
                                args?.refresh?.perform()
                            }catch (ex:Exception){}

                        }



                    }else {
                      
                        //empty
                        showHidePlaceHolder(
                            show = true,
                            type = LottieIconEnum.Error,
                            message = it.message
                        )

                    }

                } else {
                    
                    //error
                    showHidePlaceHolder(
                        show = true,
                        type = LottieIconEnum.Error,
                        message = it.message
                    )
                }

            } catch (ex: Exception) {

                //error
                showHidePlaceHolder(show = true, type = LottieIconEnum.Error, message = ex.message)
            }


        })

        chatViewModel.orderSendResponse.observe(viewLifecycleOwner, Observer {

            binding.includeChatSendBox.btnSend.isEnabled=true

            try {
                if(it.status!!){

                    chatViewModel.getRoomMessagesHistory(orderID = orderID!!)
                    binding.includeChatSendBox.txtMessage.setText("")

                }else{

                    //error
                    Helper.showDialog(requireContext(),it.message!!)
                }

            }catch(ex:Exception) {
                //error
                Helper.showDialog(requireContext(),ex.message.toString())
            }
        })


    }
    override fun events(){

        binding.toolbar.tvTitle.setOnClickListener {

            findNavController().popBackStack()
        }

        binding.toolbar.btnBack.setOnClickListener {

            findNavController().popBackStack()
        }

        binding.includeChatSendBox.btnSend.setOnClickListener{

            if(binding.includeChatSendBox.txtMessage.text.isNotEmpty() && binding.includeChatSendBox.txtMessage.text.isNotBlank()){

                binding.includeChatSendBox.btnSend.isEnabled=false
                chatViewModel.sendToOrder(binding.includeChatSendBox.txtMessage.text.trim().toString(), orderID = orderID!!)

            }

        }
    }
    override fun showData( items:List<OrderMessageHistoryData>){



        chatHistoryAdapter = ChatHistoryAdapter<OrderMessageHistoryData>()
        chatHistoryAdapter.setItemsList(items)
        binding.rvHistory.adapter = chatHistoryAdapter
        binding.rvHistory.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL,true)

        binding.scroller.postDelayed(Runnable {
            binding.scroller.fullScroll(View.FOCUS_DOWN)
        }, 100)


    }
    override fun showHidePlaceHolder(show:Boolean, type: LottieIconEnum?, message:String?,
                                     container: View?){

        if(show) {
            binding.placeholder.root.visibility=View.VISIBLE
            when (type) {
                LottieIconEnum.Empty -> {
                    binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.empty_box_lottie)
                    binding.placeholder.tvMessage.text = message
                }
                LottieIconEnum.Error -> {

                    if(message?.contains(getString(com.techcubics.style.R.string.unauthenticated))!!){
                        CoroutineScope(Dispatchers.Main).launch {
                            binding.chathistoryLayout.visibility = View.GONE
                            popupDialog.showSessionExpiredDialog(requireContext())
                            delay(1200)
                            popupDialog.onDismiss()
                            Navigation.findNavController(requireView()).navigate(com.techcubics.albarkahyper.R.id.go_to_login)
                        }
                    }else{
                        binding.placeholder.icon.setAnimation(com.techcubics.style.R.raw.lottie_error)
                        binding.placeholder.tvMessage.text = message
                    }
                } else -> throw IllegalStateException("error")
            }
        }else{

            binding.placeholder.root.visibility=View.GONE
        }
    }

    fun getDefaultMessage(f: OrderMessageOrderInfoData):OrderMessageHistoryData{
       // Log.i("here",f.locationInfo.firstName+f.locationInfo.secondName)

        // iso 8601 date
       /*val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        val l: Date = format.parse(f.createdDate)
        var formatter = SimpleDateFormat("dd/MM/yyyy").format(l)
        var timeFormat = SimpleDateFormat("hh:mm aa").format(l)*/
        var fullName:String
//        when (SharedPreferencesManager.getLanguage()){
//            "ar"-> fullName="${f.locationInfo.secondName} ${f.locationInfo.firstName}"
//            "eg"-> fullName="${f.locationInfo.secondName} ${f.locationInfo.firstName}"
//            "sa"-> fullName="${f.locationInfo.secondName} ${f.locationInfo.firstName}"
//            else -> fullName="${f.locationInfo.firstName} ${f.locationInfo.secondName}"
//        }
        fullName = "${f.customer_name}"
        Log.d(TAG, "observers: ${SharedPreferencesManager.getLanguage()}")

        var msg=  String.format(getString(com.techcubics.style.R.string.welcome_messages_order),fullName,f.code,f.date)

        return OrderMessageHistoryData(message = msg, date =f.date, time = f.time)
    }


    override fun onStart() {
        super.onStart()
        val navbarActivity = requireActivity() as NavigationBarVisibilityListener
        navbarActivity.navbarVisibility(View.GONE)
    }

}