package com.techcubics.data.model.pojo

import com.google.gson.annotations.SerializedName

data class FavouriteStore(

    @SerializedName("id")
    val id:Int,
    @SerializedName("furniture_id")
    val furnitureID:Int,
    @SerializedName("name")
    val name:String,
    @SerializedName("shop_id")
    val shopID:Int,
    @SerializedName("shop_logo")
    val logo:String,
    @SerializedName("shop_address")
    val address:String,
    @SerializedName("shop_description")
    val description:String,
    @SerializedName("shop_lat")
    val latitude:String,
    @SerializedName("shop_lng")
    val longitude:String,
    @SerializedName("rate")
    val rate:Float,
    @SerializedName("rate_count")
    val rateCount:Int,
    @SerializedName("open")
    val isOpened:Boolean,

    @SerializedName("shop_districts"   ) var shopDistricts     : List<Districts?>? = null,



    )
