<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardPreventCornerOverlap="true"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout5"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_round_gray"
        >

        <ImageView
            android:id="@+id/imgThumb"
            android:layout_width="0dp"
            android:layout_height="200dp"
            android:background="@color/color_gray_15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/portrait_placeholder" />

        <TextView
            android:id="@+id/tvDiscountTag"
            style="@style/label_item_discount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_price"
            android:paddingStart="16dp"
            android:paddingTop="4dp"
            android:paddingEnd="16dp"
            android:paddingBottom="4dp"
            android:gravity="center"
            android:text="@string/discount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnAddFav"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:contentDescription="@string/add_to_favorite"
            android:src="@drawable/ic_add_favorite"
            app:backgroundTint="@color/white"
            app:fabSize="mini"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/label_item_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textAlignment="center"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/min_qty_cont"
            app:layout_constraintTop_toBottomOf="@+id/imgThumb"
            tools:text="مياه غازية - 1.5 لترمياه غازية - 1.5 لتر" />

        <TextView
            android:id="@+id/tvDescription"
            style="@style/label_item_product_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textAlignment="center"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            tools:text="كرتونة الكرتونة 12 قطعة" />

        <LinearLayout
            android:id="@+id/min_qty_cont"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/max_qty_cont"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">

            <TextView
                style="@style/label_item_product_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/min_num" />

            <TextView
                android:id="@+id/min_qty"
                style="@style/label_item_product_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:textColor="@color/app_color"
                tools:text="5" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/max_qty_cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/priceLayout"
            app:layout_constraintTop_toBottomOf="@+id/min_qty_cont">

            <TextView
                style="@style/label_item_product_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/max_num_title" />

            <TextView
                android:id="@+id/max_qty"
                style="@style/label_item_product_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:textColor="@color/app_color"
                tools:text="20" />
        </LinearLayout>


        <FrameLayout
            android:id="@+id/priceLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginVertical="2dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/btnOrder"
            app:layout_constraintTop_toBottomOf="@+id/max_qty_cont">

            <TextView
                android:id="@+id/tvCurrentPrice"
                style="@style/label_item_price_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textAlignment="center"
                android:textColor="@color/app_color"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="10000 جنيه" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/priceDiscountLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tvBeforeDiscountPrice"
                    style="@style/label_item_old_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:textSize="12sp"
                    android:layout_marginHorizontal="2dp"
                    android:background="@drawable/line_diagonal"
                    app:layout_constraintBottom_toBottomOf="@+id/tvAfterDiscountPrice"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tvAfterDiscountPrice"
                    app:layout_constraintTop_toTopOf="@+id/tvAfterDiscountPrice"
                    tools:text="50.99 جنيه" />

                <TextView
                    android:id="@+id/tvAfterDiscountPrice"
                    style="@style/label_item_price_amount"
                    android:textSize="13sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:layout_marginHorizontal="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tvBeforeDiscountPrice"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="41.99 جنيه" />


            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>


        <include
            android:id="@+id/btnOrder"
            layout="@layout/btn_progress_circle"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/quantity_cont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/btnOrder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/btnOrder">

            <include
                android:id="@+id/quantity_controller"
                layout="@layout/include_input_quantity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>