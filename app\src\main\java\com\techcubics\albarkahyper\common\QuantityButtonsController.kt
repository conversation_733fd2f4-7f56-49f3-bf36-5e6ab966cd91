package com.techcubics.albarkahyper.common

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import com.techcubics.albarkahyper.databinding.IncludeInputQuantityBinding
import com.techcubics.albarkahyper.ui.views.products.details.fragments.OnItemsChangedListener
import com.techcubics.style.R

class QuantityButtonsController(
    view: ConstraintLayout,
    private val maxOrderNote: TextView? = null,
    private val context: Context,
    private val listener: OnItemsChangedListener? = null,
    val maxQty: Int,
    val minQty: Int
) : View.OnClickListener {

    private var isItem: Boolean = false
    private var updateQuantity: ((Int) -> Unit)? = null
    private var removeItemFromCart: (() -> Unit)? = null
    private var isCart: Boolean = false
    private var qty: Int = 1
    private var binding = IncludeInputQuantityBinding.bind(view)
    init {
        binding.btnIncrease.setOnClickListener(this)
        binding.btnDecrease.setOnClickListener(this)
        binding.btnDelete.setOnClickListener(this)

    }
    fun setIsItem(updateQuantity: (Int)-> Unit, removeCartItem:()->Unit) {
        isItem = true
        this.updateQuantity = updateQuantity
        this.removeItemFromCart = removeCartItem
    }

    fun setQuantity() {
        this.qty = minQty
        setMaxOrderNum()
        binding.productQuantity.text = minQty.toString()
    }

    fun updateQuantity(qty:Int) {
        this.qty = qty
        setMaxOrderNum()
        binding.productQuantity.text = qty.toString()
    }

    fun getQuantity(): Int {
        return qty
    }

    fun setExtras(
        isCart: Boolean = false,
        updateQuantity: ((Int) -> Unit)? = null
    ) {
        this.isCart = isCart
        this.updateQuantity = updateQuantity
    }

    private fun setQuantityValue(offset: Int, isCart: Boolean=false, updateQuantity: ((Int) -> Unit)?) {
        qty = binding.productQuantity.text.toString().toInt()
        if (offset < 0 && qty <= minQty) return
        if (offset > 0 && qty >= maxQty) return
        qty += offset
        binding.productQuantity.text = qty.toString()
        binding.btnDecrease.isEnabled = qty > minQty
        binding.btnIncrease.isEnabled = qty < maxQty
        isBtnDecreaseEnabled()
        isBtnIncreaseEnabled()
        setMaxOrderNum()
        if (isCart) {
            updateQuantity!!(qty)
        }

    }
    private fun setItemQuantityValue(offset: Int) {
        qty = binding.productQuantity.text.toString().toInt()
        if (qty<= minQty&&offset<0){
            qty-=minQty
            binding.productQuantity.text = qty.toString()
            removeItemFromCart?.let { it() }
            return
        }
        if (qty>= maxQty&&offset>0)return
        qty += offset
        binding.productQuantity.text = qty.toString()
        isBtnDelete()
        isBtnIncreaseEnabled()
        updateQuantity!!(qty)

    }
    fun isBtnDelete(){
        if (qty<=minQty) {
            binding.btnDelete.visibility = View.VISIBLE
            binding.btnDecrease.visibility = View.INVISIBLE
        }else{
            binding.btnDelete.visibility = View.INVISIBLE
            binding.btnDecrease.visibility = View.VISIBLE
        }
    }
    private fun setMaxOrderNum() {
        maxOrderNote?.text = context.getString(R.string.min_order_number,maxQty.toString())
        if (getQuantity()> maxQty){
            maxOrderNote?.setTextColor(context.getColor(R.color.color_red_3))
        }else{
            maxOrderNote?.setTextColor(context.getColor(R.color.color_59))
        }
    }
    fun isBtnDecreaseEnabled() {
        if (qty <= minQty) {
            binding.btnDecrease.background = AppCompatResources.getDrawable(context,R.drawable.rect_quantity_controllers_disabled)
        } else {
            binding.btnDecrease.background=AppCompatResources.getDrawable(context,R.drawable.rect_quantity_controllers)
        }
    }
    fun isBtnIncreaseEnabled() {
        if (qty >= maxQty) {
            binding.btnIncrease.background = AppCompatResources.getDrawable(context,R.drawable.rect_quantity_controllers_disabled)
        } else {
            binding.btnIncrease.background=AppCompatResources.getDrawable(context,R.drawable.rect_quantity_controllers)
        }
    }

    override fun onClick(p0: View?) {
        val offset = when (p0) {
            binding.btnDecrease,binding.btnDelete -> -1
            else -> 1
        }
        if (isItem){
            setItemQuantityValue(offset)
        }else{
            setQuantityValue(offset, isCart, updateQuantity)
            listener?.calcPrice()
        }
    }
}