package com.techcubics.albarkahyper.ui.adapters.holders.store

import android.content.Context
import android.os.CountDownTimer
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.data.local.SharedPreferencesManager
import com.techcubics.data.model.pojo.Save
import com.techcubics.data.model.pojo.StoresDetailsSavesData
import com.techcubics.albarkahyper.common.Helper
import com.techcubics.albarkahyper.common.Helper.getActivity
import com.techcubics.albarkahyper.databinding.ItemStoreSavesBinding
import com.techcubics.albarkahyper.ui.adapters.product.OfferProductsAdapter
import com.techcubics.shared.constants.EndPointConstants
import com.techcubics.shared.enums.RateTypesEnum
import com.techcubics.albarkahyper.common.IOnAdapterItemClickHandler
import com.techcubics.albarkahyper.common.QuantityButtonsController
import org.koin.android.ext.android.get
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

class SaveHolderItem<T>(
    val binding: ItemStoreSavesBinding,
    val context: Context,
    val margin: Int = 0,
    val clickHandler: IOnAdapterItemClickHandler
) : RecyclerView.ViewHolder(binding.root) {

    private val TAG = "SaveHolderItem"
//    lateinit var progressButton: CircleProgressButton
    private val SharedPreferencesManager: SharedPreferencesManager by lazy{
        (context.getActivity())!!.get<SharedPreferencesManager>()
    }
    private lateinit var quantityButtonsController: QuantityButtonsController
    private lateinit var modelType: String
    private var saveId = -1
    private var savePrice=0f
    private var qtyCart=0
    private var maxQty=0
    fun bind(data: T) {

        when (SharedPreferencesManager.getLanguage()) {
            "ar" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "eg" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            "sa" -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
            else -> binding.root.layoutParams = Helper.setMargin(binding.root, margin, 0, margin, 0)
        }
//        progressButton = CircleProgressButton(context)
//        progressButton.initRoundButton(binding.btnOrder)
        when (data) {
            is Save -> {
                fillByGeneral(data)
            }
            is StoresDetailsSavesData -> {
                fillByStore(data)
            }
        }
        quantityButtonsController = QuantityButtonsController(
            binding.quantityController.root,
            null,
            context,
            maxQty = Int.MAX_VALUE,
            minQty = 1
        )
        quantityButtonsController.setIsItem(updateQuantity = ::updateQuantity,
            removeCartItem=::removeCartItem)
        quantityButtonsController.updateQuantity(qtyCart)
        quantityButtonsController.isBtnDelete()

        if (qtyCart>0){
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility = View.VISIBLE
        }else{
            binding.btnOrder.root.visibility = View.VISIBLE
            binding.quantityCont.visibility = View.INVISIBLE
        }

    }

    private fun fillByGeneral(save: Save) {

        modelType = RateTypesEnum.Save.value
        saveId=save.saveID
        savePrice=save.price
        qtyCart = save.quantityCart
        maxQty = 1

        if (save.Images.size>0) {Helper.loadImage(context, save.Images[0].path, binding.imgThumb)}

        ////
        binding.tvTitle.text = save.name
        binding.tvCurrentPrice.text =
            "${save.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        //

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        /*val start="2022-07-08 12:00:00"
        val end="2022-07-08 12:01:00"*/

        val start = save.startDate + " " + save.fromTime
        val end = save.endDate + " " + save.toTime

        //
        val from = LocalDateTime.parse(start, formatter)
        val to = LocalDateTime.parse(end, formatter)

        val fromPeriod = Date.from(from.atZone(ZoneId.systemDefault()).toInstant())
        val toPeriod = Date.from(to.atZone(ZoneId.systemDefault()).toInstant())

        val diff: Long = toPeriod.getTime() - fromPeriod.getTime()


        //   Log.d(TAG, "bind: ${minutes*-1}" )


        if(save.timer!=null){
            save.timer.cancel()
        }

        save.timer= object :CountDownTimer(diff, 1000) {

            // Callback function, fired on regular interval
            override fun onTick(millisUntilFinished: Long) {

                /* val seconds = millisUntilFinished / 1000 % 60
                 val minutes = seconds/ 60
                 val hours = minutes / 60
                 val days = hours / 24*/

                val x=  Helper.formatToDigitalClock(millisUntilFinished).split(':')
                binding.includeTimerSection.tvHours.text="${x[0]}"
                binding.includeTimerSection.tvMinutes.text="${x[1]}"
                binding.includeTimerSection.tvSeconds.text="${x[2]}"


            }

            // Callback function, fired
            // when the time is up
            override fun onFinish() {
                // textView.setText("done!")
            }
        }.start()

        //
        val productsAdapter: OfferProductsAdapter = OfferProductsAdapter()
        productsAdapter.items = save.products
        binding.rvOfferProducts.adapter = productsAdapter
        val spanCount = if(save.products.size>3) 3 else save.products.size
        binding.rvOfferProducts.layoutManager =

            GridLayoutManager(context, spanCount)

        itemView.setOnClickListener {
            clickHandler.onItemClicked(save.id, RateTypesEnum.Save.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
//            progressButton.btnRoundActivated()
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility=View.VISIBLE
            quantityButtonsController.updateQuantity(1)
            clickHandler.addToCart(
                EndPointConstants.add_save,
                -1,
                save.model_type,
                save.saveID,
                1,
                savePrice,
                maxQty,
                1,
                0f,
                bindingAdapterPosition
            )
        }
    }

    private fun fillByStore(save: StoresDetailsSavesData) {

        modelType = RateTypesEnum.Save.value
        saveId=save.saveID
        savePrice=save.price
        qtyCart = save.quantityCart
        maxQty = 1

        // binding.logo.visibility= View.GONE
        //binding.tvBranchName.visibility= View.GONE

        if (save.images.size>0) { Helper.loadImage(context, save.images[0].path, binding.imgThumb)}


        ////

        binding.tvTitle.text = save.name
        binding.tvCurrentPrice.text =
            "${save.price} ${context.getString(com.techcubics.style.R.string.currency_name)}"
        //


        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        /* val start="2022-07-08 12:12:00"
         val end="2022-07-08 13:12:00"*/

        val start = save.startDate + " " + save.fromTime
        val end = save.endDate + " " + save.toTime

        //
        val from = LocalDateTime.parse(start, formatter)
        val to = LocalDateTime.parse(end, formatter)

        val fromPeriod = Date.from(from.atZone(ZoneId.systemDefault()).toInstant())
        val toPeriod = Date.from(to.atZone(ZoneId.systemDefault()).toInstant())

        val diff: Long = toPeriod.getTime() - fromPeriod.getTime()


        //   Log.d(TAG, "bind: ${minutes*-1}" )


        if(save.timer!=null){
            save.timer.cancel()
        }

        save.timer= object :CountDownTimer(diff, 1000) {

            // Callback function, fired on regular interval
            override fun onTick(millisUntilFinished: Long) {

                /* val seconds = millisUntilFinished / 1000 % 60
                 val minutes = seconds/ 60
                 val hours = minutes / 60
                 val days = hours / 24*/

                val x=  Helper.formatToDigitalClock(millisUntilFinished).split(':')
                binding.includeTimerSection.tvHours.text="${x[0]}"
                binding.includeTimerSection.tvMinutes.text="${x[1]}"
                binding.includeTimerSection.tvSeconds.text="${x[2]}"


            }

            // Callback function, fired
            // when the time is up
            override fun onFinish() {
                // textView.setText("done!")
            }
        }.start()

        //
        val productsAdapter: OfferProductsAdapter = OfferProductsAdapter()
        productsAdapter.items = save.products
        binding.rvOfferProducts.adapter = productsAdapter
        val spanCount = if(save.products.size>3) 3 else save.products.size
        binding.rvOfferProducts.layoutManager =
            GridLayoutManager(context,spanCount)

        itemView.setOnClickListener {
            clickHandler.onItemClicked(save.id, RateTypesEnum.Save.value)
        }
        binding.btnOrder.constraintsLayout.setOnClickListener {
//            if(SharedPreferencesManager.isLoggedIn() == "true"){
//                progressButton.btnRoundActivated()
//            }
            binding.btnOrder.root.visibility = View.INVISIBLE
            binding.quantityCont.visibility=View.VISIBLE
            quantityButtonsController.updateQuantity(1)
            clickHandler.addToCart(
                EndPointConstants.add_save,
                -1,
                save.model_type,
                save.saveID,
                1,
                savePrice,
                maxQty,
                1,
                0f,
                bindingAdapterPosition
            )
        }
    }

    private fun updateQuantity(
        qty: Int,
    ) {
        clickHandler.addToCart(
            EndPointConstants.add_save,
            -1,
            modelType,
            saveId,
            qty,
            savePrice,
            maxQty,
            1,
            0f,
            bindingAdapterPosition
        )
    }
    private fun removeCartItem(){
        binding.btnOrder.root.visibility = View.VISIBLE
        binding.quantityCont.visibility=View.INVISIBLE
        clickHandler.removeItemFromCart(
            modelType,
            saveId,
            0f,
            bindingAdapterPosition
        )
    }
}