package com.techcubics.albarkahyper.ui.adapters.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.techcubics.albarkahyper.common.StoreCategoryOnItemClickListener
import com.techcubics.albarkahyper.databinding.ItemCategoryBinding
import com.techcubics.albarkahyper.ui.adapters.holders.home.CategoryHolderItem


class CategoriesAdapter<T>(val onCategoryListener:StoreCategoryOnItemClickListener?=null): RecyclerView.Adapter<CategoryHolderItem<T>>() {
    lateinit var items:List<T>

  fun setItemsList(_items:List<T>) {
      items = _items
  }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryHolderItem<T> {
        val itemBinding = ItemCategoryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return CategoryHolderItem(itemBinding,parent.context,onCategoryListener=onCategoryListener)
    }

    override fun onBindViewHolder(holder: CategoryHolderItem<T>, position: Int) {

        holder.bind(items.get(position))

    }

    override fun getItemCount(): Int {
       return items.size
    }
}