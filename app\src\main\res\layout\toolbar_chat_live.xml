<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="0dp"
    android:background="@color/white"
    >

    <androidx.appcompat.widget.Toolbar

        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@drawable/bg_popup_live_dialog"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="8dp"


            >


            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="@dimen/icons_size_height_weight_9"
                android:layout_height="@dimen/icons_size_height_weight_9"
                android:layout_marginEnd="16dp"
                android:background="@null"
                android:scaleType="centerInside"
                android:src="@drawable/ic_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <com.mikhaellopez.circularimageview.CircularImageView
                android:id="@+id/photo"
                android:layout_width="@dimen/icons_size_height_weight_5"
                android:layout_height="@dimen/icons_size_height_weight_5"
                android:layout_marginStart="8dp"
                android:scaleType="centerCrop"
                app:civ_border="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btnBack"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/ic_user" />

            <TextView

                android:id="@+id/textView4"
                style="@style/toolbar_live_chat_caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginStart="8dp"
                android:drawablePadding="10dp"
                app:layout_constraintStart_toEndOf="@+id/photo"
                app:layout_constraintTop_toTopOf="@+id/photo"
                android:text="@string/chat_title" />

            <TextView
                android:id="@+id/tvTitle"
                style="@style/toolbar_live_chat_caption"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginStart="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="8dp"
                android:drawablePadding="10dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/photo"
                app:layout_constraintTop_toBottomOf="@+id/textView4"
                app:layout_constraintVertical_bias="0.0"
                tools:text="testtesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest" />
        </androidx.constraintlayout.widget.ConstraintLayout>






    </androidx.appcompat.widget.Toolbar>




</com.google.android.material.appbar.AppBarLayout>

