package com.techcubics.albarkahyper.common

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.ViewGroup
import android.widget.TextView
import com.techcubics.albarkahyper.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class BottomSheetAlertDialog {

    private lateinit var dialog: Dialog
    private lateinit var mcontext: Context

    fun init(context: Context) {
        dialog = Dialog(context)
       mcontext = context
    }

    fun showDialog(message : String){
        dialog.setContentView(R.layout.bottomsheet_layout)
        val alertMessage : TextView = dialog.findViewById(R.id.alert_message)

        alertMessage.text = message

        dialog.show()
        dialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.WRAP_CONTENT)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.window?.attributes?.windowAnimations = com.techcubics.style.R.style.dialog_animation
        dialog.window?.setGravity(Gravity.BOTTOM)
        if (dialog?.window != null) {
            dialog!!.setCancelable(true)

        }


        CoroutineScope(Dispatchers.Main).launch {
            delay(3200)
            onDismiss()
        }

    }

    fun onDismiss():Boolean{
        dialog.dismiss()
        return true

    }
}