<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.views.home.navFragments.profile.fragments.addresses.AddAddressFragment">


        <include
            android:id="@+id/toolbar"
            layout="@layout/toolbar_fragment"
            app:layout_constraintTop_toTopOf="parent"
            />
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_marginTop="17dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:text="@string/enter_num"
                    android:layout_marginTop="24.5dp"
                    android:textAppearance="@style/header_vodafoncash" />
                <EditText
                    android:id="@+id/card_no"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:hint="@string/phone_number_"
                    android:background="@drawable/et_style_with_borders"
                    android:paddingStart="15dp"
                    android:layout_marginTop="16dp"
                    android:textAppearance="@style/label_edittext_vodafoncash"
                    android:textSize="14sp" />

                <include
                    android:id="@+id/sign_btn_progress"
                    layout="@layout/btn_progress"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="90dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="55dp"
                    />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>




    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>