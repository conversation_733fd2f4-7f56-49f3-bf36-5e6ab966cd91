@echo off
title AlBarkaHyper APK Builder - FINAL VERSION
color 0A

echo.
echo ==========================================
echo    AlBarkaHyper APK Builder - FINAL
echo ==========================================
echo.
echo Fixed Issues:
echo ✓ Data Binding enabled
echo ✓ RecyclerViewSwipeDecorator version fixed
echo ✓ Build configuration updated
echo.

echo Step 1: Clean build...
call gradlew.bat clean --no-daemon
if %ERRORLEVEL% neq 0 (
    echo Warning: Clean had issues, continuing...
)

echo.
echo Step 2: Building Debug APK (for testing)...
call gradlew.bat assembleDebug --no-daemon --stacktrace
if %ERRORLEVEL% equ 0 (
    echo ✓ Debug APK build successful!
    goto :check_debug
) else (
    echo Debug build failed, trying Release...
)

echo.
echo Step 3: Building Release APK (for Google Play Store)...
call gradlew.bat assembleRelease --no-daemon --stacktrace
if %ERRORLEVEL% equ 0 (
    echo ✓ Release APK build successful!
    goto :check_release
) else (
    echo Both builds failed. Check errors above.
    pause
    exit /b 1
)

:check_debug
echo.
echo ==========================================
echo    Debug APK Results
echo ==========================================
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✓ SUCCESS: Debug APK created!
    echo   Location: %CD%\app\build\outputs\apk\debug\app-debug.apk
    for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do echo   Size: %%~zI bytes
    echo.
    echo ⚠️  This is a DEBUG version - for testing only!
    echo    Not suitable for Google Play Store.
    echo.
    echo Now trying Release build...
    call gradlew.bat assembleRelease --no-daemon --stacktrace
    if %ERRORLEVEL% equ 0 (
        goto :check_release
    ) else (
        echo Release build failed, but you have Debug APK for testing.
        goto :final_summary
    )
) else (
    echo ✗ Debug APK not found!
)
goto :final_summary

:check_release
echo.
echo ==========================================
echo    Release APK Results
echo ==========================================
if exist "app\build\outputs\apk\release\app-release.apk" (
    echo ✅ SUCCESS: Release APK created!
    echo   Location: %CD%\app\build\outputs\apk\release\app-release.apk
    for %%I in ("app\build\outputs\apk\release\app-release.apk") do echo   Size: %%~zI bytes
    echo.
    echo ✅ This is a RELEASE version - ready for Google Play Store!
    echo.
    echo Now trying to build AAB (Android App Bundle)...
    call gradlew.bat bundleRelease --no-daemon --stacktrace
    if %ERRORLEVEL% equ 0 (
        if exist "app\build\outputs\bundle\release\app-release.aab" (
            echo ✅ BONUS: AAB file also created!
            echo   Location: %CD%\app\build\outputs\bundle\release\app-release.aab
            for %%I in ("app\build\outputs\bundle\release\app-release.aab") do echo   Size: %%~zI bytes
            echo.
            echo ✅ AAB is preferred for Google Play Store!
        )
    ) else (
        echo AAB build failed, but APK is ready for Google Play Store.
    )
) else (
    echo ✗ Release APK not found!
)

:final_summary
echo.
echo ==========================================
echo    FINAL SUMMARY
echo ==========================================
echo.

set "FILES_CREATED=0"

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo 📱 DEBUG APK: CREATED
    echo    Path: %CD%\app\build\outputs\apk\debug\app-debug.apk
    echo    Use: Testing only
    set "FILES_CREATED=1"
)

if exist "app\build\outputs\apk\release\app-release.apk" (
    echo 🚀 RELEASE APK: CREATED
    echo    Path: %CD%\app\build\outputs\apk\release\app-release.apk
    echo    Use: Google Play Store (APK upload)
    set "FILES_CREATED=1"
)

if exist "app\build\outputs\bundle\release\app-release.aab" (
    echo 🎯 RELEASE AAB: CREATED
    echo    Path: %CD%\app\build\outputs\bundle\release\app-release.aab
    echo    Use: Google Play Store (PREFERRED - AAB upload)
    set "FILES_CREATED=1"
)

echo.
if "%FILES_CREATED%"=="1" (
    echo ✅ SUCCESS: APK/AAB files have been created!
    echo.
    echo 📋 Next Steps:
    echo 1. Test the APK on an Android device
    echo 2. Check google-play-assets folder for store materials
    echo 3. Upload to Google Play Console:
    echo    - Use AAB file if available (preferred)
    echo    - Use Release APK if AAB not available
    echo    - Never use Debug APK for store submission
    echo.
    echo 📁 Google Play Assets Location:
    echo    %CD%\google-play-assets\
    echo.
    echo 🎉 Your app is ready for Google Play Store!
) else (
    echo ❌ FAILED: No APK/AAB files were created!
    echo.
    echo 🔧 Troubleshooting:
    echo 1. Check the error messages above
    echo 2. Make sure you have stable internet connection
    echo 3. Try running Android Studio once to sync project
    echo 4. Check if antivirus is blocking Gradle
)

echo.
echo ==========================================
pause
