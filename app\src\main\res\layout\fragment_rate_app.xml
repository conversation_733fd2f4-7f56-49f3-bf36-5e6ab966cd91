<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/rateapp_layout"
    tools:context=".ui.views.home.navFragments.profile.fragments.terms_conditions.TermsAndConditionsFragment">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_fragment"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">
        <FrameLayout
            android:id="@+id/webview"
            android:paddingBottom="70dp"
            android:paddingHorizontal="12dp"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <WebView
                android:id="@+id/cool_webview_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </FrameLayout>
    </FrameLayout>

    <include
        android:id="@+id/server_error_layout"
        layout="@layout/include_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"

        />

    <include
        android:id="@+id/page_loading_animation"
        layout="@layout/include_page_loading_animation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>